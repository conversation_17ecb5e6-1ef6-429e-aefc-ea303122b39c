# .ebextensions/99datadog-amazon-linux-2.config

files:
    "/configure_datadog_yaml.sh":
        mode: "000700"
        owner: root
        group: root
        content: |
            #!/bin/bash
            DD_API_KEY=$(/opt/elasticbeanstalk/bin/get-config environment -k DD_API_KEY)
            DD_AGENT_VERSION=$(/opt/elasticbeanstalk/bin/get-config environment -k DD_AGENT_VERSION)
            sed 's/api_key:.*/api_key: '$DD_API_KEY'/' /etc/datadog-agent/datadog.yaml.example > /etc/datadog-agent/datadog.yaml            
            
            cat <<EOF >> /etc/datadog-agent/datadog.yaml
            site: datadoghq.eu
            logs_enabled: true
            listeners:
                - name: docker
            config_providers:
                - name: docker
                  polling: true
            logs_config:
                container_collect_all: true
            apm_config: 
                enabled: true
                apm_non_local_traffic: true            
            EOF


    "/datadog/datadog.repo":
        mode: "000644"
        owner: root
        group: root
        content: |
            [datadog]
            name = Datadog, Inc.
            baseurl = https://yum.datadoghq.com/stable/7/x86_64/
            enabled=1
            gpgcheck=1
            repo_gpgcheck=1
            gpgkey=https://keys.datadoghq.com/DATADOG_RPM_KEY_CURRENT.public
                   https://keys.datadoghq.com/DATADOG_RPM_KEY_FD4BF915.public
                   https://keys.datadoghq.com/DATADOG_RPM_KEY_E09422B3.public

    "/start_datadog.sh":
        mode: "000700"
        owner: root
        group: root
        content: |
            #!/bin/bash
            STATUS=$(sudo systemctl status datadog-agent)
            if [[ "$STATUS" == *"active (running)"* ]]
              then
                echo "Agent already running"
              else
                echo "Agent starting..."
                sudo systemctl start datadog-agent
            fi

    "/stop_datadog.sh":
        mode: "000700"
        owner: root
        group: root
        content: |
            #!/bin/bash
            STATUS=$(sudo systemctl status datadog-agent)
            if [[ "$STATUS" == *"active (running)"* ]]
              then
                echo "Agent stopping..."
                sudo systemctl stop datadog-agent
              else
                echo "Agent already stopped"
            fi


container_commands:
    02stop_datadog:
        command: "/stop_datadog.sh"
    04install_datadog:
        test: '[ -f /datadog/datadog.repo ]'
        command: 'DD_AGENT_VERSION=$(/opt/elasticbeanstalk/bin/get-config environment -k DD_AGENT_VERSION); cp /datadog/datadog.repo /etc/yum.repos.d/datadog.repo; yum -y makecache; yum -y install datadog-agent${DD_AGENT_VERSION:+-$DD_AGENT_VERSION-1}'
    05setup_datadog:
        test: '[ -x /configure_datadog_yaml.sh ]'
        # Must add dd-agent user to the docker group so it has access to the docker socket to read logs
        command: "usermod -aG docker dd-agent; /configure_datadog_yaml.sh"
    06start_datadog:
        command: "/start_datadog.sh"