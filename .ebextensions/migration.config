files:
  "/opt/elasticbeanstalk/hooks/appdeploy/post/10_post_migrate.sh":
    mode: "000755"
    owner: root
    group: root
    content: |
      #!/usr/bin/env bash
      if [ -f /tmp/leader_only ]
      then
        rm /tmp/leader_only
        docker exec $(docker ps -q | head -n 1) npx sequelize-cli db:migrate
        docker exec $(docker ps -q | head -n 1) npx sequelize-cli db:migrate:status
      fi

container_commands:
  01_migrate:
    command: "chmod +x .platform/hooks/postdeploy/00_migrate.sh || true && touch /tmp/leader_only"
    leader_only: true
