{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "testSingleFile",
      "type": "shell",
      "command": "yarn",
      "args": [
        "test",
        "--collectCoverage", "false",
        "${relativeFile}"
      ],
      "group": {
        "kind": "test",
        "isDefault": true
      }
    }
  ]
}
