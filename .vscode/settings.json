{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.format.enable": true, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features", "editor.codeActionsOnSave": ["source.organizeImports", "source.fixAll", "source.eslint.fixAll"]}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features", "editor.codeActionsOnSave": ["source.fixAll", "source.eslint.fixAll"]}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": ["source.fixAll"]}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": ["source.fixAll"]}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features", "editor.codeActionsOnSave": ["source.fixAll"]}, "editor.formatOnSave": true, "typescript.preferences.importModuleSpecifier": "relative", "javascript.preferences.importModuleSpecifier": "relative", "eslint.options": {"extensions": [".ts", ".html"]}, "eslint.validate": ["javascript", "typescript", "json", "html"], "cSpell.words": ["BANKID", "Byggstart", "Eiendomsverdi", "Ekstra", "EXPONOVA", "Fortum", "<PERSON><PERSON><PERSON>", "Idfy", "LIPSCORE", "norges", "pades", "PREMARKET", "sendgrid", "STOREBRAND", "TRYGG", "Verisure", "<PERSON><PERSON><PERSON>", "vitec"], "yaml.schemas": {"https://json.schemastore.org/github-workflow.json": "/.github/workflows/*.yml"}}