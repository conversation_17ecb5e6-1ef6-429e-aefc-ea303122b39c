version: '3.7'

services:
  backend:
    container_name: nordvik_backend
    env_file:
      - ./../.env
    build: ./../
    ports:
      - 3000:3000
    networks:
      - nordvik_network
    # environment:
    # PG_CONNECTION_STRING: **********************************/testdb

  pg:
    container_name: nordvik_pg
    build: ./postgres
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: user
      POSTGRES_DB: testdb
      POSTGRES_PASSWORD: password
    networks:
      - nordvik_network
    restart: unless-stopped

  pgadmin:
    container_name: nordvik_pgadmin
    image: dpage/pgadmin4
    environment:
      PGADMIN_DEFAULT_EMAIL: pgadmin@nordvik
      PGADMIN_DEFAULT_PASSWORD: nordvik
    ports:
      - 5050:80
    networks:
      - nordvik_network
    restart: unless-stopped

  mongo:
    image: mongo
    restart: always
    ports:
      - 27017:27017
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example

  mongo-express:
    image: mongo-express
    restart: always
    ports:
      - 5051:8081
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: example

networks:
  nordvik_network:
    driver: bridge
