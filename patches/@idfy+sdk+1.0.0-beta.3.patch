diff --git a/node_modules/@idfy/sdk/dist/services/IdfyBaseService.js b/node_modules/@idfy/sdk/dist/services/IdfyBaseService.js
index 226f53b..6c3e6c9 100644
--- a/node_modules/@idfy/sdk/dist/services/IdfyBaseService.js
+++ b/node_modules/@idfy/sdk/dist/services/IdfyBaseService.js
@@ -78,6 +78,8 @@ class IdfyBaseService {
             this.oauthToken = res;
             const now = new Date();
             this.oauthToken.expiry = now.setSeconds(now.getSeconds() + res.expires_in);
+        }).catch(() => {
+            console.log('Invalid idfy configuration!');
         });
         return promise;
     }
