import convict from 'convict';
import * as dotenv from 'dotenv';

dotenv.config({ path: 'e2e-api-tests/.env' });

const convictConfig = convict({
  backendApi: {
    doc: 'The backend API to test on',
    format: String,
    default: '',
    env: 'E2E_API_URL',
  },
  mongo: {
    connection: {
      doc: 'Connection string for mongodb.',
      format: String,
      default: 'mongodb://127.0.0.1:27017',
      env: 'E2E_MONGO_CONNECTION',
    },
    db: {
      doc: 'Connection db for mongodb',
      format: String,
      default: 'test',
      env: 'E2E_MONGO_DB',
    },
  },
  pg: {
    username: {
      doc: 'PG username',
      format: String,
      default: '',
      env: 'E2E_POSTGRES_USERNAME',
    },
    password: {
      doc: 'PG pass',
      format: String,
      default: '',
      env: 'E2E_POSTGRES_PASSWORD',
    },
    database: {
      doc: 'PG db',
      format: String,
      default: '',
      env: 'E2E_POSTGRES_DB',
    },
    host: {
      doc: 'PG host',
      format: String,
      default: '',
      env: 'E2E_POSTGRES_HOST',
    },
  },
});

convictConfig.validate({ allowed: 'strict' });

export const e2eConfig = convictConfig.getProperties();
