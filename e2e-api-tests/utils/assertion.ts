export function deepStrictEqualProps(obj1: Record<any, any> | null, obj2: Record<any, any> | null, ignoreProps: string[] = []) {
  if (obj1 === obj2) {
    return true;
  }

  if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null) {
    return false;
  }

  const keys1 = Object.keys(obj1).filter(key => !ignoreProps.includes(key));
  const keys2 = Object.keys(obj2).filter(key => !ignoreProps.includes(key));

  if (keys1.length !== keys2.length) {
    throw new Error(`Prop length mismatch: \n ${keys1.sort()} \n ${keys2.sort()}`);
  }

  for (const key of keys1) {
    if (!keys2.includes(key) || !deepStrictEqualProps(obj1[key], obj2[key])) {
      throw new Error(`Prop mismatch '${key}': got: ${obj1[key]}, expected: ${obj2[key]}`);
    }
  }

  return true;
}