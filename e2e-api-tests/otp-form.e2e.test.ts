import { deepStrictEqual, strictEqual } from 'assert';
import axios, { AxiosResponse } from 'axios';
import { sub } from 'date-fns';
import { omit, orderBy } from 'lodash';
import { MongoClient } from 'mongodb';
import { QueryTypes } from 'sequelize';
import { ElectricityProvider, OtpResponse } from '../src/domain/otp/otp';
import { OtpMeterResponse, OtpMeterType } from '../src/domain/otp/otp-meter/otp-meter';
import { OtpParticipantResponse, OtpParticipantRole } from '../src/domain/otp/otp-participant/otp-participant';
import { ResBody as CreateResponse } from '../src/endpoints/otp/create-otp.endpoint';
import { ResBody as CreateOtpDeocumentResponse } from '../src/endpoints/otp/document/create-otp-document.endpoint';
import { ResBody as GetSignProcessResponse } from '../src/endpoints/otp/document/get-otp-document.endpoint';
import { ResBody as GetResponse } from '../src/endpoints/otp/get-otp.endpoint';
import { ResBody as GetMeterResponse } from '../src/endpoints/otp/meter/create-otp-meter.endpoint';
import { ResBody as DeleteMeterResponse } from '../src/endpoints/otp/meter/delete-otp-meter.endpoint';
import { ResBody as CreateParticipantResponse } from '../src/endpoints/otp/participant/create-otp-participant.endpoint';
import { ResBody as DeleteParticipantResponse } from '../src/endpoints/otp/participant/delete-otp-participant.endpoint';
import { ResBody as UpdateParticipantResponse } from '../src/endpoints/otp/participant/update-otp-participant.endpoint';
import { ResBody as UpdateResponse } from '../src/endpoints/otp/update-otp.endpoint';
import { mockBuyerOTP, mockEstateOTP, mockSellerOTP } from '../src/use-cases/otp/mock-objects';
import { e2eConfig } from './utils/config';
import { sequelizeConnection } from './utils/postgres-connection';

const mapParticipants = (p: OtpParticipantResponse) => {
  const withoutBaseProps = omit(p, 'createdAt', 'id', 'updatedAt');

  if (withoutBaseProps.image) {
    return { ...withoutBaseProps, image: omit(withoutBaseProps.image, ['id', 'type']) };
  }
  return withoutBaseProps;
};

const mapMeters = (m: OtpMeterResponse) => {
  const withoutBaseProps = omit(m, ['id', 'createdAt', 'updatedAt']);

  if (withoutBaseProps.image) {
    return { ...withoutBaseProps, image: omit(withoutBaseProps.image, ['id']) };
  }
  return withoutBaseProps;
};

const getDynamicLessOTP = (data: GetResponse | OtpResponse) => ({
  ...omit(data, ['id']),
  participants: orderBy(data.participants.map(mapParticipants), ['email'], 'desc'),
  waterMeters: data.waterMeters.map(mapMeters),
  electricityMeters: data.electricityMeters.map(mapMeters),
});

const getDynamicLessParticipant = (data: CreateParticipantResponse) => ({
  ...omit(data, ['id', 'createdAt', 'updatedAt']),
  image: omit(data.image, ['id']),
});

const getDynamicLessMeter = (data: GetMeterResponse) => ({
  ...omit(data, ['id', 'createdAt', 'updatedAt']),
  image: omit(data.image, ['id']),
});

const baseOTPForm = {
  estateVitecId: mockEstateOTP.estateId,
  moneyTransferred: null,
  sellerNewAddress: null,
  sellerNewPostcode: null,
  sellerNewCity: null,
  propertyCleaned: null,
  propertyCleanedComment: null,
  sellerPaidCosts: null,
  sellerPaidCostsComment: null,
  handedOverAllKeys: null,
  handedOverAllKeysComment: null,
  numberOfKeys: null,
  smokeAlarmAvailable: null,
  fireExtinguisherAvailable: null,
  fireSafetyComment: null,
  waterInfoProvided: null,
  waterMeters: [],
  electricityProviderSelected: null,
  electricityMeters: [],
  electricityInfoProvided: null,
  idfyDocumentId: null,
  finalSettlement: null,
  finalSettlementComment: null,
  finalSettlementWithholding: null,
  finalSettlementWithholdAmount: null,
  finalSettlementWithholdReason: null,
  finalSettlementWithholdingComment: null,
  handoverComment: null,
  // billing data
  address: 'Huk Aveny, estate.address.apartmentNumber',
  postCode: '0287',
  city: 'Oslo',
  billingBuyerContactId: null,
  participants: [
    {
      name: `${mockSellerOTP.firstName} ${mockSellerOTP.lastName}`,
      email: mockSellerOTP.email,
      phoneNumber: mockSellerOTP.mobilePhone,
      isPowerOfAttorney: false,
      belongsTo: 'SELLER',
    },
    {
      name: `${mockBuyerOTP.firstName} ${mockBuyerOTP.lastName}`,
      email: mockBuyerOTP.email,
      phoneNumber: mockBuyerOTP.mobilePhone,
      isPowerOfAttorney: false,
      belongsTo: 'BUYER',
    },
  ],
  signingStarted: null,
  estateBaseType: 4,
};

const updateOTPBody = {
  moneyTransferred: false,
  sellerNewAddress: 'sellerNewAddress',
  sellerNewPostcode: 'sellerNewPostcode',
  sellerNewCity: 'sellerNewCity',
  propertyCleaned: false,
  propertyCleanedComment: 'propertyCleanedComment',
  sellerPaidCosts: true,
  sellerPaidCostsComment: 'sellerPaidCostsComment',
  handedOverAllKeys: false,
  handedOverAllKeysComment: 'handedOverAllKeysComment',
  numberOfKeys: '2',
  smokeAlarmAvailable: true,
  fireExtinguisherAvailable: true,
  fireSafetyComment: '',
  waterInfoProvided: true,
  electricityInfoProvided: true,
  finalSettlement: true,
  finalSettlementComment: 'finalSettlementComment',
  finalSettlementWithholding: true,
  finalSettlementWithholdAmount: 'finalSettlementWithholdAmount',
  finalSettlementWithholdReason: 'finalSettlementWithholdReason',
  finalSettlementWithholdingComment: 'finalSettlementWithholdingComment',
  electricityProviderSelected: ElectricityProvider.HAFSLUND_FORTUM,
  handoverComment: 'handoverComment',
  // billing data
  address: 'address',
  postCode: 'postCode',
  city: 'city',
  billingBuyerContactId: 'billingBuyerContactId',
};

const client = new MongoClient(e2eConfig.mongo.connection);

async function setupMongoDBWithEstateBuyerSeller(): Promise<void> {
  await client.connect();
  const db = client.db(e2eConfig.mongo.db);
  const sellerCollection = db.collection('sellers');
  const buyerCollection = db.collection('buyers');
  const estateCollection = db.collection('estates');

  try {
    await buyerCollection.insertOne(mockBuyerOTP);
    await sellerCollection.insertOne(mockSellerOTP);
    await estateCollection.insertOne(mockEstateOTP);
  } finally {
    await client.close();
  }
}
async function cleanupMongoDB(): Promise<void> {
  console.log('Cleaning up mongoDB...');
  await client.connect();
  const db = client.db(e2eConfig.mongo.db);
  const sellerCollection = db.collection('sellers');
  const buyerCollection = db.collection('buyers');
  const estateCollection = db.collection('estates');

  try {
    await buyerCollection.deleteMany({ contactId: mockBuyerOTP.contactId });
    await sellerCollection.deleteMany({ contactId: mockSellerOTP.contactId });
    await estateCollection.deleteMany({ estateId: mockEstateOTP.estateId });
  } finally {
    await client.close();
    console.log('Mongo cleanup done.');
  }
}
async function cleanupPostgres(): Promise<void> {
  console.log('Cleaning up Postgres...');

  const sequelize = sequelizeConnection();
  try {
    const resOtps = await sequelize.query(
      `SELECT id, "fileId" FROM "OvertakeProtocol" WHERE "estateVitecId"='${mockEstateOTP.estateId}'`,
      { type: QueryTypes.SELECT },
    );
    for (const otp of resOtps) {
      const overtakeProtocolId = (otp as { id: string })?.id;
      const overtakeProtocolFileId = (otp as { fileId: string })?.fileId;

      if (overtakeProtocolId) {
        const transaction = await sequelize.transaction();

        const resMeterFileIds = await sequelize.query<{ fileId: string }>(
          `SELECT "fileId" FROM "OvertakeProtocolMeter" WHERE "overtakeProtocolId"='${overtakeProtocolId}'`,
          { type: QueryTypes.SELECT },
        );
        const meterFileIds = resMeterFileIds
          .filter((m) => !!m.fileId)
          .map((m) => `'${m.fileId}'`)
          .join(',');

        const resParticipantFileIds = await sequelize.query<{ fileId: string }>(
          `SELECT "fileId" FROM "OvertakeProtocolParticipant" WHERE "overtakeProtocolId"='${overtakeProtocolId}'`,
          { type: QueryTypes.SELECT },
        );
        const participantFileIds = resParticipantFileIds
          .filter((p) => !!p.fileId)
          .map((p) => `'${p.fileId}'`)
          .join(',');

        if (overtakeProtocolFileId) {
          await sequelize.query(`DELETE FROM "File" WHERE "id"='${overtakeProtocolFileId}'`, {
            transaction,
          });
        }

        if (meterFileIds.length) {
          await sequelize.query(`DELETE FROM "File" WHERE "id" in (${meterFileIds})`, {
            transaction,
          });
        }

        if (participantFileIds.length) {
          await sequelize.query(`DELETE FROM "File" WHERE "id" in (${participantFileIds})`, {
            transaction,
          });
        }

        await sequelize.query(
          `DELETE FROM "OvertakeProtocolMeter" WHERE "overtakeProtocolId"='${overtakeProtocolId}'`,
          {
            transaction,
          },
        );

        await sequelize.query(
          `DELETE FROM "OvertakeProtocolParticipant" WHERE "overtakeProtocolId"='${overtakeProtocolId}'`,
          {
            transaction,
          },
        );

        await sequelize.query(`DELETE FROM "OvertakeProtocol" WHERE "estateVitecId"='${mockEstateOTP.estateId}'`, {
          transaction,
        });

        await transaction.commit();
      }
    }
  } catch (e) {
    console.error(e);
  } finally {
    await sequelize.close();
    console.log('Postgres cleanup done.');
  }
}

describe('Overtake Protocol Form API tests', () => {
  let baseParticipantId: string = '';
  let createdParticipantId: string = '';
  let waterMeterId: string = '';
  let waterMeterFileId: string = '';
  let electricityMeterId: string = '';
  let electricityMeterFileId: string = '';

  before(async () => {
    await cleanupMongoDB();
    await cleanupPostgres();
    await setupMongoDBWithEstateBuyerSeller();
  });

  after(async () => {
    await cleanupMongoDB();
    await cleanupPostgres();
  });

  it('should createOTPForm once with participants, throw error second time', async () => {
    try {
      const { data, status } = await axios.post<CreateResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
        {},
        { headers: { 'Content-Type': 'application/json' } },
      );
      baseParticipantId = data.participants[0].id;
      baseParticipantId;
      strictEqual(status, 200);
      deepStrictEqual(getDynamicLessOTP(data), baseOTPForm);

      try {
        await axios.post<null, AxiosResponse<CreateResponse>>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
          {},
          { headers: { 'Content-Type': 'application/json' } },
        );
      } catch (e) {
        strictEqual((e as any).response.status, 400);
        deepStrictEqual((e as any).response.data, { detail: 'ct.otp.errors.alreadyExists' });
      }
    } catch (e) {
      console.log(e);
      throw e;
    }
  });

  it('should get the freshly created OTPForm every time', async () => {
    try {
      const { data, status } = await axios.get<GetResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 200);
      deepStrictEqual(getDynamicLessOTP(data), baseOTPForm);

      const { data: data2, status: status2 } = await axios.get<GetResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status2, 200);
      deepStrictEqual(getDynamicLessOTP(data2), baseOTPForm);
    } catch (e) {
      console.log(e);
      throw e;
    }
  });

  it('should update OTP', async () => {
    try {
      const { data, status } = await axios.patch<UpdateResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
        updateOTPBody,
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 200);
      deepStrictEqual(getDynamicLessOTP(data), { ...omit(baseOTPForm, 'estateBaseType'), ...updateOTPBody });
    } catch (e) {
      console.log(e);
      throw e;
    }
  });

  describe('participant', () => {
    it('should create a participant for an OTP next to the existing ones', async () => {
      try {
        const { data, status } = await axios.post<CreateParticipantResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/participant`,
          {
            name: 'buyer created during testing',
            email: 'a@b.c',
            phoneNumber: '123123123',
            isPowerOfAttorney: false,
            belongsTo: OtpParticipantRole.BUYER,
            image: { content: 'imageContent', fileName: 'profilePic', mimeType: 'image/png' },
          },
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(status, 201);
        createdParticipantId = data.id;
        deepStrictEqual(getDynamicLessParticipant(data), {
          name: 'buyer created during testing',
          email: 'a@b.c',
          phoneNumber: '123123123',
          isPowerOfAttorney: false,
          belongsTo: 'BUYER',
          image: {
            type: 'participantImage',
            fileName: 'profilePic',
            mimeType: 'image/png',
          },
        });

        const { data: dataGet, status: statusGet } = await axios.get<GetResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(statusGet, 200);
        strictEqual(dataGet.participants.length, 3);
        deepStrictEqual(getDynamicLessOTP(dataGet), {
          ...baseOTPForm,
          ...updateOTPBody,
          participants: [
            ...baseOTPForm.participants,
            {
              name: 'buyer created during testing',
              email: 'a@b.c',
              phoneNumber: '123123123',
              isPowerOfAttorney: false,
              belongsTo: OtpParticipantRole.BUYER,
              image: { fileName: 'profilePic', mimeType: 'image/png' },
            },
          ],
        });
      } catch (e) {
        console.log(e);
        throw e;
      }
    });

    it('should update a participant for an OTP leave the existing ones intact', async () => {
      try {
        const { data, status } = await axios.patch<UpdateParticipantResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/participant/${createdParticipantId}`,
          {
            name: 'buyer updated during testing',
          },
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(status, 201);
        deepStrictEqual(getDynamicLessParticipant(data), {
          name: 'buyer updated during testing',
          email: 'a@b.c',
          phoneNumber: '123123123',
          isPowerOfAttorney: false,
          belongsTo: 'BUYER',
          image: {},
        });

        const { data: dataGet, status: statusGet } = await axios.get<GetResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(statusGet, 200);
        strictEqual(dataGet.participants.length, 3);
        deepStrictEqual(getDynamicLessOTP(dataGet), {
          ...baseOTPForm,
          ...updateOTPBody,
          participants: [
            ...baseOTPForm.participants,
            {
              name: 'buyer updated during testing',
              email: 'a@b.c',
              phoneNumber: '123123123',
              isPowerOfAttorney: false,
              belongsTo: OtpParticipantRole.BUYER,
              image: { fileName: 'profilePic', mimeType: 'image/png' },
            },
          ],
        });
      } catch (e) {
        console.log(e);
        throw e;
      }
    });

    it('should delete a participant for an OTP, leave the existing ones', async () => {
      try {
        const { data, status } = await axios.delete<DeleteParticipantResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/participant/${createdParticipantId}`,
        );
        strictEqual(status, 200);
        deepStrictEqual(data, {});

        const { data: dataGet, status: statusGet } = await axios.get<GetResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(statusGet, 200);
        strictEqual(dataGet.participants.length, 2);
        deepStrictEqual(getDynamicLessOTP(dataGet), { ...baseOTPForm, estateBaseType: 4, ...updateOTPBody });
      } catch (e) {
        console.log(e);
        throw e;
      }
    });
  });

  describe('meter', () => {
    it('should create an electricity and a water meter for an OTP', async () => {
      try {
        const { data, status } = await axios.post<GetMeterResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter`,
          {
            meterNumber: 'eMeterNumber',
            meterReading: '123',
            type: OtpMeterType.ELECTRICITY,
            meterName: 'eMeterName',
            image: { content: 'eMeterContent', fileName: 'eMeterImg', mimeType: 'image/png' },
          },
          { headers: { 'Content-Type': 'application/json' } },
        );
        electricityMeterId = data.id;
        electricityMeterFileId = data.image?.id || '';
        strictEqual(status, 201);
        deepStrictEqual(getDynamicLessMeter(data), {
          meterNumber: 'eMeterNumber',
          meterReading: '123',
          type: OtpMeterType.ELECTRICITY,
          meterName: 'eMeterName',
          image: { fileName: 'eMeterImg', mimeType: 'image/png', type: 'meterImage' },
        });

        const { data: dataW, status: statusW } = await axios.post<GetMeterResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter`,
          {
            meterNumber: 'wMeterNumber',
            meterReading: '123',
            type: OtpMeterType.WATER,
            meterName: 'wMeterName',
            image: { content: 'wMeterContent', fileName: 'wMeterImg', mimeType: 'image/png' },
          },
          { headers: { 'Content-Type': 'application/json' } },
        );
        waterMeterId = dataW.id;
        waterMeterFileId = dataW.image?.id || '';
        strictEqual(statusW, 201);
        deepStrictEqual(getDynamicLessMeter(dataW), {
          meterNumber: 'wMeterNumber',
          meterReading: '123',
          type: OtpMeterType.WATER,
          meterName: 'wMeterName',
          image: { fileName: 'wMeterImg', mimeType: 'image/png', type: 'meterImage' },
        });

        const { data: dataGet, status: statusGet } = await axios.get<GetResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(statusGet, 200);
        strictEqual(dataGet.waterMeters.length, 1);
        strictEqual(dataGet.electricityMeters.length, 1);
        deepStrictEqual(getDynamicLessOTP(dataGet), {
          ...baseOTPForm,
          ...updateOTPBody,
          waterMeters: [
            {
              meterNumber: 'wMeterNumber',
              meterReading: '123',
              type: OtpMeterType.WATER,
              meterName: 'wMeterName',
              image: { fileName: 'wMeterImg', mimeType: 'image/png', type: 'meterImage' },
            },
          ],
          electricityMeters: [
            {
              meterNumber: 'eMeterNumber',
              meterReading: '123',
              type: OtpMeterType.ELECTRICITY,
              meterName: 'eMeterName',
              image: { fileName: 'eMeterImg', mimeType: 'image/png', type: 'meterImage' },
            },
          ],
        });
      } catch (e) {
        console.log(e);
        throw e;
      }
    });

    it('should update an electricity and a water meter for an OTP', async () => {
      try {
        const { data, status } = await axios.patch<GetMeterResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter/${electricityMeterId}`,
          {
            meterReading: '1234',
          },
          { headers: { 'Content-Type': 'application/json' } },
        );
        electricityMeterFileId = data.image?.id || '';
        strictEqual(status, 201);
        deepStrictEqual(getDynamicLessMeter(data), {
          meterNumber: 'eMeterNumber',
          meterReading: '1234',
          type: OtpMeterType.ELECTRICITY,
          meterName: 'eMeterName',
          image: {},
        });

        const { data: dataW, status: statusW } = await axios.patch<GetMeterResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter/${waterMeterId}`,
          {
            meterNumber: 'wMeterNumber2',
            image: { content: 'wMeterContentUpdated', fileName: 'wMeterImg2', mimeType: 'image/png' },
          },
          { headers: { 'Content-Type': 'application/json' } },
        );
        waterMeterFileId = dataW.image?.id || '';
        strictEqual(statusW, 201);
        deepStrictEqual(getDynamicLessMeter(dataW), {
          meterNumber: 'wMeterNumber2',
          meterReading: '123',
          type: OtpMeterType.WATER,
          meterName: 'wMeterName',
          image: { fileName: 'wMeterImg2', mimeType: 'image/png', type: 'meterImage' },
        });

        const { data: dataGet, status: statusGet } = await axios.get<GetResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(statusGet, 200);
        strictEqual(dataGet.waterMeters.length, 1);
        strictEqual(dataGet.electricityMeters.length, 1);
        deepStrictEqual(getDynamicLessOTP(dataGet), {
          ...baseOTPForm,
          ...updateOTPBody,
          waterMeters: [
            {
              meterNumber: 'wMeterNumber2',
              meterReading: '123',
              type: OtpMeterType.WATER,
              meterName: 'wMeterName',
              image: { fileName: 'wMeterImg2', mimeType: 'image/png', type: 'meterImage' },
            },
          ],
          electricityMeters: [
            {
              meterNumber: 'eMeterNumber',
              meterReading: '1234',
              type: OtpMeterType.ELECTRICITY,
              meterName: 'eMeterName',
              image: { fileName: 'eMeterImg', mimeType: 'image/png', type: 'meterImage' },
            },
          ],
        });
      } catch (e) {
        console.log(e);
        throw e;
      }
    });

    it('should get image of an OTP electricity and water meters', async () => {
      try {
        const { data, status } = await axios.get<string>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter/${waterMeterId}/${waterMeterFileId}/wMeterImg2`,
          {
            responseType: 'arraybuffer',
          },
        );
        strictEqual(status, 200);
        strictEqual(Buffer.from(data, 'binary').toString('base64'), 'wMeterContentUpdated');

        try {
          await axios.get<string>(
            `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter/${electricityMeterId}/${electricityMeterFileId}/eMeterImg`,
            {
              responseType: 'arraybuffer',
            },
          );
        } catch (e) {
          strictEqual((e as any).response.status, 400); // imageId was empty after no image was provided on update electricity
        }
      } catch (e) {
        console.log(e);
        throw e;
      }
    });

    it('should delete a water and an electricity meter for an OTP', async () => {
      try {
        const { data, status } = await axios.delete<DeleteMeterResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter/${electricityMeterId}`,
        );
        strictEqual(status, 200);
        deepStrictEqual(data, {});

        const { data: dataW, status: statusW } = await axios.delete<DeleteMeterResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/meter/${waterMeterId}`,
        );
        strictEqual(statusW, 200);
        deepStrictEqual(dataW, {});

        const { data: dataGet, status: statusGet } = await axios.get<GetResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(statusGet, 200);
        deepStrictEqual(getDynamicLessOTP(dataGet), { ...baseOTPForm, ...updateOTPBody });
      } catch (e) {
        console.log(e);
        throw e;
      }
    });
  });

  describe('document', () => {
    it('should create a document for an OTP and save it to DB', async (done) => {
      const sequelize = sequelizeConnection();
      try {
        const { data, status } = await axios.post<CreateOtpDeocumentResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/document`,
          { inksign: true, signers: ['signer1', 'signer2'] },
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(status, 201);
        deepStrictEqual(data, [
          {
            inkSign: true,
            participantPgId: 'signer1',
            signUrl: 'mockIdfyDocumentSignUrl0',
          },
          {
            inkSign: true,
            participantPgId: 'signer2',
            signUrl: 'mockIdfyDocumentSignUrl1',
          },
        ]);

        const otpRes = await sequelize.query<{ signingStarted: Date, idfyDocumentId: string }>(
          `SELECT "signingStarted", "idfyDocumentId" FROM "OvertakeProtocol" WHERE "estateVitecId"='${mockEstateOTP.estateId}'`,
          { type: QueryTypes.SELECT },
        );

        const signingStarted = otpRes[0].signingStarted;
        const idfyDocumentId = otpRes[0].idfyDocumentId;

        const signingAlreadyStartedInTheLast5Minutes =
          sub(new Date(), { minutes: 5 }).getTime() < signingStarted.getTime() &&
          signingStarted.getTime() < new Date().getTime();
        if (!signingAlreadyStartedInTheLast5Minutes) {
          throw new Error('Signing not started correctly!');
        }
        strictEqual(idfyDocumentId, 'mockIdfyDocumentIdOTP');
        await sequelize.close();
        done()
      } catch (e) {
        console.log(e);
        await sequelize.close();
        throw e;
      }
    });

    it('should get otp document signing urls meaning that signing is still needed', async () => {
      try {
        const { data, status } = await axios.get<GetSignProcessResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/otp/document-sign-status`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(status, 201);
        deepStrictEqual(data, [
          {
            participantPgId: 'signer1',
            signUrl: null,
            inkSign: false,
          },
          {
            participantPgId: 'signer2',
            signUrl: 'mockIdfyDocumentSignUrl1',
            inkSign: false
          },
        ]);
      } catch (e) {
        console.log(e);
        throw e;
      }
    });

    it('should send sms about signing', async () => {
      try {
        const { data, status } = await axios.post<GetSignProcessResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/sign-sms/${baseParticipantId}`,
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(status, 201);
        deepStrictEqual(data, {});
      } catch (e) {
        console.log(e);
        throw e;
      }
    });
  });
  describe('finalize', () => {
    it('should finalize an otp', async () => {
      const sequelize = sequelizeConnection();
      try {
        const { data, status } = await axios.post<GetSignProcessResponse>(
          `${e2eConfig.backendApi}/estate/${mockEstateOTP.estateId}/finish-otp`,
          {},
          { headers: { 'Content-Type': 'application/json' } },
        );
        strictEqual(status, 201);
        deepStrictEqual(data, {});

        const otpRes = await sequelize.query<{ signingFinished: Date }>(
          `SELECT "signingFinished" FROM "OvertakeProtocol" WHERE "estateVitecId"='${mockEstateOTP.estateId}'`,
          { type: QueryTypes.SELECT },
        );
        const signingFinished = new Date(otpRes[0].signingFinished);
        const signingFinishedInTheLastMinute =
          sub(new Date(), { minutes: 1 }).getTime() < signingFinished.getTime() &&
          signingFinished.getTime() < new Date().getTime();
        if (!signingFinishedInTheLastMinute) {
          throw new Error('Signing not finished correctly!');
        }
        await sequelize.close();
      } catch (e) {
        console.log(e);
        await sequelize.close();
        throw e;
      }
    });
  });
});
