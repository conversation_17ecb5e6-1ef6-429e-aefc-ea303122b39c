import assert, { deepStrictEqual, strictEqual } from 'assert';
import axios from 'axios';
import { sub } from 'date-fns';
import { MongoClient } from 'mongodb';
import { PEPType } from '../src/domain/politically-exposed-person-form/politically-exposed-person-form';
import { ResBody as CreateResponse } from '../src/endpoints/pep-form/create-pep-form.endpoint';
import { ResBody as SignStatusResponse } from '../src/endpoints/pep-form/get-pep-document-sign-status.endpoint';
import { ResBody as GetParticipantsResponse } from '../src/endpoints/pep-form/get-pep-form-participants.endpoint';
import { ResBody as GetResponse } from '../src/endpoints/pep-form/get-pep-form.endpoint';
import { ResBody as PatchResponse } from '../src/endpoints/pep-form/update-pep-form.endpoint';
import {
  mockBuyerPep, mockEstatePep, mockSellerPep
} from '../src/use-cases/pep-form/mock-objects';
import { deepStrictEqualProps } from './utils/assertion';
import { e2eConfig } from './utils/config';
import { sequelizeConnection } from './utils/postgres-connection';

const basePepForm = {

  estateVitecId: 'mockEstateIdE2E',

  estateAddress: '35 Huk Aveny, 0287 Oslo',
  estateAssignmentNumber: 'mockEstateAssignmentNumberE2E',
  estateAssignmentTypeGroup: null,
  isNotificationSent: null,
  type: PEPType.SELLER,

  // Sale Information
  saleInfoTransactionReason: null,
  saleInfoInMyNameOrProxy: null,
  saleInfoUseOrInvestment: null,

  // Property Information
  propertyInfoOwnedForTime: null,
  propertyInfoIsRenovatedByOwner: null,
  propertyInfoRenovator: null,
  propertyInfoRenovationFinance: null,
  propertyInfoRenovationDocumentation: null,

  // Equity Information
  equityInfoPercent: null,
  equityInfoSource: null,

  // Signing
  idfyDocumentId: null,
  signingStarted: null,
  signingFinished: null,

  // Financing
  financing: {
    newHome: null,
    doYouWantFinancing: null,
    participantsSelected: null
  },

  valuationDescriptionOfOther: null,
  valuationPurpose: null,
  valuationSincePurchase: null,
  valuationYear: null,
};

const basePepParticipant = {
  id: 'to be replaced',
  contactPersonVitecId: mockSellerPep.contactId,
  name: `${mockSellerPep.firstName} ${mockSellerPep.lastName}`,
  email: mockSellerPep.email,
  phoneNumber: null,
  // Page 1 form related
  profession: null,
  employer: null,
  selectedCountry: null,
  typedCountry: null,
  estateCount: null,
  citizenshipDescription: [],
  // Page 2 (Own) form related
  ownPepType: null,
  ownDescriptionsOfSelected: {
    countries: '',
    dateWhenPracticed: null,
    description: '',
    isLessThanAYearAgo: false,
  },
  ownComment: null,
  // Page 3 (Employer) form related
  employerPepType: null,
  employerDescriptionsOfSelected: {
    countries: '',
    dateWhenPracticed: null,
    description: '',
    isLessThanAYearAgo: false,
    name: '',
    relationship: '',
  },
  employerComment: null,
  // Page 4 (Family) form related
  familyPepType: null,
  familyDescriptionsOfSelected: {
    countries: '',
    dateWhenPracticed: null,
    description: '',
    isLessThanAYearAgo: false,
    name: '',
    relationship: '',
  },
  familyComment: null,
  settlementBuyerParticipantId: null,
}

const client = new MongoClient(e2eConfig.mongo.connection);

async function setupMongoDBWithEstateBuyerSeller(): Promise<void> {
  await client.connect();
  const db = client.db(e2eConfig.mongo.db);
  const sellerCollection = db.collection('sellers');
  const buyerCollection = db.collection('buyers');
  const estateCollection = db.collection('estates');

  try {
    await buyerCollection.insertOne(mockBuyerPep);
    await sellerCollection.insertOne(mockSellerPep);
    await estateCollection.insertOne(mockEstatePep);
  } finally {
    await client.close();
  }
}
async function cleanupMongoDB(): Promise<void> {
  await client.connect();
  const db = client.db(e2eConfig.mongo.db);
  const sellerCollection = db.collection('sellers');
  const buyerCollection = db.collection('buyers');
  const estateCollection = db.collection('estates');

  try {
    await buyerCollection.deleteMany({ contactId: mockBuyerPep.contactId });
    await sellerCollection.deleteMany({ contactId: mockSellerPep.contactId });
    await estateCollection.deleteMany({ estateId: mockEstatePep.estateId });
  } finally {
    await client.close();
    console.log('Mongo cleanup done.');
  }
}
async function cleanupPostgres(): Promise<void> {
  const sequelize = sequelizeConnection();
  const transaction = await sequelize.transaction();
  try {
    await sequelize.query(
      `DELETE FROM "PEPForm" WHERE "estateVitecId"='${mockEstatePep.estateId}'`, { transaction }
    );
    await transaction.commit();
  } catch (e) {
    await transaction.rollback();
    console.error(e);
  } finally {
    await sequelize.close();
    console.log('Postgres cleanup done.');
  }
}

describe('Politically Exposed Person Form API tests', () => {
  before(async () => {
    await cleanupMongoDB();
    await cleanupPostgres();
    await setupMongoDBWithEstateBuyerSeller();
  });

  after(async () => {
    await cleanupMongoDB();
    await cleanupPostgres();
  });

  it('should createPepForm for seller', async () => {
    try {
      const { status } = await axios.post<CreateResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep`,
        {},
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 201);
    } catch (e) {
      console.log(e);
      throw e;
    }
  });

  it('should getPepForm for seller after creation', async () => {
    try {
      const { data, status } = await axios.get<GetResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep`,
      );
      strictEqual(status, 200);
      assert.ok(deepStrictEqualProps(data, {
        ...basePepForm,
      }, ['createdAt', 'updatedAt', 'id']));
    } catch (e) {
      console.error(e);
      throw e;
    }
  });

  it('should patchPepForm for seller after creation', async () => {
    try {
      const { status } = await axios.patch<PatchResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep`,
        { saleInfoTransactionReason: 'saleInfoTransactionReason', equityInfoPercent: '100' },
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 200);
    } catch (e) {
      console.error(e);
      throw e;
    }
  });

  it('should getPepForm for seller after change, check updates', async () => {
    try {
      const { data, status } = await axios.get<GetResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep`,
      );
      strictEqual(status, 200);
      assert.ok(deepStrictEqualProps(data, {
        ...basePepForm,
        saleInfoTransactionReason: 'saleInfoTransactionReason',
        equityInfoPercent: '100',
      }, ['createdAt', 'updatedAt', 'id']));
    } catch (e) {
      console.error(e);
      throw e;
    }
  });

  it('should get participant for seller after creation', async () => {
    try {
      const { data, status } = await axios.get<GetParticipantsResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep-seller-participants`,
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 200);
      strictEqual(data!.length, 1);
      basePepParticipant.id = data![0].id;
      assert.ok(deepStrictEqualProps(data![0], {
        ...basePepParticipant
      }, ['createdAt', 'updatedAt', 'id', 'pepFormId']));
    } catch (e) {
      console.error(e);
      throw e;
    }
  });

  it('should patchPepFormParticipant for seller after creation', async () => {
    try {
      const { status } = await axios.patch<PatchResponse>(
        `${e2eConfig.backendApi}/estate/pep-participant/${basePepParticipant.id}`,
        { employer: 'employer', selectedCountry: 'Norge' },
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 200);
    } catch (e) {
      console.error(e);
      throw e;
    }
  });

  it('should get participant for seller after patch', async () => {
    try {
      const { data, status } = await axios.get<GetParticipantsResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep-seller-participants`,
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 200);
      strictEqual(data!.length, 1);
      assert.ok(deepStrictEqualProps(data![0], {
        ...basePepParticipant,
        employer: 'employer',
        selectedCountry: 'Norge',
      }, ['createdAt', 'updatedAt', 'id', 'pepFormId']));
    } catch (e) {
      console.error(e);
      throw e;
    }
  });

  it('should create document for seller and save it to db', async () => {
    const sequelize = sequelizeConnection();
    try {
      const { status } = await axios.post<{ signUrl?: string }>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep/document`,
        { inksign: true },
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 201);

      const res = await sequelize.query(
        `SELECT "signingStarted", "idfyDocumentId" FROM "PEPForm" WHERE "estateVitecId"='${mockEstatePep.estateId}'`,
      );
      const signingStarted = (res[0][0] as any).signingStarted;
      const idfyDocumentId = (res[0][0] as any).idfyDocumentId;
      const signingAlreadyStartedInTheLast5Minutes =
        sub(new Date(), { minutes: 5 }).getTime() < signingStarted.getTime() &&
        signingStarted.getTime() < new Date().getTime();
      if (!signingAlreadyStartedInTheLast5Minutes) {
        throw new Error('Signing not started correctly!');
      }
      strictEqual(idfyDocumentId, 'mockIdfyDocumentIdPep');

      await sequelize.close();
    } catch (e) {
      console.error(e);
      await sequelize.close();
      throw e;
    }
  });

  it('should getPepForm sign status for seller', async () => {
    try {
      const { data, status } = await axios.get<SignStatusResponse>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep/document-sign-status`,
      );
      strictEqual(status, 200);
      deepStrictEqual(data, [{
        inkSign: true,
        isAlreadySigned: true,
        signUrl: '',
        participantPgId: basePepParticipant.id,
        contactName: 's eller',
      }]);
    } catch (e) {
      console.error(e);
      throw e;
    }
  });

  it('should finalize signing for seller, and give back bad request for get because it is already finalized', async () => {
    try {
      const { status } = await axios.post(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep/finalize`,
        {},
        { headers: { 'Content-Type': 'application/json' } },
      );
      strictEqual(status, 201);

      const { data, status: statusGet } = await axios.get<{ detail: string }>(
        `${e2eConfig.backendApi}/estate/${mockEstatePep.estateId}/pep`,
        { validateStatus: () => true }
      );
      strictEqual(statusGet, 400);
      strictEqual(data!.detail, 'pepForm.errors.alreadySigned');
    } catch (e) {
      console.error(e);
      throw e;
    }
  });
});
