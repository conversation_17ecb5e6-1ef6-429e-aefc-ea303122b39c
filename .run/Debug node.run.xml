<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Debug node" type="NodeJSConfigurationType" application-parameters="--files --ignore-watch node_modules src/index.ts --respawn -- src/app.ts" path-to-js-file="node_modules/ts-node-dev/lib/bin.js" working-dir="$PROJECT_DIR$">
    <envs>
      <env name="NODE_ENV" value="development" />
    </envs>
    <method v="2" />
  </configuration>
</component>