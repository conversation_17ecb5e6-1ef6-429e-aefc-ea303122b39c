name: Deploy
run-name: Deploy ${{ github.ref }} to ${{ github.event.inputs.environment }}
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        type: environment
        required: true
env:
  REPOSITORY_URL: 917043647191.dkr.ecr.eu-north-1.amazonaws.com/nordvik-backend
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: eu-north-1
jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    container: atherenergy/awsebcli
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Copy to AWS folder
        run: |
          mkdir .aws && cd .aws
          cp -r ../.ebextensions .
          cp -r ../.platform .
      - name: Create Dockerrun.aws.json
        run: |
          cd .aws
          echo "{\"AWSEBDockerrunVersion\":\"1\",\"Image\":{\"Name\":\"$REPOSITORY_URL:$GITHUB_SHA\",\"Update\":\"true\"},\"Ports\":[{\"ContainerPort\":\"3000\"}]}" > Dockerrun.aws.json
      - name: EB Init
        run: |
          cd .aws
          eb init -p docker -r $AWS_DEFAULT_REGION nordvik-backend
      - name: EB Deploy (Develop)
        if: github.event.inputs.environment == 'develop'
        run: |
          cd .aws
          eb deploy nordvik-api-dev
      - name: EB Deploy (Staging)
        if: github.event.inputs.environment == 'staging'
        run: |
          cd .aws
          eb deploy nordvik-api-staging
      - name: EB Deploy (Production)
        if: github.event.inputs.environment == 'production'
        run: |
          cd .aws
          eb deploy nordvik-api-production
