name: Build, Test and Deploy to Develop
on:
  push:
    branches:
      - develop
      - feat/NOR-1233-api-tests
concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true
env:
  REPOSITORY_URL: 917043647191.dkr.ecr.eu-north-1.amazonaws.com/nordvik-backend
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: eu-north-1
  ROOT_FOLDER_PATH: ${{ github.workspace }}
  E2E_API_URL: https://api-dev.nordvik.app
  E2E_POSTGRES_USERNAME: postgres
  E2E_POSTGRES_PASSWORD: ${{ vars.E2E_POSTGRES_PASSWORD }}
  E2E_POSTGRES_DB: nordvik
  E2E_POSTGRES_HOST: nordvik-dev.cscps8wwccxx.eu-north-1.rds.amazonaws.com
  E2E_MONGO_CONNECTION: ${{ vars.E2E_MONGO_CONNECTION }}
  E2E_MONGO_DB: vitec-data-sync-dev
  PUPPETEER_SKIP_DOWNLOAD: true
jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    container: node:18-alpine
    steps:
      - name: Add utilities
        run: apk add --no-cache tar
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'
      - name: Install NPM
        run: npm ci --legacy-peer-deps
      - run: export NODE_ICU_DATA=./node_modules/full-icu
      - name: Lint
        run: npm run lint
      - name: Test
        run: npm run test
  build:
    name: Build
    runs-on: ubuntu-latest
    container: docker:stable
    services:
      docker:stable-dind:
        image: docker:stable-dind
    steps:
      - name: Add utilities
        run: apk add --no-cache curl jq python3 py-pip
      - name: PIP install
        run: pip install awscli
      - name: Set AWS login
        run: $(aws ecr get-login --no-include-email --region $AWS_DEFAULT_REGION)
      - name: Checkout
        uses: actions/checkout@v3
      - name: Build image
        run: docker build -t $REPOSITORY_URL:latest .
      - name: Tag image
        run: docker tag $REPOSITORY_URL:latest $REPOSITORY_URL:$GITHUB_SHA
      - name: Push image as latest
        run: docker push $REPOSITORY_URL:latest
      - name: Push image with commit sha
        run: docker push $REPOSITORY_URL:$GITHUB_SHA

  deploy-dev:
    name: Deploy Dev
    runs-on: ubuntu-latest
    container: atherenergy/awsebcli
    environment: develop
    needs: [test, build]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Copy to AWS folder
        run: |
          mkdir .aws && cd .aws
          cp -r ../.ebextensions .
          cp -r ../.platform .
      - name: Create Dockerrun.aws.json
        run: |
          cd .aws
          echo "{\"AWSEBDockerrunVersion\":\"1\",\"Image\":{\"Name\":\"$REPOSITORY_URL:$GITHUB_SHA\",\"Update\":\"true\"},\"Ports\":[{\"ContainerPort\":\"3000\"}]}" > Dockerrun.aws.json
      - name: EB Init
        run: |
          cd .aws
          eb init -p docker -r $AWS_DEFAULT_REGION nordvik-backend
      - name: EB Deploy
        run: |
          cd .aws
          eb deploy nordvik-api-dev

  api-test-deployment:
    name: API test Dev
    runs-on: ubuntu-latest
    container: node:18-alpine
    needs: [deploy-dev]
    environment: develop
    steps:
      - name: Add utilities
        run: apk add --no-cache tar
      - name: Checkout
        uses: actions/checkout@v3
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'
      - name: Install NPM
        run: npm ci --legacy-peer-deps
      - run: export NODE_ICU_DATA=./node_modules/full-icu
      - name: Test Api
        run: npm run test:e2e
