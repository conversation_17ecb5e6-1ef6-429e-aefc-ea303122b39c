<div style="
    font-size: 12px;
    display: block;
    width: 210mm;
    height: 297mm;
    font-family: Arial, Helvetica, sans-serif;
  ">
  <!-- Title -->
  <div style="display: flex; flex-flow: row; justify-content: space-between;">
    <div>
      <h1 style="margin-bottom: 48px;">Opp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> selger</h1>
    </div>
    <div style="display: flex; flex-flow: row; align-items: baseline;">
      <span style="font-weight: 600; margin-right: 24px;">
        Oppdragsnr:
      </span>
      <div style="flex-flow: column;">
        <p style="font-weight: 400; flex-flow: row;">
          {{estateAssignmentNumber}}
        </p>
      </div>
    </div>
  </div>
  <!-- Header explanation -->
  <div style="
      display: flex;
      flex-flow: row;
      border-bottom: 1px solid black;
      padding-bottom: 24px;
      align-items: baseline;
      justify-content: space-between;
    ">
    <div>
      <div style="display: flex; flex-flow: row; align-items: baseline;">
        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Eiendom:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row;">
            {{estateAddress}}
          </p>
        </div>
      </div>
      <div style="display: flex; flex-flow: row; align-items: baseline;">
        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Gjennomført dato:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row;">
            {{date}}
          </p>
        </div>
      </div>
    </div>
    <div>
      <div style="display: flex; flex-flow: row; align-items: baseline;">
        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Meglere:
        </span>
        <div style="flex-flow: column;">
          {{#brokers}}
          <p style="font-weight: 400; flex-flow: row;">
            {{name}}
          </p>
          <p style="font-weight: 400; flex-flow: row;">
            {{phoneNumber}}
          </p>
          {{/brokers}}
        </div>
      </div>
    </div>
  </div>
  <div style="
      display: flex;
      flex-flow: row;
      flex-wrap: wrap;
      border-bottom: 1px solid black;
      padding-bottom: 8px;
      align-items: baseline;
      justify-content: space-between;
    ">
    <table style="font-size: 12px">
      <tbody>
        <tr style="vertical-align: baseline;">
          <td>
            <span style="width: 140px; font-weight: 600; margin-right: 24px;">
              Rolle
            </span>
          </td>
          <td>
            <span style="width: 140px; font-weight: 600; margin-right: 24px;">
              Navn
            </span>
          </td>
          <td>
            <span style="width: 140px; font-weight: 600; margin-right: 24px;">
              Telefon
            </span>
          </td>
          <td>
            <span style="width: 140px; font-weight: 600; margin-right: 24px;">
              E-post
            </span>
          </td>
        </tr>
        {{#participants}}

        <tr style="vertical-align: baseline;">
          <td style="margin-right: 24px; width: 140px;">{{role}}</td>
          <td style="margin-right: 24px; width: 140px;">{{name}}</td>
          <td style="margin-right: 24px; width: 140px;">{{phoneNumber}}</td>
          <td style="margin-right: 24px; width: 140px;">{{email}}</td>
        </tr>
        {{/participants}}
      </tbody>
    </table>
  </div>
  <!-- Loan details -->
  <div style="
      display: flex;
      flex-flow: row;
      flex-wrap: wrap;
      border-bottom: 1px solid black;
      padding-bottom: 24px;
      align-items: baseline;
    ">
    <div>
      <h2>
        Innfrielse av lån
      </h2>
      <div>
        Dersom lån på boligen ikke skal innfris, må pantet være slettet fra grunnboken innen overtakelse.
      </div>
      <div>
        <p style="font-weight: 600;">
          Ja / Nei
        </p>
        <p>
          {{#isMortgaged}}
          <img style="margin-right: 14px;" width="10px"
            src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/close.png" alt="check" />
          <img style="margin-right: 14px;" width="10px"
            src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/square.png" alt="no-check" />
          {{/isMortgaged}} {{^isMortgaged}}
          <img style="margin-right: 14px;" width="10px"
            src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/square.png" alt="no-check" />
          <img style="margin-right: 14px;" width="10px"
            src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/close.png" alt="check" />
          {{/isMortgaged}}
          <span style="font-weight: 600;">
            Er eiendommen belånt?
          </span>
        </p>
      </div>
      <div style="display: flex; flex-flow: row; align-items: baseline;">
        <span style="width: 300px; font-weight: 500; margin-right: 16px;">
          Hvor mange lån er det på boligen?
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-right: 36px;">
            {{loans.length}}
          </p>
        </div>
      </div>
    </div>
  </div>
  <!-- Loans -->
  {{#loans.length}}
  <div style="
      display: grid;
      grid-template-columns: auto auto;
      align-items: baseline;
      justify-content: space-between;
    ">
    {{#loans}}
    <div>
      <h2>Lån {{index}}</h2>
      <div style="
          display: grid;
          grid-template-columns: auto auto;
          padding-bottom: 24px;
          align-items: baseline;
          justify-content: space-between;
        ">
        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Låntaker:
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          <p style="font-weight: 400; flex-flow: row;">
            {{loanTaker}}
          </p>
        </div>

        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Bank:
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          <p style="font-weight: 400; flex-flow: row;">
            {{loanBank}}
          </p>
        </div>

        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Lånenummer:
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          <p style="font-weight: 400; flex-flow: row;">
            {{loanIdNumber}}
          </p>
        </div>

        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Restgjeld i kr:
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          <p style="font-weight: 400; flex-flow: row;">
            {{residualDebt}}
          </p>
        </div>

        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Kontaktperson:
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          <p style="font-weight: 400; flex-flow: row;">
            {{bankContactName}}
          </p>
        </div>

        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Telefon:
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          <p style="font-weight: 400; flex-flow: row;">
            {{bankContactPhone}}
          </p>
        </div>

        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          E-post:
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          <p style="font-weight: 400; flex-flow: row;">
            {{bankContactEmail}}
          </p>
        </div>

        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Skal lånet innfris?
        </span>
        <div style="flex-flow: column; max-width: 120px;">
          {{#shouldMortgageLoanBeRepaid}}
          <p style="font-weight: 400; flex-flow: row;">
            {{shouldMortgageLoanBeRepaidText}}
          </p>
          {{/shouldMortgageLoanBeRepaid}}
          {{^shouldMortgageLoanBeRepaid}}
          <p style="font-weight: 400; flex-flow: row; color: red;">
            {{shouldMortgageLoanBeRepaidText}}
          </p>
          {{/shouldMortgageLoanBeRepaid}}
        </div>
      </div>
    </div>
    {{/loans}}
  </div>
  {{/loans.length}}

  <div style="padding-top: 24px; font-weight: 600;">
    Hvis et eller flere lån ikke skal innfris av selgerne i fellesskap må dere fylle inn lånenummer og hvem dette skal
    trekkes fra:
  </div>
  <div style="padding-bottom: 24px; border-bottom: 1px solid black; color: red;">{{accountsComment}}</div>

  <!-- Accounts and account managers -->
  <div>
    <h2>
      Informasjon om tilgodehavende
    </h2>
    <div>Tilgodehavende overføres til norsk kontonummer tilhørende hjemmehaver eller ihht. fullmakt.</div>
    <div>Tilgodehavende utbetales i henhold til registrert eierbrøk, men mindre noe annet blir avtalt her</div>

    <div style="display: flex; flex-flow: row; align-items: baseline;">
      <span style="width: 120px; font-weight: 600; margin-right: 16px;">
        Hvor mange konti skal oppgjøret utbetales til?
      </span>
      <div style="flex-flow: column;">
        <p style="font-weight: 400; flex-flow: row; margin-right: 36px;">
          {{accounts.length}} kontonummer
        </p>
      </div>
    </div>
    <div>
      Dersom fordelingen fraviker registrert eierbrøk, gir selgerne gjensidig fullmakt til at tilgodehavende utbetales i
      samsvar med avtalt fordeling.
    </div>
  </div>

  <!-- Accounts -->

  {{#accounts}}
  <div class="accountsDiv" style="display: flex; flex-flow: column;">
    <h2>Konto {{index}}</h2>
    <div style="display: flex; flex-flow: row; align-items: baseline;">
      <span style="width: 120px; font-weight: 600; margin-right: 16px;">
        Kontonummer:
      </span>
      <div style="flex-flow: column;">
        <p style="font-weight: 400; flex-flow: row; margin-right: 36px;">
          {{accountNumber}}
        </p>
      </div>
    </div>
    <div style="display: flex; flex-flow: row; align-items: baseline;">
      <span style="width: 120px; font-weight: 600; margin-right: 16px;">
        Kontoinnehaver:
      </span>
      <div style="flex-flow: column;">
        <p style="font-weight: 400; flex-flow: row; margin-right: 36px;">
          {{accountOwner}}
        </p>
      </div>
    </div>
    <div style="display: flex; flex-flow: row; align-items: baseline; margin-bottom: 24px;">
      <span style="width: 120px; font-weight: 600; margin-right: 16px;">
        Fordeling i prosent:
      </span>
      <div style="flex-flow: column;">
        <p style="font-weight: 400; flex-flow: row; margin-right: 36px;">
          {{distribution}} %
        </p>
      </div>
    </div>

    <!-- Checkbox -->
    <div>
      <p style="font-weight: 600;">
        Ja / Nei
      </p>
      <p>
        {{#hasFinanceAccountManagers}}
        <img style="margin-right: 14px;" width="10px"
          src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/close.png" alt="check" />
        <img style="margin-right: 14px;" width="10px"
          src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/square.png" alt="check" />
        {{/hasFinanceAccountManagers}} {{^hasFinanceAccountManagers}}
        <img style="margin-right: 14px;" width="10px"
          src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/square.png" alt="check" />
        <img style="margin-right: 14px;" width="10px"
          src="https://s3.eu-north-1.amazonaws.com/static.nordvik.app/close.png" alt="check" />
        {{/hasFinanceAccountManagers}}
        <span style="font-weight: 600;">
          Har andre personer enn selger tilgang til kontoen som selger skal motta oppgjøret på?
        </span>
      </p>
      <div style="display: flex; flex-flow: row; align-items: baseline;">
        <span style="width: 140px; font-weight: 600; margin-right: 24px;">
          Hvor mange personer:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row;">
            {{financeAccountManagers.length}}
          </p>
        </div>
      </div>
    </div>

    <!-- Account Managers -->
    <div style="
        display: grid;
        grid-template-columns: auto auto;
        border-bottom: 1px solid black;
        padding-bottom: 24px;
        align-items: baseline;
        justify-content: space-between;
      ">
      {{#financeAccountManagers.length}} {{#financeAccountManagers}}
      <div style="margin-right: 12px;">
        <h2>Kontodisponent {{index}}</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; align-items: baseline;">
          <span style="width: 140px; font-weight: 600; margin-right: 24px;">
            Navn:
          </span>
          <div style="flex-flow: column;">
            <p style="font-weight: 400; flex-flow: row;">
              {{name}}
            </p>
          </div>
          <span style="width: 140px; font-weight: 600; margin-right: 24px;">
            Fødsels- og personnummer:
          </span>
          <div style="flex-flow: column;">
            <p style="font-weight: 400; flex-flow: row;">
              {{ssn}}
            </p>
          </div>
          <span style="width: 140px; font-weight: 600; margin-right: 24px;">
            Adresse:
          </span>
          <div style="flex-flow: column;">
            <p style="font-weight: 400; flex-flow: row;">
              {{address}}
            </p>
          </div>
          <span style="width: 140px; font-weight: 600; margin-right: 24px;">
            Relasjon til kontodisponent:
          </span>
          <div style="flex-flow: column;">
            <p style="font-weight: 400; flex-flow: row;">
              {{relationToAccountManager}}
            </p>
          </div>
        </div>
      </div>
      {{/financeAccountManagers}} {{/financeAccountManagers.length}}
    </div>
  </div>
  {{/accounts}}

  <!-- PEP own/employer/family pages -->
  {{#pepParticipants}}
  <div style="page-break-inside: avoid; padding-bottom: 8px; border-bottom: 1px solid black;">
    <h2 style="font-size: 18px; margin-bottom: 16px; margin-top: 12px;">PEP-avklaring - {{name}}</h2>
  </div>

  <!-- Elaborations -->
  <div style="
    page-break-inside: avoid;
    display: flex;
    flex-flow: row;
    border-bottom: 1px solid black;
    align-items: stretch;
  ">
    <!-- Avklaring - Egen -->
    <div style="flex: 1; padding: 8px;">
      <h2 style="text-align: center; font-size: 18px; margin-top: 12px;">Avklaring - Egen</h2>
      {{#ownDescription}}
      <span style="font-size: 12px; font-weight: 600; margin-top: 24px; margin-bottom: 16px;">
        {{ownTitle}}
      </span>
      {{/ownDescription}}
      {{^ownDescription}}
      <span style="font-size: 12px; font-weight: 300; margin-top: 24px; margin-bottom: 16px;">
        {{ownTitle}}
      </span>
      {{/ownDescription}}
      {{#ownDescription}}
      <div style="flex-grow: 1; display: grid; grid-template-columns: auto auto; align-items: baseline;">
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Stilling/Verv:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{description}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Land:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{countries}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Tidsperiode:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{#isLessThanAYearAgo}}
            Mindre enn ett år siden
            {{/isLessThanAYearAgo}}
            {{^isLessThanAYearAgo}}
            Mer enn ett år siden
            {{/isLessThanAYearAgo}}
          </p>
        </div>
        {{^isLessThanAYearAgo}}
        <span style="margin-right: 24px; margin-bottom: 6px; margin-top: 6px; padding-left: 8px;">
          Dato for opphør:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{dateWhenPracticed}}
          </p>
        </div>
        {{/isLessThanAYearAgo}}
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Andre kommentarer:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{ownComment}}
          </p>
        </div>
      </div>
      {{/ownDescription}}
    </div>
    <!-- Avklaring - Medarbeider -->
    <div style="flex: 1; padding: 8px; border-right: 1px solid black; border-left: 1px solid black;">
      <h2 style="text-align: center; font-size: 18px; margin-bottom: 16px; margin-top: 12px;">Avklaring - Medarbeider
      </h2>
      {{#employerDescription}}
      <span style="font-size: 12px; font-weight: 900; margin-top: 24px; margin-bottom: 16px;">
        {{employerTitle}}
      </span>
      {{/employerDescription}}
      {{^employerDescription}}
      <span style="font-size: 12px; font-weight: 300; margin-top: 24px; margin-bottom: 16px;">
        {{employerTitle}}
      </span>
      {{/employerDescription}}
      {{#employerDescription}}
      <div style="display: grid; grid-template-columns: auto auto; align-items: baseline;">
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Stilling/Verv:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{description}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Navn:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{name}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Relasjon:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{relationship}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Land:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{countries}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Tidsperiode:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{#isLessThanAYearAgo}}
            Mindre enn ett år siden
            {{/isLessThanAYearAgo}}
            {{^isLessThanAYearAgo}}
            Mer enn ett år siden
            {{/isLessThanAYearAgo}}
          </p>
        </div>
        {{^isLessThanAYearAgo}}
        <span style="margin-right: 24px; margin-bottom: 6px; margin-top: 6px; padding-left: 8px;">
          Dato for opphør:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{dateWhenPracticed}}
          </p>
        </div>
        {{/isLessThanAYearAgo}}
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Andre kommentarer:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{employerComment}}
          </p>
        </div>
      </div>
      {{/employerDescription}}
    </div>
    <!-- Avklaring - Familie -->
    <div style="flex: 1; padding: 8px;">
      <h2 style="text-align: center; font-size: 18px; margin-bottom: 16px; margin-top: 12px;">Avklaring - Familie
      </h2>
      {{#familyDescription}}
      <span style="font-size: 12px; font-weight: 900; margin-top: 24px; margin-bottom: 16px;">
        {{familyTitle}}
      </span>
      {{/familyDescription}}
      {{^familyDescription}}
      <span style="font-size: 12px; font-weight: 300; margin-top: 24px; margin-bottom: 16px;">
        {{familyTitle}}
      </span>
      {{/familyDescription}}
      {{#familyDescription}}
      <div style="flex-grow: 1; display: grid; grid-template-columns: auto auto; align-items: baseline;">
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Stilling/Verv:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{description}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Navn:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{name}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Relasjon:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{relationship}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Land:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{countries}}
          </p>
        </div>
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Tidsperiode:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{#isLessThanAYearAgo}}
            Mindre enn ett år siden
            {{/isLessThanAYearAgo}}
            {{^isLessThanAYearAgo}}
            Mer enn ett år siden
            {{/isLessThanAYearAgo}}
          </p>
        </div>
        {{^isLessThanAYearAgo}}
        <span style="margin-right: 24px; margin-bottom: 6px; margin-top: 6px; padding-left: 8px;">
          Dato for opphør:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{dateWhenPracticed}}
          </p>
        </div>
        {{/isLessThanAYearAgo}}
        <span style="font-weight: 600; margin-bottom: 6px; margin-top: 6px; margin-right: 24px;">
          Andre kommentarer:
        </span>
        <div style="flex-flow: column;">
          <p style="font-weight: 400; flex-flow: row; margin-bottom: 6px; margin-top: 6px;">
            {{familyComment}}
          </p>
        </div>
      </div>
      {{/familyDescription}}
    </div>
  </div>
  {{/pepParticipants}}
</div>
<style>
  @media print {
    .accountsDiv {
      page-break-before: always;
    }
  }
</style>