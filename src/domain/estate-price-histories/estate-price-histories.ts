import type { Resource } from '../../framework/sequelize/resource';
import type { Estate } from '../estate/estate';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';

export type EstatePriceHistory = Resource & {
  postgresEstateId: Estate['id'];
  landIdentificationMatrix: LandIdentificationMatrix;
  evPrice: number;
  actualPriceWithVitecOffset: number;
};
