import type { EstatePriceHistory } from './estate-price-histories';
import { EstatePriceHistoryModel } from './estate-price-histories.model';

export type EstatePriceHistoriesRepository = {
  getByPostgresEstateId(props: { postgresEstateId: string }): Promise<EstatePriceHistory[] | null>;
};

export const estatePriceHistoriesRepositoryFactory = (): EstatePriceHistoriesRepository => {
  const getByPostgresEstateId = async ({
    postgresEstateId,
  }: {
    postgresEstateId: string;
  }): Promise<EstatePriceHistory[] | null> => {
    const res = await EstatePriceHistoryModel.findAll({
      where: {
        postgresEstateId,
      },
    });
    if (!res) {
      return null;
    }
    return res.map((r) => r.toJSON());
  };

  return {
    getByPostgresEstateId,
  };
};
