import type { Sequelize } from 'sequelize';
import { ResourceModel } from '../../framework/sequelize/resource';
import {
  estatePriceHistoriesTableAttributes,
  ESTATE_PRICE_HISTORIES_TABLE_NAME,
} from '../../migrations/20220818130000/estate-price-histories.table';
import type { Estate } from '../estate/estate';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';

export class EstatePriceHistoryModel extends ResourceModel {
  public postgresEstateId: Estate['id'];
  public landIdentificationMatrix: LandIdentificationMatrix;
  public evPrice: number;
  public actualPriceWithVitecOffset: number;
}

export const estatePriceHistoryModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstatePriceHistoryModel.init(estatePriceHistoriesTableAttributes, {
    sequelize,
    tableName: ESTATE_PRICE_HISTORIES_TABLE_NAME,
    timestamps: true,
  });
};
