import { dateFromBankidFormat } from './BankIdDate';

const validNor = [
  ['***********', '1934-10-21'],
  ['211034-26631', '1934-10-21'],
  ['***********', '1950-01-16'],
  ['***********', '1983-08-10'],
  ['***********', '2018-06-03'],
  ['***********', '1921-08-18'],
  ['***********', '2014-08-19'],
  ['***********', '1915-10-24'],
  ['***********', '1974-12-13'],
  ['***********', '1914-09-29'],
  ['270588 33598', '1988-05-27'],
];

const validSwe = [
  ['870430-2713', '1987-04-30'],
  ['**********', '1987-04-30'],
];

const invalid = ['530913-5701', '530934-5701', '*********64', '*********6', '*********', '', null, undefined];

describe('BankIdDate', () => {
  it('Returns date on valid norwegian', () => {
    for (const [str, expected] of validNor) {
      const date = new Date(expected);
      const result = dateFromBankidFormat(str);
      expect(result.toDateString()).toEqual(date.toDateString());
    }
  });

  it('Returns date on valid swedish', () => {
    for (const [str, expected] of validSwe) {
      const date = new Date(expected);
      const result = dateFromBankidFormat(str);
      expect(result.toDateString()).toEqual(date.toDateString());
    }
  });

  it('Returns 1900 on invalid', () => {
    const def = new Date('1900-01-01');
    for (const str of invalid) {
      const result = dateFromBankidFormat(str as string);
      expect(result.toDateString()).toEqual(def.toDateString());
    }
  });
});
