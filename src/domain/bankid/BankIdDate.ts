import { logger } from '../../logger';
import * as norwegian from './norwegian-ssn';
import * as swedish from './swedish-ssn';

export const dateFromBankidFormat = (ssn: string): Date => {
  try {
    const norRes = norwegian.validate(ssn);
    if (norRes.isValid) {
      return norRes.birthDate;
    }

    const sweRes = swedish.validate(ssn);
    if (sweRes.isValid) {
      return sweRes.birthDate;
    }
  } catch (e) {
    logger.error(e as Error, 'Error while parsing SSN to date');
  }

  return new Date('1900-01-01');
};
