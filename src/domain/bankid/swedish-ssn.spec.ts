import { validate } from './swedish-ssn';

describe('SwedishSSN', () => {
  describe('#validate', () => {
    it('Should fail when given empty String', () => {
      expect(validate('')).toEqual({ isValid: false });
    });

    it('Should fail when given undefined', () => {
      expect(validate(undefined)).toEqual({ isValid: false });
    });

    it('Should fail when given null', () => {
      expect(validate(null)).toEqual({ isValid: false });
    });

    it('Should pass when given valid Swedish ssn with yy format year', () => {
      expect(validate('870430-2713')).toEqual({ isValid: true, birthDate: new Date('1987-04-30') });
    });

    it('Should pass when given valid Swedish ssn yyyy format year', () => {
      expect(validate('20870430-2713')).toEqual({ isValid: true, birthDate: new Date('1987-04-30') });
    });

    it('Should fail when given invalid Swedish ssn', () => {
      expect(validate('530913-5701')).toEqual({ isValid: false });
    });

    it('Should failwhen given non-parsable date', () => {
      expect(validate('530934-5701')).toEqual({ isValid: false });
    });
  });
});
