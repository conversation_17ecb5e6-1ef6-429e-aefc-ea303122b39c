import { validate } from './norwegian-ssn';

const invalid = [
  '99999999999',
  '12345678900',
  '21103426632',
  '16015015435',
  '10089338788',
  '03071888089',
  '18182135460',
  '19081470751',
  '24101539581',
  '13127448172',
  '32091402864',
  '3209140286',
  '320914028',
  '32091402',
  '3209140',
  '320914',
  '32091',
  '3209',
  '320',
  '32',
  '3',
];

const valid = [
  ['21103426631', '1934'],
  ['16015014435', '1950'],
  ['10088338788', '1983'],
  ['03061888089', '2018'],
  ['18082135460', '1921'],
  ['19081470750', '2014'],
  ['24101539551', '1915'],
  ['13127448072', '1974'],
  ['29091402864', '1914'],
  ['19040518226', '1905'],
  ['26085638318', '1956'],
  ['24081379633', '2013'],
  ['27104432382', '1944'],
  ['21038813991', '1988'],
  ['12101941262', '1919'],
  ['16114849387', '1948'],
  ['19120911360', '1909'],
  ['25013649068', '1936'],
  ['15089026684', '1990'],
  ['04040799945', '2007'],
  ['26075005015', '1950'],
  ['19098502206', '1985'],
  ['09058419114', '1984'],
  ['22098347481', '1983'],
  ['21080120068', '1901'],
  ['20068608067', '1986'],
  ['13077020637', '1970'],
  ['22074310331', '1943'],
  ['11051072806', '2010'],
  ['07081013041', '1910'],
  ['03032518973', '1925'],
  ['02028545935', '1985'],
  ['23094331331', '1943'],
  ['23046804805', '1968'],
  ['19080941784', '1909'],
  ['19051777126', '2017'],
  ['03050921521', '1909'],
  ['21023825187', '1938'],
  ['30125723191', '1957'],
  ['03078702787', '1987'],
  ['01101269538', '2012'],
  ['25127524419', '1975'],
  ['03018931469', '1989'],
  ['28075407825', '1954'],
  ['18080632785', '1906'],
  ['03125819590', '1958'],
  ['18020789162', '2007'],
  ['15014841609', '1948'],
  ['06111760509', '2017'],
  ['05031155268', '2011'],
  ['04018006419', '1980'],
  ['02022076844', '2020'],
  ['01094027248', '1940'],
  ['26012004035', '1920'],
  ['28119633925', '1996'],
  ['12036517881', '1965'],
  ['24120185299', '2001'],
  ['24100726536', '1907'],
  ['11040987896', '2009'],
  ['09086010337', '1960'],
  ['31038004746', '1980'],
  ['19104808479', '1948'],
  ['14120704851', '1907'],
  ['12066613699', '1966'],
  ['11081437523', '1914'],
  ['11028733692', '1987'],
  ['31018904481', '1989'],
  ['29072414886', '1924'],
  ['01099124071', '1991'],
  ['01058917024', '1989'],
  ['07091474128', '2014'],
  ['17039543222', '1995'],
  ['10124837702', '1948'],
  ['02120716533', '1907'],
  ['30054320589', '1943'],
  ['26017807403', '1978'],
  ['29016210976', '1962'],
  // ['15129956558', '1899'],
  ['16081667296', '2016'],
  ['23115346156', '1953'],
  ['13015520382', '1955'],
  ['02091741302', '1917'],
  ['23103836050', '1938'],
  ['27036020666', '1960'],
  ['17013608997', '1936'],
  ['23126827199', '1968'],
  ['11082127021', '1921'],
  ['02066825461', '1968'],
  ['28079230997', '1992'],
  ['29111218593', '1912'],
  ['17078314613', '1983'],
  ['10120176426', '2001'],
  ['13029329251', '1993'],
  ['02037643745', '1976'],
  ['26046209531', '1962'],
  ['27053737396', '1937'],
  ['18114539178', '1945'],
  ['23070090317', '2000'],
  ['14089547882', '1995'],
  ['16032418930', '1924'],
];

describe('Validate SSN', () => {
  it('Should succeed', () => {
    for (const [str, year] of valid) {
      const day = str.slice(0, 2);
      const month = str.slice(2, 4);
      const date = new Date(`${year}-${month}-${day}`);

      const result = validate(str);

      expect(result.isValid).toEqual(true);
      expect(result.birthDate?.toDateString()).toEqual(date.toDateString());
    }
  });

  it('Should fail', () => {
    const ok = invalid.every((str) => validate(str).isValid === false);
    expect(ok).toBe(true);
  });

  it('Should fail on empty', () => {
    const fail = validate('').isValid;
    expect(fail).toBeFalsy();
  });

  it('Should fail on undefined', () => {
    const fail = validate((undefined as unknown) as string).isValid;
    expect(fail).toBeFalsy();
  });

  it('Should fail on null', () => {
    const fail = validate((null as unknown) as string).isValid;
    expect(fail).toBeFalsy();
  });
});
