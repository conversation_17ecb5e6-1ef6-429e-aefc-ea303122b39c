import type { IdSessionResponse } from '../../framework/idfy/interface/IdSessionResponse';

export const bankIdSessionMock: IdSessionResponse = {
  created: new Date(),
  expires: new Date(),
  flow: 'redirect',
  identity: {
    firstName: 'Incep',
    middleName: 'Tech',
    lastName: 'User',
    dateOfBirth: new Date(),
    nin: '**********',
  },
  status: 'success',
  provider: 'no_bankid_mobile',
  error: undefined,
  id: 'f9de8a29-c82f-4d6a-8568-577c2e22d7a4',
};
