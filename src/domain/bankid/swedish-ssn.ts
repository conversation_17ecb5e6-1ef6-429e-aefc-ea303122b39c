import { flatMap } from 'lodash';
import { isValid as isValidDate } from 'date-fns';
import type { SsnParseResult } from './ssn';

/**
 * Validates parameter given SSN. Returns true if SSN is valid, otherwise false.
 * @param ssn - {String}
 * @returns {SsnParseResult}
 */
export function validate(ssn: string | null | undefined): SsnParseResult {
  if (ssn === undefined || ssn === null) {
    return { isValid: false };
  }

  const ssnAsArray = parse(ssn);
  if (ssnAsArray.length === 10) {
    const checkDigit = ssnAsArray.pop();
    switch (getChecksum(ssnAsArray)) {
      case checkDigit: {
        const birthDate = getBirthDate(ssnAsArray);
        return birthDate
          ? {
              isValid: true,
              birthDate,
            }
          : { isValid: false };
      }
      default:
        return { isValid: false };
    }
  } else {
    return { isValid: false };
  }
}

function getBirthDate(ssnAsArray: number[]): Date | null {
  const currentYear = new Date().getFullYear();
  let year = `${ssnAsArray[0]}${ssnAsArray[1]}`;
  if (+year <= currentYear % 100) {
    year = `20${year}`;
  } else {
    year = `19${year}`;
  }

  const month = `${ssnAsArray[2]}${ssnAsArray[3]}`;
  const day = `${ssnAsArray[4]}${ssnAsArray[5]}`;

  const retVal = new Date(`${year}-${month}-${day}`);

  return isValidDate(retVal) ? retVal : null;
}

function getChecksum(ssn: number[]): number {
  const luhn = [2, 1, 2, 1, 2, 1, 2, 1, 2];
  const multiplied = ssn.map((e, i) => e * luhn[i]);
  const digits = flatMap(multiplied, (n) => (n >= 10 ? [sumOfNum(n)] : [n]));

  const reducer = (accumulator: number, currentValue: number): number => accumulator + currentValue;
  const sumOfDigits = digits.reduce(reducer);
  const checksum = Math.floor((sumOfDigits * 9) % 10);
  return checksum;
}

function sumOfNum(num: number): number {
  return Math.floor(num % 10) + Math.floor(num / 10);
}

/**
 * Parse parameter given SSN string. Remove all characters except digits.
 * @param ssn - {String} SSN to parse
 * @returns Int[]
 */
function parse(ssn: string): number[] {
  const cleaned = ssn.replace(/\D/g, '').split('');
  if (cleaned.length === 12) {
    return cleaned.slice(2).map(Number);
  }
  return cleaned.map(Number);
}
