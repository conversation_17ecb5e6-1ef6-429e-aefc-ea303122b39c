import { addHours, isValid as isValidDate } from 'date-fns';
import { NorwegianId } from 'norwegian-national-id-validator';
import type { SsnParseResult } from './ssn';

/**
 * Validates a Norwegian SSN
 *
 * @param {string|number} str The number
 * @return {SsnParseResult} If number was validated or not
 */
export const validate = (str: string): SsnParseResult => {
  if (!str) {
    return { isValid: false };
  }

  const trimmed = str.replace(/\D/g, '');
  const validation = NorwegianId(trimmed);
  const isValid = validation.isValid();
  const birthDate = validation.birthDate();

  return isValid && isValidDate(birthDate)
    ? {
        isValid: true,
        birthDate: addHours(birthDate as Date, 12),
      }
    : { isValid: false };
};
