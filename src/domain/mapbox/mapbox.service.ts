import axios from 'axios';

export type GeoCode = {
  lat: number;
  lng: number;
};
type GeocodeResponse = {
  query: string[];
  features: {
    relevance: number;
    center: [number, number];
  }[];
};
export type MapBoxImageQuery = {
  geoCode: GeoCode;
  zoom: number;
  userName: string;
  styleName: string;
};

export type MapboxService = {
  getGeoCodeFromAddress: (address: string) => Promise<GeoCode | null>;
  getImagefromGeoCode: (i: MapBoxImageQuery) => Promise<Buffer | null>;
};

export const mapboxServiceFactory = (accessToken: string, baseUrl: string, minRelevance: number): MapboxService => {
  const getGeoCodeFromAddress = async (address: string): Promise<GeoCode | null> => {
    const url = `${baseUrl}/geocoding/v5/mapbox.places/${address}.json?limit=1&types=address&access_token=${accessToken}`;
    try {
      const response = await axios.get<GeocodeResponse>(url);
      const geoCode = response.data.features.find((result) => result.relevance > minRelevance);
      if (!geoCode) {
        return null;
      }
      return {
        lng: geoCode.center[0],
        lat: geoCode.center[1],
      };
    } catch {
      return null;
    }
  };

  const getImagefromGeoCode = async (i: MapBoxImageQuery): Promise<Buffer | null> => {
    const url = `${baseUrl}/styles/v1/${i.userName}/${i.styleName}/static/${i.geoCode.lng},${i.geoCode.lat},${i.zoom},0,0/512x512?access_token=${accessToken}`;
    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      const buffer = response.data as Buffer;
      return buffer;
    } catch {
      return null;
    }
  };

  return {
    getGeoCodeFromAddress,
    getImagefromGeoCode,
  };
};
