import type { Schema } from 'mongoose';

export enum ContactType {
  PERSON = 0,
  COMPANY = 1,
}

export type ContactMongoose = {
  _id: Schema.Types.ObjectId;
  contactId: string;
  address: string;
  city: string;
  changedDate: Date;
  companyName: string;
  consents: unknown[];
  contactType: ContactType;
  customerReview: string;
  departmentId: number;
  email: string;
  firstName: string;
  lastName: string;
  mobilePhone: string;
  organisationNumber: number;
  postalAddress: string;
  postalCode: string;
  privatePhone: string;
  workPhone: string;
};
