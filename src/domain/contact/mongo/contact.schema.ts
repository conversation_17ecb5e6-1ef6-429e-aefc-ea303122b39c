import { Schema } from 'mongoose';

export const contactSchema = new Schema({
  _id: Schema.Types.ObjectId,
  contactId: String,
  address: String,
  city: String,
  changedDate: Date,
  companyName: String,
  consents: [Object],
  contactType: Number,
  customerReview: String,
  departmentId: Number,
  email: String,
  firstName: String,
  lastName: String,
  mobilePhone: String,
  organisationNumber: Number,
  postalAddress: String,
  postalCode: String,
  privatePhone: String,
  workPhone: String,
});
