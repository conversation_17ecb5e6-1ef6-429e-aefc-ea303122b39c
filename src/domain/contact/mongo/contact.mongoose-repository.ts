import type { Connection, Document } from 'mongoose';
import type { Contact } from '../contact';
import type { ContactMongoose } from './contact-mongoose';
import { contactSchema } from './contact.schema';

export type ContactMongooseRepository = {
  getContactsByIDs(ids: string[]): Promise<Contact[]>;
};

const contactTransformer = (contactMongoose: ContactMongoose): Contact => ({
  name: `${contactMongoose.firstName || ''} ${contactMongoose.lastName || ''}`,
  phone: contactMongoose.mobilePhone,
  type: contactMongoose.contactType,
  email: contactMongoose.email,
  contactId: contactMongoose.contactId,
});

export const contactMongooseRepositoryFactory = ({
  connection,
}: {
  connection: Connection;
}): ContactMongooseRepository => {
  const contactModel = connection.model<ContactMongoose & Document>('Contact ', contactSchema, 'contacts');

  const getContactsByIDs = async (ids: string[]): Promise<Contact[]> => {
    const selectedConctacts = await contactModel.find({ contactId: { $in: ids } });
    return selectedConctacts.map((c) => contactTransformer(c));
  };

  return { getContactsByIDs };
};
