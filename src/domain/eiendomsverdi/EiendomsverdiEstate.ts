import type { EstatePG } from '../estate/estate';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import type { User } from '../user/user';
import type { ValuationEstate } from '../valuation/ValuationEstate';

export type CreatableEstate = {
  toEstateCreatePayload(user: User): Partial<EstatePG>;
};

export const createEiendomsverdiEstate = (dto: ValuationEstate): CreatableEstate => {
  const getLandIdentificationMatrix = (): LandIdentificationMatrix => {
    return {
      ...dto.location,
    };
  };

  const toEstateCreatePayload = (user: User): Partial<EstatePG> => {
    const numberOfBedrooms = parseInt(dto.numBedrooms, 10);
    const livingArea = parseInt(dto.livingArea, 10);
    const buildYear = parseInt(dto.buildYear, 10);
    const floor = parseInt(dto.floor, 10);
    const landIdentificationMatrix = getLandIdentificationMatrix();
    return {
      userID: user.id,
      address: dto.address,
      landIdentificationMatrix,
      propertyType: dto.estateType,
      numberOfBedrooms: isNaN(numberOfBedrooms) ? null : numberOfBedrooms,
      livingArea: isNaN(livingArea) ? null : livingArea,
      buildYear: isNaN(buildYear) ? null : buildYear,
      floor: isNaN(floor) ? null : floor,
      ownership: dto.ownership,
      EVEstateID: dto.estateId, // this is null for new REST API
      EVAddressID: dto.addressId,
      OrganizationNumber: dto.OrganizationNumber,
      ShareNumber: dto.ShareNumber,
    };
  };

  return { toEstateCreatePayload };
};
