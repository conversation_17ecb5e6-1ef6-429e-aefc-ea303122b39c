import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { ResourceModel } from '../../framework/sequelize/resource';
import {
  eiendomsverdiEstateCacheAttributes as originalModelAttributes,
  EIENDOMSVERDI_ESTATE_CACHE_TABLE_NAME,
} from '../../migrations/202201101040/eiendomsverdi-estate-cache.table';
import type { CacheTypeEnum } from './eiendomsverdi-estate-cache';
import type { ParsedXmlTag } from './SoapApiClient';

export const eiendomsverdiEstateCacheAttributes: ModelAttributes = {
  ...originalModelAttributes,
  queryHash: {
    type: DataTypes.CHAR(64),
    allowNull: false,
    unique: true,
  },
  query: {
    type: DataTypes.TEXT,
    allowNull: false,
    unique: true,
  },
};

export class EiendomsverdiEstateCacheModel extends ResourceModel {
  public query!: string;
  public cacheType!: CacheTypeEnum;
  public lastSynced!: Date;
  public data!: ParsedXmlTag;
  public queryHash!: string;
}

export const eiendomsverdiEstateCacheModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EiendomsverdiEstateCacheModel.init(eiendomsverdiEstateCacheAttributes, {
    sequelize,
    tableName: EIENDOMSVERDI_ESTATE_CACHE_TABLE_NAME,
    timestamps: true,
  });
};
