import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import { UserModel } from '../user/user.model';

export const EIENDOMSVERDI_AUDIT_TABLE_NAME = 'EiendomsverdiAudit';

export const eiendomsverdiAuditAttributes: ModelAttributes = {
  ...baseModelAttributes,
  userID: {
    type: DataTypes.UUID,
    references: {
      model: UserModel,
      key: 'id',
    },
    allowNull: true,
  },
  response: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
};

export class EiendomsverdiAuditModel extends ResourceModel {
  public userID: UserModel['id'];
  public response: string;
}

export const eiendomsverdiAuditModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EiendomsverdiAuditModel.init(eiendomsverdiAuditAttributes, {
    sequelize,
    tableName: EIENDOMSVERDI_AUDIT_TABLE_NAME,
    timestamps: true,
  });
};

export const setEiendomsverdiModelReferences = (): void => {
  EiendomsverdiAuditModel.belongsTo(UserModel, {
    foreignKey: 'id',
    as: 'user',
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  });
};
