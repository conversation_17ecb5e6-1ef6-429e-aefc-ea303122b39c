import xmljs from 'xml-js';
import { unwrapActionResult } from './EiendomsverdiApiClient';
import { toEstateIds } from './EiendomsverdiEstateValuationService';
import type { ParsedXmlTag } from './SoapApiClient';

describe('eiendsomverdi estate valuation service', () => {
  describe('#toEstateIds', () => {
    describe('given a response with share information', () => {
      const mockResponse =
        '<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:a="http://www.w3.org/2005/08/addressing" xmlns:u="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">\n' +
        '    <s:Header>\n' +
        '        <a:Action s:mustUnderstand="1">http://tempuri.org/IPublicInformationRealtime/GetEstatesByOwnerIdentityResponse</a:Action>\n' +
        '        <o:Security s:mustUnderstand="1" xmlns:o="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">\n' +
        '            <u:Timestamp u:Id="_0">\n' +
        '                <u:Created>2020-01-01T00:00:00.000Z</u:Created>\n' +
        '                <u:Expires>2020-01-01T00:00:00.000Z</u:Expires>\n' +
        '            </u:Timestamp>\n' +
        '        </o:Security>\n' +
        '    </s:Header>\n' +
        '    <s:Body>\n' +
        '        <GetEstatesByOwnerIdentityResponse xmlns="http://tempuri.org/">\n' +
        '            <GetEstatesByOwnerIdentityResult xmlns:b="http://schemas.datacontract.org/2004/07/EV.InformationServices.ExternalApi.DTO" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">\n' +
        '                <b:Estates>\n' +
        '                    <b:RealEstateIdentificationInfo>\n' +
        '                        <b:EV_AddressID>Fake Address ID</b:EV_AddressID>\n' +
        '                        <b:EV_EstateID>Fake Estate ID</b:EV_EstateID>\n' +
        '                        <b:HousingCooperatativeShareIdentification>\n' +
        '                            <b:OrganizationNumber>Fake Organization Number</b:OrganizationNumber>\n' +
        '                            <b:ShareNumber>Fake Share Number</b:ShareNumber>\n' +
        '                        </b:HousingCooperatativeShareIdentification>\n' +
        '                        <b:Cadastre>\n' +
        '                            <b:BNr>205</b:BNr>\n' +
        '                            <b:FNr>0</b:FNr>\n' +
        '                            <b:GNr>34</b:GNr>\n' +
        '                            <b:KNr>3005</b:KNr>\n' +
        '                            <b:SNr>14</b:SNr>\n' +
        '                        </b:Cadastre>\n' +
        '                    </b:RealEstateIdentificationInfo>\n' +
        '                </b:Estates>\n' +
        '                <b:Status>\n' +
        '                    <b:Description>Ok</b:Description>\n' +
        '                    <b:Key>0</b:Key>\n' +
        '                </b:Status>\n' +
        '            </GetEstatesByOwnerIdentityResult>\n' +
        '        </GetEstatesByOwnerIdentityResponse>\n' +
        '    </s:Body>\n' +
        '</s:Envelope>';

      let result: ParsedXmlTag;

      beforeEach(() => {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        result = unwrapActionResult(xmljs.xml2js(mockResponse) as ParsedXmlTag)!;
      });

      it('should return the correct address id, estate id, organization number and share number', () => {
        expect(toEstateIds(result?.elements || [])).toEqual([
          {
            addressId: 'Fake Address ID',
            estateId: 'Fake Estate ID',
            organizationNumber: 'Fake Organization Number',
            shareNumber: 'Fake Share Number',
            matrix: {
              bnr: 205,
              fnr: 0,
              gnr: 34,
              knr: 3005,
              snr: 14,
            },
          },
        ]);
      });
    });

    describe('given a response without share information', () => {
      const mockResponse =
        '<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:a="http://www.w3.org/2005/08/addressing" xmlns:u="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">\n' +
        '    <s:Header>\n' +
        '        <a:Action s:mustUnderstand="1">http://tempuri.org/IPublicInformationRealtime/GetEstatesByOwnerIdentityResponse</a:Action>\n' +
        '        <o:Security s:mustUnderstand="1" xmlns:o="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">\n' +
        '            <u:Timestamp u:Id="_0">\n' +
        '                <u:Created>2020-01-01T00:00:00.000Z</u:Created>\n' +
        '                <u:Expires>2020-01-01T00:00:00.000Z</u:Expires>\n' +
        '            </u:Timestamp>\n' +
        '        </o:Security>\n' +
        '    </s:Header>\n' +
        '    <s:Body>\n' +
        '        <GetEstatesByOwnerIdentityResponse xmlns="http://tempuri.org/">\n' +
        '            <GetEstatesByOwnerIdentityResult xmlns:b="http://schemas.datacontract.org/2004/07/EV.InformationServices.ExternalApi.DTO" xmlns:i="http://www.w3.org/2001/XMLSchema-instance">\n' +
        '                <b:Estates>\n' +
        '                    <b:RealEstateIdentificationInfo>\n' +
        '                        <b:EV_AddressID>Fake Address ID</b:EV_AddressID>\n' +
        '                        <b:EV_EstateID>Fake Estate ID</b:EV_EstateID>\n' +
        '                        <b:HousingCooperatativeShareIdentification i:nil="true"/>\n' +
        '                        <b:Cadastre>\n' +
        '                            <b:BNr>205</b:BNr>\n' +
        '                            <b:FNr>0</b:FNr>\n' +
        '                            <b:GNr>34</b:GNr>\n' +
        '                            <b:KNr>3005</b:KNr>\n' +
        '                            <b:SNr>14</b:SNr>\n' +
        '                        </b:Cadastre>\n' +
        '                    </b:RealEstateIdentificationInfo>\n' +
        '                </b:Estates>\n' +
        '                <b:Status>\n' +
        '                    <b:Description>Ok</b:Description>\n' +
        '                    <b:Key>0</b:Key>\n' +
        '                </b:Status>\n' +
        '            </GetEstatesByOwnerIdentityResult>\n' +
        '        </GetEstatesByOwnerIdentityResponse>\n' +
        '    </s:Body>\n' +
        '</s:Envelope>';

      let result: ParsedXmlTag;

      beforeEach(() => {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        result = unwrapActionResult(xmljs.xml2js(mockResponse) as ParsedXmlTag)!;
      });

      it('should return the correct address id, estate id and empty organization number or share number', () => {
        expect(toEstateIds(result?.elements || [])).toEqual([
          {
            addressId: 'Fake Address ID',
            estateId: 'Fake Estate ID',
            organizationNumber: null,
            shareNumber: null,
            matrix: {
              bnr: 205,
              fnr: 0,
              gnr: 34,
              knr: 3005,
              snr: 14,
            },
          },
        ]);
      });
    });
  });
});
