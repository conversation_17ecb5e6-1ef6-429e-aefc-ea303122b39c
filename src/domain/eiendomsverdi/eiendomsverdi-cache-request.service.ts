import { differenceInHours } from 'date-fns';
import { pick } from 'lodash';
import type { ConfigObject } from '../../config';
import type {
  HousingCooperativeShareIdentification,
  LandIdentificationMatrix,
} from '../land-identification/LandIdentificationMatrix';
import { CacheTypeEnum } from './eiendomsverdi-estate-cache';
import type { EiendomsverdiEstateCacheRepository } from './eiendomsverdi-estate-cache.repository';
import type { FetchCachedEstateData } from './eiendomsverdi-estate-cache.service';
import { hashQuery } from './eiendomsverdi-estate-cache.service';
import type {
  EiendomsverdiRestApiClient,
  EVEstateAttributesResponse,
  EVEstateSaleHistoryResponse,
  EVEstateValuationResponse,
} from './EiendomsverdiApiClient';
import type { ParsedXmlTag, SoapApiClient } from './SoapApiClient';

function copyAndSort<T>(obj: T): T {
  return (Object.keys(obj) as (keyof T)[]).sort().reduce<T>((sortedObj, key) => {
    return {
      ...sortedObj,
      [key]: obj[key],
    };
    //eslint-disable-next-line @typescript-eslint/prefer-reduce-type-parameter
  }, {} as T);
}

export type CacheRequestService = (params: { cacheType: CacheTypeEnum; query: string }) => Promise<ParsedXmlTag | null>;
type Factory = (params: {
  soapApiClient: SoapApiClient;
  config: ConfigObject;
  fetchCachedEstateData: FetchCachedEstateData;
}) => CacheRequestService;
export const cacheRequestServiceFactory: Factory = ({ soapApiClient, config, fetchCachedEstateData }) => async ({
  cacheType,
  query,
}) => {
  const types = JSON.parse(config.eiendomsverdi.cacheableTypes) as CacheTypeEnum[];
  if (!types.includes(cacheType)) {
    const response = await soapApiClient.request(cacheType, query);
    if (!response) {
      return null;
    }
    return response;
  }
  const cachedResult = await fetchCachedEstateData({ cacheType, query });
  return cachedResult;
};

export type FetchRestCachedEstateData = (params: {
  cacheType: CacheTypeEnum;
  matrix?: LandIdentificationMatrix;
  coopNumbers?: HousingCooperativeShareIdentification;
}) => Promise<EVEstateSaleHistoryResponse[] | EVEstateValuationResponse | EVEstateAttributesResponse | null>;
type RestFactory = (params: {
  evRestApiClient: EiendomsverdiRestApiClient;
  evConfig: ConfigObject['eiendomsverdi'];
  cacheRepository: EiendomsverdiEstateCacheRepository;
}) => FetchRestCachedEstateData;
export const cacheRestRequestServiceFactory: RestFactory = ({ evRestApiClient, evConfig, cacheRepository }) => async ({
  cacheType,
  matrix,
  coopNumbers,
}) => {
  const cacheableTypes = JSON.parse(evConfig.cacheableTypes) as CacheTypeEnum[];
  if (!cacheableTypes.includes(cacheType)) {
    return restApiResponseForCacheType(cacheType, evRestApiClient, matrix, coopNumbers);
  }

  // these are intended because in Mongo anything can be added to the matrix itself
  const stringifiedMatrix = matrix
    ? JSON.stringify(copyAndSort<LandIdentificationMatrix>(pick(matrix, ['knr', 'gnr', 'bnr', 'fnr', 'snr'])))
    : '';
  const stringifiedCoopNumbers = coopNumbers
    ? JSON.stringify(
        copyAndSort<HousingCooperativeShareIdentification>(pick(coopNumbers, ['organizationNumber', 'shareNumber'])),
      )
    : '';
  const hashedQuery = hashQuery(`${cacheType}${stringifiedMatrix}${stringifiedCoopNumbers}`);
  const cachedResult = await cacheRepository.get(hashedQuery);
  const now = new Date();
  if (!cachedResult || !cachedResult?.lastSynced) {
    const response = await restApiResponseForCacheType(cacheType, evRestApiClient, matrix, coopNumbers);
    if (!response) {
      return null;
    }
    await cacheRepository.upsert({
      query: stringifiedMatrix || stringifiedCoopNumbers,
      cacheType,
      lastSynced: now,
      data: response,
      queryHash: hashedQuery,
    });
    return response;
  }

  const isTimedOut = differenceInHours(now, cachedResult.lastSynced) > evConfig.cacheTimeoutHours;

  if (!isTimedOut) {
    return cachedResult?.data as EVEstateSaleHistoryResponse[] | EVEstateValuationResponse;
  }

  const response = await restApiResponseForCacheType(cacheType, evRestApiClient, matrix, coopNumbers);
  if (!response) {
    const isSuperTimedOut = differenceInHours(now, cachedResult.lastSynced) > evConfig.cacheFallbackTimeoutHours;
    if (!isSuperTimedOut) {
      return cachedResult?.data as EVEstateSaleHistoryResponse[] | EVEstateValuationResponse;
    }
    return null;
  }
  const updatedCache = await cacheRepository.update({
    id: cachedResult?.id,
    updates: {
      data: response,
      lastSynced: now,
    },
  });
  // if left side is null or undefined return null
  return (updatedCache?.data as EVEstateSaleHistoryResponse[] | EVEstateValuationResponse) ?? null;
};

const restApiResponseForCacheType = async (
  cacheType: CacheTypeEnum,
  api: EiendomsverdiRestApiClient,
  matrix?: LandIdentificationMatrix,
  coopNumbers?: HousingCooperativeShareIdentification,
): Promise<EVEstateSaleHistoryResponse[] | EVEstateValuationResponse | EVEstateAttributesResponse | null> => {
  switch (cacheType) {
    case CacheTypeEnum.GET_ESTATE_SALE_HISTORY:
      if (!matrix) {
        return null;
      }
      return api.getSaleHistory(matrix);
    case CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY:
      if (!coopNumbers) {
        return null;
      }
      return api.getHousingCooperativeSaleHistory(coopNumbers);
    case CacheTypeEnum.GET_MARKET_ESTIMATE:
      if (!matrix) {
        return null;
      }
      return api.getMarketEstimate(matrix);
    case CacheTypeEnum.GET_HOUSING_COOPERATIVE_MARKET_ESTIMATE:
      if (!coopNumbers) {
        return null;
      }
      return api.getHousingCooperativeMarketEstimate(coopNumbers);
    case CacheTypeEnum.GET_EXTENDED_ESTATE_INFO:
      if (!matrix) {
        return null;
      }
      return api.getEstateAttributes(matrix);
    case CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_INFORMATION:
      if (!coopNumbers) {
        return null;
      }
      return api.getHousingCooperativeEstateAttributes(coopNumbers);
    default:
      return null;
  }
};
