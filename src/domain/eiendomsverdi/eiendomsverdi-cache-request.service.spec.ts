import type { ConfigObject } from '../../config';
import type { ParsedXmlTag, SoapApiClient } from './SoapApiClient';
import type { CacheRequestService } from './eiendomsverdi-cache-request.service';
import { cacheRequestServiceFactory } from './eiendomsverdi-cache-request.service';
import { CacheTypeEnum } from './eiendomsverdi-estate-cache';

const mockSoapApiClient = {
  request: jest.fn(),
} as SoapApiClient;
const mockConfig = {
  eiendomsverdi: {
    cacheableTypes: '["GetHousingCooperativeShareSaleHistory","GetEstateSaleHistory","GetMarketEstimate"]',
  },
} as ConfigObject;
describe('cacheRequestService', () => {
  it('should return cached result if cache type is in config', async () => {
    const data = {
      elements: [],
      name: '',
      text: '',
    };
    const cacheRequestService: CacheRequestService = cacheRequestServiceFactory({
      soapApiClient: mockSoapApiClient,
      config: mockConfig,
      fetchCachedEstateData: jest.fn().mockReturnValue(data),
    });

    const result = await cacheRequestService({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toBe(data);
  });
  it('should return api result if cache type is not in config', async () => {
    const data = {
      elements: [],
      name: '',
      text: '',
    };
    const mockInputData = {
      elements: [],
      name: 'kekw',
      text: 'asdf',
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockResolvedValue(mockInputData as ParsedXmlTag),
    };
    const cacheRequestService: CacheRequestService = cacheRequestServiceFactory({
      soapApiClient: soapApiClient,
      config: mockConfig,
      fetchCachedEstateData: jest.fn().mockReturnValue(data),
    });

    const result = await cacheRequestService({
      cacheType: CacheTypeEnum.GET_EXTENDED_ESTATE_INFO,
      query: '',
    });
    expect(result).toBe(mockInputData);
  });
  it('should return null if cache type is not in config and api request fails', async () => {
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockResolvedValue(null),
    };
    const cacheRequestService: CacheRequestService = cacheRequestServiceFactory({
      soapApiClient: soapApiClient,
      config: mockConfig,
      fetchCachedEstateData: jest.fn().mockReturnValue(null),
    });
    const result = await cacheRequestService({
      cacheType: CacheTypeEnum.GET_EXTENDED_ESTATE_INFO,
      query: '',
    });
    expect(result).toBeNull();
  });
});
