import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import * as jwt from 'jsonwebtoken';
import urljoin from 'url-join';
import xmljs from 'xml-js';
import { logger } from '../../logger';
import type {
  HousingCooperativeShareIdentification,
  LandIdentificationMatrix,
} from '../land-identification/LandIdentificationMatrix';
import type { EiendomsverdiAuditRepository } from './EiendomsverdiAuditRepository';
import type { ParsedXmlTag, SoapApiClient } from './SoapApiClient';

export type EVEstateSaleHistoryResponse = {
  askingPrice: number | null;
  salesPrice: number | null;
  judicialTransferDate: string | null;
  registrationDate: string | null;
  saleDate: string | null;
  commonDebt: number | null;
  surveyorsValuation: number | null;
  estateAgent: string | null;
  isSitePrice: boolean;
  isDeviantPrice: boolean;
  deviantPriceReason: string | null;
};

export type EVEstateValuationResponse = {
  estimate: number;
  estimateLow: number;
  estimateHigh: number;
};

export type EVOwnerEstateResponse = {
  address: {
    id: number;
    municipality: string;
    postOffice: {
      code: number | null;
      name: string;
    } | null;
    streetLetter: string | null;
    streetName: string;
    streetNumber: number | null;
  };
  cadastre: {
    bNr: number;
    fNr: number;
    gNr: number;
    kNr: number;
    sNr: number;
    municipality: string;
  };
  housingCooperativeShareIdentity: {
    housingCooperative: {
      name: string;
      organizationNumber: number | null;
    };
    shareNumber: number | null;
  } | null;
  location: {
    latitude: number;
    longitude: number;
  } | null;
};

export type EVEstateAttributesResponse = EVOwnerEstateResponse & {
  attributes: {
    buildYear: number | null;
    estateSubType: string | null;
    estateType: string | null;
    facilities:
      | [
          {
            name: string | null;
          },
        ]
      | null;
    floor: number | null;
    grossArea: {
      value: number | null;
    } | null;
    numberOfBedrooms: number | null;
    numberOfBuildings: number | null;
    numberOfFloors: number | null;
    numberOfRooms: number | null;
    plotOwnership: string | null;
    primaryArea: {
      value: number | null;
    } | null;
    realEstateOwnership: string | null;
    usableArea: {
      value: number | null;
    } | null;
  };
  marketScore: {
    area: number | null;
    municipality: number | null;
  } | null;
};

export type EiendomsverdiRestApiClient = {
  getEstatesIdentifiers: (socialSecurityNumber: string) => Promise<EVOwnerEstateResponse[] | null>;
  getEstateAttributes: (matrix: LandIdentificationMatrix) => Promise<EVEstateAttributesResponse | null>;
  getHousingCooperativeEstateAttributes: (
    coopNumbers: HousingCooperativeShareIdentification,
  ) => Promise<EVEstateAttributesResponse | null>;
  getSaleHistory: (matrix: LandIdentificationMatrix) => Promise<EVEstateSaleHistoryResponse[] | null>;
  getHousingCooperativeSaleHistory: (
    coopNumbers: HousingCooperativeShareIdentification,
  ) => Promise<EVEstateSaleHistoryResponse[] | null>;
  getMarketEstimate: (matrix: LandIdentificationMatrix) => Promise<EVEstateValuationResponse | null>;
  getHousingCooperativeMarketEstimate: (
    coopNumbers: HousingCooperativeShareIdentification,
  ) => Promise<EVEstateValuationResponse | null>;
  refreshJwtToken: () => Promise<void>;
  getJwtToken: () => string | undefined;
};

export const createEiendomsverdiRestApiClient = ({
  axios,
  tokenUrl,
  clientId,
  clientSecret,
  apiUrl,
  estimateApiUrl,
}: {
  axios: AxiosInstance;
  tokenUrl: string;
  clientId: string;
  clientSecret: string;
  apiUrl: string;
  estimateApiUrl: string;
}): EiendomsverdiRestApiClient => {
  let jwtToken: string;

  const getJwtToken = (): string | undefined => {
    return jwtToken;
  };

  const refreshJwtToken = async (): Promise<void> => {
    const isTokenFresh =
      jwtToken &&
      (jwt.decode(jwtToken) as jwt.JwtPayload).exp &&
      (jwt.decode(jwtToken) as jwt.JwtPayload).exp! > new Date().getTime() / 1000;
    if (isTokenFresh) {
      return;
    }
    const tokenOptions: AxiosRequestConfig = {
      method: 'POST',
      url: tokenUrl,
      headers: { 'content-type': 'application/x-www-form-urlencoded' },
      data: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: clientId,
        client_secret: clientSecret,
      }),
    };

    try {
      const { data } = await axios.request<{ access_token: string }>(tokenOptions);
      jwtToken = data.access_token;
    } catch (e) {
      logger.error(e as Error);
    }
  };

  return {
    getEstatesIdentifiers: async (ssn) => {
      await refreshJwtToken();
      try {
        // This typing of axios.get is intended, their endpoint wraps the response in a data object
        const res = await axios.get<{ data: EVOwnerEstateResponse[] }>(urljoin(apiUrl, 'Owners', ssn, 'RealEstates'), {
          headers: { Authorization: `Bearer ${jwtToken}` },
        });
        return res.data.data;
      } catch (e) {
        logger.error(e as Error);
        return null;
      }
    },
    getEstateAttributes: async ({ knr, gnr, bnr, fnr, snr }) => {
      await refreshJwtToken();
      try {
        const res = await axios.get<{ data: EVEstateAttributesResponse }>( // This typing of axios.get is intended, their endpoint wraps the response in a data object
          urljoin(
            apiUrl,
            'RealEstates',
            knr.toString(),
            gnr.toString(),
            bnr.toString(),
            fnr.toString(),
            snr.toString(),
            'attributes',
          ),
          { headers: { Authorization: `Bearer ${jwtToken}` } },
        );
        return res.data.data;
      } catch (e) {
        logger.error(e as Error);
        return null;
      }
    },
    getHousingCooperativeEstateAttributes: async ({ organizationNumber, shareNumber }) => {
      await refreshJwtToken();
      if (!organizationNumber || !shareNumber) {
        logger.error(
          `Something is not provided for cooperative estate attributes: ${organizationNumber} ${shareNumber}`,
        );
        return null;
      }
      try {
        const res = await axios.get<{ data: EVEstateAttributesResponse }>( // This typing of axios.get is intended, their endpoint wraps the response in a data object
          urljoin(apiUrl, 'RealEstates', organizationNumber, shareNumber, 'attributes'),
          { headers: { Authorization: `Bearer ${jwtToken}` } },
        );
        return res.data.data;
      } catch (e) {
        logger.error(e as Error);
        return null;
      }
    },
    getSaleHistory: async ({ knr, gnr, bnr, fnr, snr }) => {
      await refreshJwtToken();
      try {
        const res = await axios.get<{ data: EVEstateSaleHistoryResponse[] }>( // This typing of axios.get is intended, their endpoint wraps the response in a data object
          urljoin(
            apiUrl,
            'RealEstates',
            knr.toString(),
            gnr.toString(),
            bnr.toString(),
            fnr.toString(),
            snr.toString(),
            'sales',
          ),
          { headers: { Authorization: `Bearer ${jwtToken}` } },
        );
        return res.data.data;
      } catch (e) {
        logger.error(e as Error);
        return null;
      }
    },
    getHousingCooperativeSaleHistory: async ({ organizationNumber, shareNumber }) => {
      await refreshJwtToken();
      try {
        if (!organizationNumber || !shareNumber) {
          logger.error(`Something is not provided for cooperative sale history: ${organizationNumber} ${shareNumber}`);
          return null;
        }
        const res = await axios.get<{ data: EVEstateSaleHistoryResponse[] }>( // This typing of axios.get is intended, their endpoint wraps the response in a data object
          urljoin(apiUrl, 'RealEstates', organizationNumber, shareNumber, 'sales'),
          { headers: { Authorization: `Bearer ${jwtToken}` } },
        );
        return res.data.data;
      } catch (e) {
        logger.error(e as Error);
        return null;
      }
    },
    getMarketEstimate: async ({ knr, gnr, bnr, fnr, snr }) => {
      await refreshJwtToken();
      try {
        const res = await axios.get<{ data: EVEstateValuationResponse }>( // This typing of axios.get is intended, their endpoint wraps the response in a data object
          urljoin(
            estimateApiUrl,
            'RealEstates',
            knr.toString(),
            gnr.toString(),
            bnr.toString(),
            fnr.toString(),
            snr.toString(),
            'MarketEstimate',
          ),
          { headers: { Authorization: `Bearer ${jwtToken}` } },
        );
        return res.data.data;
      } catch (e) {
        logger.error(e as Error);
        return null;
      }
    },
    getHousingCooperativeMarketEstimate: async ({ organizationNumber, shareNumber }) => {
      await refreshJwtToken();
      try {
        if (!organizationNumber || !shareNumber) {
          logger.error(
            `Something is not provided for cooperative market estimate: ${organizationNumber} ${shareNumber}`,
          );
          return null;
        }
        const res = await axios.get<{ data: EVEstateValuationResponse }>( // This typing of axios.get is intended, their endpoint wraps the response in a data object
          urljoin(estimateApiUrl, 'RealEstates', organizationNumber, shareNumber, 'MarketEstimate'),
          { headers: { Authorization: `Bearer ${jwtToken}` } },
        );
        return res.data.data;
      } catch (e) {
        logger.error(e as Error);
        return null;
      }
    },
    refreshJwtToken,
    getJwtToken,
  };
};

export const createEiendomsverdiSoapApiClient = (
  axios: AxiosInstance,
  apiUrl: string,
  user: string,
  password: string,
  serviceName: string,
  eiendomsverdiAuditRepository: EiendomsverdiAuditRepository,
): SoapApiClient => {
  const request = async (action: string, body: string, userID?: string): Promise<ParsedXmlTag | null> => {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    const headers = { 'Content-Type': 'application/soap+xml' };
    const data = makeXmlRequest(action, body);

    try {
      const result = await axios({ url: apiUrl, method: 'POST', headers, data });

      await eiendomsverdiAuditRepository.create(result.data as string, userID);

      const actionResult = unwrapActionResult(xmljs.xml2js(result.data as string) as ParsedXmlTag);
      if (!actionResult || !actionResult.elements || !actionResult.elements[0]) {
        return null;
      }

      return actionResult;
    } catch (error) {
      return null;
    }
  };

  const makeXmlRequest = (action: string, body: string): string => {
    return `<?xml version="1.0"?>
    <s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:a="http://www.w3.org/2005/08/addressing" xmlns:u="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:tem="http://tempuri.org/" xmlns:ev="http://schemas.datacontract.org/2004/07/EV.InformationServices.ExternalApi.DTO" xmlns:ev1="http://schemas.datacontract.org/2004/07/EV.InformationServices.ExternalApi.DTO.ObjectWrappers">
      <s:Header>
        <a:Action>http://tempuri.org/I${serviceName}/${action}</a:Action>
        <a:To s:mustUnderstand="1">https://test-api.eiendomsverdi.no/${serviceName}.svc</a:To>
        ${makeSecurityHeader()}
      </s:Header>
      <s:Body><${action} xmlns="http://tempuri.org/">${body}</${action}></s:Body>
    </s:Envelope>`;
  };

  const makeSecurityHeader = (): string => {
    return `<o:Security xmlns:o="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
      <o:UsernameToken>
        <o:Username>${user}</o:Username>
        <o:Password>${password}</o:Password>
      </o:UsernameToken>
    </o:Security>`;
  };

  return { request };
};

export const findTagByName = (nameIdentifier: string) => {
  return ({ name }: ParsedXmlTag): boolean => name === nameIdentifier;
};

export const unwrapEnvelopeAndBody = (soapResponse: ParsedXmlTag): ParsedXmlTag => {
  if (!soapResponse.elements) {
    throw new Error('no elements in soapResponse');
  }
  const [envelope] = soapResponse.elements;
  const body = envelope.elements?.find(findTagByName('s:Body'));

  if (!body || !body.elements) {
    throw new Error('no body or elements in soap response');
  }
  return body.elements[0];
};

export const unwrapActionResult = (soapResponse: ParsedXmlTag): ParsedXmlTag | null => {
  const response = unwrapEnvelopeAndBody(soapResponse);
  if (!response || !response.elements || !response.elements[0]) {
    return null;
  }
  return response.elements[0];
};
