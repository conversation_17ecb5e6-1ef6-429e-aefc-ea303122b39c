import { createHash } from 'crypto';
import { differenceInHours } from 'date-fns';
import type { ConfigObject } from '../../config';
import type { CacheTypeEnum } from './eiendomsverdi-estate-cache';
import type { EiendomsverdiEstateCacheRepository } from './eiendomsverdi-estate-cache.repository';
import type { ParsedXmlTag, SoapApiClient } from './SoapApiClient';

export type FetchCachedEstateData = (params: {
  cacheType: CacheTypeEnum;
  query: string;
}) => Promise<ParsedXmlTag | null>;
type Factory = (params: {
  cacheRepository: EiendomsverdiEstateCacheRepository;
  soapApiClient: SoapApiClient;
  config: ConfigObject;
}) => FetchCachedEstateData;

export const fetchCachedEstateDataFactory: Factory = ({ cacheRepository, soapApiClient, config }) => async ({
  cacheType,
  query,
}) => {
  const hashedQuery = hashQuery(query);
  const cachedResult = await cacheRepository.get(hashedQuery);
  const now = new Date();
  if (!cachedResult || !cachedResult?.lastSynced) {
    const response = await soapApiClient.request(cacheType, query);
    if (!response) {
      return null;
    }
    await cacheRepository.upsert({
      query,
      cacheType,
      lastSynced: now,
      data: response,
      queryHash: hashedQuery,
    });
    return response;
  }
  const isTimedOut = differenceInHours(now, cachedResult.lastSynced) > config.eiendomsverdi.cacheTimeoutHours;

  if (isTimedOut) {
    const response = await soapApiClient.request(cacheType, query);
    if (!response) {
      const isSuperTimedOut =
        differenceInHours(now, cachedResult.lastSynced) > config.eiendomsverdi.cacheFallbackTimeoutHours;
      if (!isSuperTimedOut) {
        return cachedResult?.data as ParsedXmlTag;
      }
      return null;
    }
    const updatedCache = await cacheRepository.update({
      id: cachedResult?.id,
      updates: {
        data: response,
        lastSynced: now,
      },
    });
    // if left side is null or undefined return null
    return (updatedCache?.data as ParsedXmlTag) ?? null;
  }
  return cachedResult?.data as ParsedXmlTag;
};
export const hashQuery = (query: string): string => {
  return createHash('sha256').update(query).digest('hex');
};
