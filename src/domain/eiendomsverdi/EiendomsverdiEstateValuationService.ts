import { isEmpty, isNil } from 'ramda';
import type { Unleash } from 'unleash-client';
import { FeatureFlag } from '../../config';
import type {
  HousingCooperativeShareIdentification,
  LandIdentificationMatrix,
} from '../land-identification/LandIdentificationMatrix';
import type { EstateSalesHistory } from '../valuation/EstateSalesHistoryEntry';
import type { ValuationEstate } from '../valuation/ValuationEstate';
import type { ValuationService } from '../valuation/ValuationService';
import type {
  EVEstateAttributesResponse,
  EVEstateSaleHistoryResponse,
  EVEstateValuationResponse,
  EVOwnerEstateResponse,
  EiendomsverdiRestApiClient,
} from './EiendomsverdiApiClient';
import type { CreatableEstate } from './EiendomsverdiEstate';
import { createEiendomsverdiEstate } from './EiendomsverdiEstate';
import type { ParsedXmlTag, SoapApiClient } from './SoapApiClient';
import type { CacheRequestService, FetchRestCachedEstateData } from './eiendomsverdi-cache-request.service';
import { CacheTypeEnum } from './eiendomsverdi-estate-cache';

export const createEiendomsverdiEstateValuationService = (
  publicClient: SoapApiClient,
  cacheRequestService: CacheRequestService,
  evRestApiClient: EiendomsverdiRestApiClient,
  eiendomsverdiCacheRestRequestService: FetchRestCachedEstateData,
  unleash: Unleash,
): ValuationService => {
  const isRestEnabled = (): boolean => {
    return unleash.isEnabled(FeatureFlag.USE_EIENDOMSVERDI_REST_API, {});
  };

  const getSaleHistory = async (
    baseLandIdentificationMatrix: LandIdentificationMatrix,
    organizationNumber: string | null,
    shareNumber: string | null,
  ): Promise<EstateSalesHistory> => {
    if (isEmpty(baseLandIdentificationMatrix) || isNil(baseLandIdentificationMatrix)) {
      return [];
    }

    if (isRestEnabled()) {
      let histories: EVEstateSaleHistoryResponse[] | null;
      if (organizationNumber && shareNumber) {
        histories = (await eiendomsverdiCacheRestRequestService({
          cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
          coopNumbers: { organizationNumber, shareNumber },
        })) as EVEstateSaleHistoryResponse[] | null;
      } else {
        histories = (await eiendomsverdiCacheRestRequestService({
          cacheType: CacheTypeEnum.GET_ESTATE_SALE_HISTORY,
          matrix: baseLandIdentificationMatrix,
        })) as EVEstateSaleHistoryResponse[] | null;
      }
      return toEstateSaleHistoryFromRest(histories);
    }

    const estateIdentifier = await getEstateIdentifier(baseLandIdentificationMatrix);
    if (!estateIdentifier) {
      return [];
    }
    if (organizationNumber && shareNumber) {
      // query fields should be in the exact order:
      //  1) organizationNumber
      //  2) shareNumber
      const query = [
        `<organizationNumber>${organizationNumber}</organizationNumber>`,
        `<shareNumber>${shareNumber}</shareNumber>`,
      ].join('');

      const result = await cacheRequestService({
        query,
        cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      });
      return result?.elements ? toEstateSharedSaleHistory(result?.elements) : [];
    }
    // query fields should be in the exact order:
    //  1) estateID
    //  2) addressID
    const query = [
      `<estateID>${estateIdentifier.estateId}</estateID>`,
      `<addressID>${estateIdentifier.addressId}</addressID>`,
    ].join('');
    const result = await cacheRequestService({
      query,
      cacheType: CacheTypeEnum.GET_ESTATE_SALE_HISTORY,
    });
    return toEstateSaleHistory(result?.elements || []);
  };

  const getEstateEvaluation = async (
    baseLandIdentificationMatrix: LandIdentificationMatrix,
    housingCooperativeShareIdentification: HousingCooperativeShareIdentification,
  ): Promise<ValuationObject | null> => {
    if (isEmpty(baseLandIdentificationMatrix) || isNil(baseLandIdentificationMatrix)) {
      return null;
    }

    if (isRestEnabled()) {
      let valuation: EVEstateValuationResponse;
      if (
        housingCooperativeShareIdentification.organizationNumber &&
        housingCooperativeShareIdentification.shareNumber
      ) {
        valuation = (await eiendomsverdiCacheRestRequestService({
          cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_MARKET_ESTIMATE,
          coopNumbers: housingCooperativeShareIdentification,
        })) as EVEstateValuationResponse;
      } else {
        valuation = (await eiendomsverdiCacheRestRequestService({
          cacheType: CacheTypeEnum.GET_MARKET_ESTIMATE,
          matrix: baseLandIdentificationMatrix,
        })) as EVEstateValuationResponse;
      }
      return toEvaluationRest(valuation);
    }
    if (housingCooperativeShareIdentification.organizationNumber && housingCooperativeShareIdentification.shareNumber) {
      // query fields should be in the exact order:
      //  1) identification
      //   a) BNr
      //   b) FNr
      //   c) GNr
      //   d) KNr
      //   e) SNr
      //  2) housingCooperativeShareIdentification
      //   a) OrganizationNumber
      //   b) ShareNumber
      const dataPayload = [
        `<tem:estateIdentification>`,
        `<ev:Identification>`,
        `<ev:BNr>${baseLandIdentificationMatrix?.bnr}</ev:BNr>`,
        `<ev:FNr>${baseLandIdentificationMatrix?.fnr}</ev:FNr>`,
        `<ev:GNr>${baseLandIdentificationMatrix?.gnr}</ev:GNr>`,
        `<ev:KNr>${baseLandIdentificationMatrix?.knr}</ev:KNr>`,
        `<ev:SNr>${baseLandIdentificationMatrix?.snr}</ev:SNr>`,
        `</ev:Identification>`,
        `</tem:estateIdentification>`,
        `<tem:housingCooperativeShareIdentification>`,
        `<ev:OrganizationNumber>${housingCooperativeShareIdentification.organizationNumber}</ev:OrganizationNumber>`,
        `<ev:ShareNumber>${housingCooperativeShareIdentification.shareNumber}</ev:ShareNumber>`,
        `</tem:housingCooperativeShareIdentification>`,
      ].join('');
      const result = await cacheRequestService({
        query: dataPayload,
        cacheType: CacheTypeEnum.GET_MARKET_ESTIMATE,
      });
      return result ? toEvaluation(result) : null;
    }
    // query fields should be in the exact order:
    //  1) BNr
    //  2) FNr
    //  3) GNr
    //  4) KNr
    //  5) SNr
    const requestPayload = [
      `<tem:estateIdentification>`,
      `<ev:Identification>`,
      `<ev:BNr>${baseLandIdentificationMatrix?.bnr}</ev:BNr>`,
      `<ev:FNr>${baseLandIdentificationMatrix?.fnr}</ev:FNr>`,
      `<ev:GNr>${baseLandIdentificationMatrix?.gnr}</ev:GNr>`,
      `<ev:KNr>${baseLandIdentificationMatrix?.knr}</ev:KNr>`,
      `<ev:SNr>${baseLandIdentificationMatrix?.snr}</ev:SNr>`,
      `</ev:Identification>`,
      `</tem:estateIdentification>`,
    ].join('');
    const result = await cacheRequestService({
      query: requestPayload,
      cacheType: CacheTypeEnum.GET_MARKET_ESTIMATE,
    });
    return result ? toEvaluation(result) : null;
  };

  const getEstateIdentifiers = async (
    userID: string,
    socialSecurityNumber: string,
  ): Promise<
    {
      estateId: string;
      addressId: string;
      organizationNumber: string | null;
      shareNumber: string | null;
      matrix: LandIdentificationMatrix;
    }[]
  > => {
    if (isRestEnabled()) {
      const result = await evRestApiClient.getEstatesIdentifiers(socialSecurityNumber);
      return toEstateIdsRest(result || []);
    }
    const requestPayload = `<ownerIdentity>${socialSecurityNumber}</ownerIdentity>`;
    const result = await publicClient.request('GetEstatesByOwnerIdentity', requestPayload, userID);

    return toEstateIds(result?.elements || []);
  };

  const getCreatableEstates = async (userID: string, socialSecurityNumber: string): Promise<CreatableEstate[]> => {
    const estateIdentifiers = await getEstateIdentifiers(userID, socialSecurityNumber);

    return (
      await Promise.all(
        estateIdentifiers.map(async ({ addressId, estateId, organizationNumber, shareNumber, matrix }) => {
          if (isRestEnabled()) {
            let estateWithAttributes: EVEstateAttributesResponse;
            if (organizationNumber && shareNumber) {
              estateWithAttributes = (await eiendomsverdiCacheRestRequestService({
                cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_INFORMATION,
                coopNumbers: { organizationNumber, shareNumber },
              })) as EVEstateAttributesResponse;
            } else {
              estateWithAttributes = (await eiendomsverdiCacheRestRequestService({
                cacheType: CacheTypeEnum.GET_EXTENDED_ESTATE_INFO,
                matrix,
              })) as EVEstateAttributesResponse;
            }
            return estateWithAttributes
              ? toCreatableRest(estateId, addressId, organizationNumber, shareNumber, estateWithAttributes, matrix)
              : null;
          }

          // query fields should be in the exact order:
          //  1) addressID
          //  2) estateID
          const query = [`<addressID>${addressId}</addressID>`, `<estateID>${estateId}</estateID>`].join('');
          if (organizationNumber && shareNumber) {
            // query fields should be in the exact order:
            //  1) organizationNumber
            //  2) shareNumber
            const queryShared = [
              `<organizationNumber>${organizationNumber}</organizationNumber>`,
              `<shareNumber>${shareNumber}</shareNumber>`,
            ].join('');
            const result = await cacheRequestService({
              cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_INFORMATION,
              query: queryShared,
            });
            return result ? toCreatable(estateId, addressId, organizationNumber, shareNumber, result) : null;
          } else {
            const result = await cacheRequestService({
              cacheType: CacheTypeEnum.GET_EXTENDED_ESTATE_INFO,
              query,
            });
            return result ? toCreatable(estateId, addressId, organizationNumber, shareNumber, result) : null;
          }
        }),
      )
    ).filter((result) => result !== null) as CreatableEstate[];
  };

  const toEvaluation = (getEstatesResult: ParsedXmlTag): ValuationObject | null => {
    const marketEstimateTag = getEstatesResult.elements?.find(findTagByName('b:MarketEstimate'));
    const estimate = getFirstText(marketEstimateTag?.elements?.find(findTagByName('b:Estimate')));

    if (estimate && parseInt(estimate) >= 0) {
      return { valuation: parseInt(estimate), valuationIndex: null };
    }

    return null;
  };

  const toEvaluationRest = (estimate: EVEstateValuationResponse): ValuationObject | null => {
    if (estimate && estimate.estimate >= 0) {
      return { valuation: estimate.estimate, valuationIndex: null };
    }

    return null;
  };

  const toCreatable = (
    estateId: string,
    addressId: string,
    OrganizationNumber: string | null,
    ShareNumber: string | null,
    getEstatesResult: ParsedXmlTag,
  ): CreatableEstate => {
    const addressTag = getEstatesResult.elements?.find(findTagByName('b:Address'));
    const propertiesTag = getEstatesResult.elements?.find(findTagByName('b:Properties'));
    const address = getFirstText(addressTag?.elements?.find(findTagByName('b:FullAddress')));
    const municipal = getFirstText(addressTag?.elements?.find(findTagByName('b:Municipality')));
    const estateType = getFirstText(propertiesTag?.elements?.find(findTagByName('b:EstateType')));
    const numBedrooms = getFirstText(propertiesTag?.elements?.find(findTagByName('b:NumBedrooms')));
    const livingArea = getFirstText(propertiesTag?.elements?.find(findTagByName('b:LivingArea')));
    const buildYear = getFirstText(propertiesTag?.elements?.find(findTagByName('b:BuildYear')));
    const floor = getFirstText(propertiesTag?.elements?.find(findTagByName('b:Floor')));
    const ownership = getFirstText(propertiesTag?.elements?.find(findTagByName('b:Ownership')));
    const cadastreTag = getEstatesResult.elements?.find(findTagByName('b:Identification'));

    const dto = {
      address,
      municipal,
      addressId,
      estateId,
      estateType,
      numBedrooms,
      livingArea,
      buildYear,
      ownership,
      floor,
      location: cadastreTag?.elements?.reduce(
        (acc, tag): { [key: string]: string } => ({
          ...acc,
          [tag.name.slice(2).toLowerCase()]: parseInt(getFirstText(tag), 10),
        }),
        {},
      ),
      OrganizationNumber,
      ShareNumber,
    };
    return createEiendomsverdiEstate(dto as ValuationEstate);
  };

  const prefixNumber = (num: number | null | undefined): string => {
    if (!num) {
      return '';
    }
    let str = num.toString();
    while (str.length < 4) {
      str = `0${str}`;
    }
    return str;
  };

  const toCreatableRest = (
    estateId: string,
    addressId: string,
    OrganizationNumber: string | null,
    ShareNumber: string | null,
    estateWithAttributes: EVEstateAttributesResponse,
    matrix: LandIdentificationMatrix,
  ): CreatableEstate => {
    const address = estateWithAttributes.address;
    const attributes = estateWithAttributes.attributes;
    const postalCode = prefixNumber(address?.postOffice?.code);
    const dto = {
      address: `${address.streetName} ${address.streetNumber || ''}, ${postalCode} ${address.municipality}`,
      municipal: address.municipality,
      addressId,
      estateId,
      estateType: attributes.estateType,
      numBedrooms: attributes.numberOfBedrooms?.toString(),
      livingArea: attributes.usableArea?.value?.toString(),
      buildYear: attributes.buildYear?.toString(),
      ownership: attributes.realEstateOwnership || attributes.plotOwnership,
      floor: attributes.floor?.toString() || '',
      location: matrix,
      OrganizationNumber,
      ShareNumber,
    };
    return createEiendomsverdiEstate(dto as ValuationEstate);
  };

  const toEstateSaleHistory = (getSaleHistoryResult: ParsedXmlTag[]): EstateSalesHistory => {
    return getSaleHistoryResult
      .map((basicSale) => {
        const date =
          getFirstText(basicSale.elements?.find(findTagByName('b:SoldDate'))) ||
          getFirstText(basicSale.elements?.find(findTagByName('b:PublicRegistrationDate')));
        const price = getFirstText(basicSale.elements?.find(findTagByName('b:SalesPrice')));
        const commonDebt = getFirstText(getSaleHistoryResult[0].elements?.find(findTagByName('b:CommonDebt')));

        return [date, (parseInt(price) + (commonDebt !== '' ? parseInt(commonDebt) : 0)).toString()];
      })
      .filter(([, value]) => !!value)
      .map(([date, value]) => ({ date: date ? new Date(date) : null, value: value }));
  };

  const toEstateSharedSaleHistory = (getSaleHistoryResult: ParsedXmlTag[]): EstateSalesHistory => {
    const date =
      getFirstText(getSaleHistoryResult[0].elements?.find(findTagByName('b:SoldDate'))) ||
      getFirstText(getSaleHistoryResult[0].elements?.find(findTagByName('b:PublicRegistrationDate')));
    const price = getFirstText(getSaleHistoryResult[0].elements?.find(findTagByName('b:SalesPrice')));
    const commonDebt = getFirstText(getSaleHistoryResult[0].elements?.find(findTagByName('b:CommonDebt')));
    return [
      {
        date: date ? new Date(date) : null,
        value: (parseInt(price) + (commonDebt !== '' ? parseInt(commonDebt) : 0)).toString(),
      },
    ];
  };

  const toEstateSaleHistoryFromRest = (histories: EVEstateSaleHistoryResponse[] | null): EstateSalesHistory => {
    return (
      histories
        ?.filter((h) => h.salesPrice)
        .map((h) => {
          const date = h.saleDate || h.registrationDate;
          const price = h.salesPrice || 0;
          const debt = h.commonDebt || 0;
          return {
            date: date ? new Date(date) : null,
            value: (price + debt).toString(),
          };
        }) || []
    );
  };

  const getEstateIdentifier = async (
    baseLandIdentificationMatrix: LandIdentificationMatrix,
  ): Promise<PropertyValuationEstateIdentifier | null> => {
    const query = toEvEstateQuery(baseLandIdentificationMatrix);
    const result = await cacheRequestService({ cacheType: CacheTypeEnum.FIND_ESTATES, query });
    if (!result || !result.elements || !result.elements[0] || !result.elements[0].elements) {
      return null;
    }
    const [firstMatchingIdentifier] = result.elements
      .filter(filterByLandIdentificationMatrix(baseLandIdentificationMatrix))
      .map(({ elements }) => mapToValuationEstateIdentifier(elements));
    if (!firstMatchingIdentifier) {
      return null;
    }
    return { estateId: firstMatchingIdentifier.estateId, addressId: firstMatchingIdentifier.addressId };
  };

  const mapToValuationEstateIdentifier = (elements: ParsedXmlTag[] | undefined): PropertyValuationEstateIdentifier => {
    const estateId = getFirstText((elements || []).find(findTagByName('b:EV_EstateID')));
    const addressId = getFirstText((elements || []).find(findTagByName('b:EV_AddressID')));
    return { estateId, addressId };
  };

  const filterByLandIdentificationMatrix = (baseLandIdentificationMatrix: LandIdentificationMatrix) => {
    return ({ elements }: ParsedXmlTag) => {
      const identificationTag = (elements || []).find(findTagByName('b:Identification'));
      const bnr = getFirstText((identificationTag?.elements || []).find(findTagByName('b:BNr')));
      const fnr = getFirstText((identificationTag?.elements || []).find(findTagByName('b:FNr')));
      const gnr = getFirstText((identificationTag?.elements || []).find(findTagByName('b:GNr')));
      const knr = getFirstText((identificationTag?.elements || []).find(findTagByName('b:KNr')));
      const snr = getFirstText((identificationTag?.elements || []).find(findTagByName('b:SNr')));
      return (
        bnr === baseLandIdentificationMatrix.bnr.toString() &&
        fnr === baseLandIdentificationMatrix.fnr.toString() &&
        gnr === baseLandIdentificationMatrix.gnr.toString() &&
        knr === baseLandIdentificationMatrix.knr.toString() &&
        snr === baseLandIdentificationMatrix.snr.toString()
      );
    };
  };

  return { getCreatableEstates, getEstateEvaluation, getSaleHistory };
};

type PropertyValuationEstateIdentifier = {
  addressId: string;
  estateId: string;
};

const findTagByName = (nameIdentifier: string) => {
  return ({ name }: { name: string }) => name === nameIdentifier;
};

const toEvEstateQuery = (baseLandIdentificationMatrix: LandIdentificationMatrix): string => {
  // query fields should be in the exact order:
  //  1) KNr
  //  2) GNr
  //  3) BNr
  //  4) FNr
  //  5) SNr
  return `
      <KNr>${baseLandIdentificationMatrix?.knr}</KNr>
      <GNr>${baseLandIdentificationMatrix?.gnr}</GNr>
      <BNr>${baseLandIdentificationMatrix?.bnr}</BNr>
      <FNr>${baseLandIdentificationMatrix?.fnr}</FNr>
      <SNr>${baseLandIdentificationMatrix?.snr}</SNr>
    `;
};

const getFirstText = (tag?: ParsedXmlTag): string => {
  return tag?.elements?.find(Boolean)?.text || '';
};

export const toEstateIds = (
  getEstatesResult: ParsedXmlTag[],
): {
  estateId: string;
  addressId: string;
  organizationNumber: string | null;
  shareNumber: string | null;
  matrix: LandIdentificationMatrix;
}[] => {
  return (
    getEstatesResult.find(findTagByName('b:Estates'))?.elements?.map((estateTag) => {
      const addressId = getFirstText(estateTag.elements?.find(findTagByName('b:EV_AddressID')));
      const estateId = getFirstText(estateTag.elements?.find(findTagByName('b:EV_EstateID')));
      // Dont fix typo its actually like this in the API
      const shareIdentification = estateTag.elements?.find(findTagByName('b:HousingCooperatativeShareIdentification'));
      const organizationNumber = getFirstText(
        shareIdentification?.elements?.find(findTagByName('b:OrganizationNumber')),
      );
      const shareNumber = getFirstText(shareIdentification?.elements?.find(findTagByName('b:ShareNumber')));

      const cadastre = estateTag.elements?.find(findTagByName('b:Cadastre'));
      const bnr = getFirstText(cadastre?.elements?.find(findTagByName('b:BNr')));
      const fnr = getFirstText(cadastre?.elements?.find(findTagByName('b:FNr')));
      const gnr = getFirstText(cadastre?.elements?.find(findTagByName('b:GNr')));
      const knr = getFirstText(cadastre?.elements?.find(findTagByName('b:KNr')));
      const snr = getFirstText(cadastre?.elements?.find(findTagByName('b:SNr')));
      return {
        addressId,
        estateId,
        organizationNumber: organizationNumber.length > 0 ? organizationNumber : null,
        shareNumber: shareNumber.length > 0 ? shareNumber : null,
        matrix: {
          bnr: parseInt(bnr),
          fnr: parseInt(fnr),
          gnr: parseInt(gnr),
          knr: parseInt(knr),
          snr: parseInt(snr),
        },
      };
    }) || []
  );
};

const toEstateIdsRest = (
  estateInfos: EVOwnerEstateResponse[],
): {
  estateId: string;
  addressId: string;
  organizationNumber: string | null;
  shareNumber: string | null;
  matrix: LandIdentificationMatrix;
}[] => {
  return estateInfos.map((i) => ({
    addressId: i.address.id.toString(),
    estateId: '',
    organizationNumber: i.housingCooperativeShareIdentity?.housingCooperative?.organizationNumber?.toString() || null,
    shareNumber: i.housingCooperativeShareIdentity?.shareNumber?.toString() || null,
    matrix: {
      knr: i.cadastre.kNr,
      bnr: i.cadastre.bNr,
      fnr: i.cadastre.fNr,
      gnr: i.cadastre.gNr,
      snr: i.cadastre.sNr,
    },
  }));
};

export type ValuationObject = {
  valuation: number | null;
  valuationIndex: number | null;
};
