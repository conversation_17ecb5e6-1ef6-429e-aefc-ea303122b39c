import { sub } from 'date-fns';
import type { ConfigObject } from '../../config';
import { CacheTypeEnum } from './eiendomsverdi-estate-cache';
import type { EiendomsverdiEstateCacheRepository } from './eiendomsverdi-estate-cache.repository';
import type { FetchCachedEstateData } from './eiendomsverdi-estate-cache.service';
import { fetchCachedEstateDataFactory } from './eiendomsverdi-estate-cache.service';
import type { ParsedXmlTag, SoapApiClient } from './SoapApiClient';

const mockCacheRepository = {
  get: jest.fn(),
  update: jest.fn(),
  upsert: jest.fn(),
} as EiendomsverdiEstateCacheRepository;
const mockSoapApiClient = {
  request: jest.fn(),
} as SoapApiClient;
const mockConfig = {
  eiendomsverdi: {
    cacheTimeoutHours: 24,
    cacheFallbackTimeoutHours: 30 * 24,
  },
} as ConfigObject;
describe('fetchCachedEstateData', () => {
  it('should return null in case of no cached result and failed response', async () => {
    const cacheRepo: EiendomsverdiEstateCacheRepository = {
      ...mockCacheRepository,
      get: jest.fn().mockReturnValue(null),
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockReturnValue(null),
    };
    const fetchCachedEstateData: FetchCachedEstateData = fetchCachedEstateDataFactory({
      cacheRepository: cacheRepo,
      soapApiClient: soapApiClient,
      config: mockConfig,
    });
    const result = await fetchCachedEstateData({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toBeNull();
  });
  it('should save and return the api response in case of response from the API', async () => {
    const data = {
      elements: [],
      name: 'kekw',
      text: 'asdf',
    };
    const upsert = jest.fn();
    const cacheRepo: EiendomsverdiEstateCacheRepository = {
      ...mockCacheRepository,
      get: jest.fn().mockReturnValue(null),
      upsert,
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockResolvedValue(data as ParsedXmlTag),
    };
    const fetchCachedEstateData: FetchCachedEstateData = fetchCachedEstateDataFactory({
      cacheRepository: cacheRepo,
      soapApiClient: soapApiClient,
      config: mockConfig,
    });
    const result = await fetchCachedEstateData({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toEqual(data);
    expect(upsert).toHaveBeenCalledWith({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
      queryHash: 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855',
      data: data,
      lastSynced: expect.anything() as Date,
    });
  });
  it('should return cached data from db in case of timeout, no response and not more than 30 days have passed', async () => {
    const data = {
      elements: [],
      name: '',
      text: '',
    };
    const syncDate = sub(new Date(), { days: 0, hours: 23 });
    const cacheRepo: EiendomsverdiEstateCacheRepository = {
      ...mockCacheRepository,
      get: jest.fn().mockReturnValue({ data: data, lastSynced: syncDate }),
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockResolvedValue(null),
    };
    const fetchCachedEstateData: FetchCachedEstateData = fetchCachedEstateDataFactory({
      cacheRepository: cacheRepo,
      soapApiClient: soapApiClient,
      config: mockConfig,
    });
    const result = await fetchCachedEstateData({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toEqual(data);
  });
  it('should return null in case of timeout, no response and more than 30 days have passed', async () => {
    const data = {
      elements: [],
      name: '',
      text: '',
    };
    const syncDate = sub(new Date(), { hours: mockConfig.eiendomsverdi.cacheFallbackTimeoutHours + 1 });
    const cacheRepo: EiendomsverdiEstateCacheRepository = {
      ...mockCacheRepository,
      get: jest.fn().mockReturnValue({ data: data, lastSynced: syncDate }),
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockResolvedValue(null),
    };
    const fetchCachedEstateData: FetchCachedEstateData = fetchCachedEstateDataFactory({
      cacheRepository: cacheRepo,
      soapApiClient: soapApiClient,
      config: mockConfig,
    });
    const result = await fetchCachedEstateData({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toEqual(null);
  });
  it('should return updated cache data in case of timeout and non-null response from API', async () => {
    const data = {
      elements: [],
      name: '',
      text: '',
    };
    const syncDate = sub(new Date(), { days: 2, hours: 23 });
    const mockInputData = {
      elements: [],
      name: 'kekw',
      text: 'asdf',
    };
    const update = jest.fn().mockResolvedValue({ data: mockInputData });
    const cacheRepo: EiendomsverdiEstateCacheRepository = {
      ...mockCacheRepository,
      get: jest.fn().mockReturnValue({ data: data, lastSynced: syncDate }),
      update,
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockResolvedValue(mockInputData as ParsedXmlTag),
    };
    const fetchCachedEstateData: FetchCachedEstateData = fetchCachedEstateDataFactory({
      cacheRepository: cacheRepo,
      soapApiClient: soapApiClient,
      config: mockConfig,
    });
    const result = await fetchCachedEstateData({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toEqual(mockInputData);
  });
  it('should return null in case of timeout, non-null response from API and updating of the cache failed', async () => {
    const data = {
      elements: [],
      name: '',
      text: '',
    };
    const syncDate = sub(new Date(), { days: 2, hours: 23 });
    const mockInputData = {
      elements: [],
      name: 'kekw',
      text: 'asdf',
    };
    const update = jest.fn().mockResolvedValue({ mockInputData });
    const cacheRepo: EiendomsverdiEstateCacheRepository = {
      ...mockCacheRepository,
      get: jest.fn().mockReturnValue({ data: data, lastSynced: syncDate }),
      update,
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
      request: jest.fn().mockResolvedValue(mockInputData as ParsedXmlTag),
    };
    const fetchCachedEstateData: FetchCachedEstateData = fetchCachedEstateDataFactory({
      cacheRepository: cacheRepo,
      soapApiClient: soapApiClient,
      config: mockConfig,
    });
    const result = await fetchCachedEstateData({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toEqual(null);
  });
  it('should return the cached data in case of non-null response from the database', async () => {
    const data = {
      elements: [],
      name: '',
      text: '',
    };
    const syncDate = sub(new Date(), { days: 0, hours: 23 });
    const cacheRepo: EiendomsverdiEstateCacheRepository = {
      ...mockCacheRepository,
      get: jest.fn().mockReturnValue({ data: data, lastSynced: syncDate }),
    };
    const soapApiClient: SoapApiClient = {
      ...mockSoapApiClient,
    };
    const fetchCachedEstateData: FetchCachedEstateData = fetchCachedEstateDataFactory({
      cacheRepository: cacheRepo,
      soapApiClient: soapApiClient,
      config: mockConfig,
    });
    const result = await fetchCachedEstateData({
      cacheType: CacheTypeEnum.GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY,
      query: '',
    });
    expect(result).toEqual(data);
  });
});
