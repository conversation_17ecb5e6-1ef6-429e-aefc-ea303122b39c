import type { EiendomsverdiAudit } from './EiendomsverdiAudit';
import { EiendomsverdiAuditModel } from './EiendomsverdiAuditModel';

export type EiendomsverdiAuditRepository = {
  create(response: EiendomsverdiAudit['response'], userID?: EiendomsverdiAudit['userID']): Promise<EiendomsverdiAudit>;
};

export const eiendomsverdiAuditTransformer = (eiendomsverdiAuditModel: EiendomsverdiAuditModel): EiendomsverdiAudit => {
  return {
    id: eiendomsverdiAuditModel.id,
    createdAt: eiendomsverdiAuditModel.createdAt,
    updatedAt: eiendomsverdiAuditModel.updatedAt,
    userID: eiendomsverdiAuditModel.userID,
    response: eiendomsverdiAuditModel.response,
  };
};

export const eiendomsverdiAuditRepositoryFactory = (): EiendomsverdiAuditRepository => {
  const create = async (
    response: EiendomsverdiAudit['response'],
    userID?: EiendomsverdiAudit['userID'],
  ): Promise<EiendomsverdiAudit> => {
    const audit = await EiendomsverdiAuditModel.create({
      userID: userID || null,
      response,
    });

    return eiendomsverdiAuditTransformer(audit);
  };

  return {
    create,
  };
};
