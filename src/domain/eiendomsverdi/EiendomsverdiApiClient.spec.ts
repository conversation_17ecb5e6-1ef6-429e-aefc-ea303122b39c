import axios from 'axios';
import * as jwt from 'jsonwebtoken';
import { eiendomsverdiAuditRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/eiendomsverdiAuditRepositoryFixtureFactory';
import { createEiendomsverdiRestApiClient, createEiendomsverdiSoapApiClient } from './EiendomsverdiApiClient';
import type { ParsedXmlTag } from './SoapApiClient';

jest.mock('axios');

describe('EiendomsverdiRestApiClient', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });
  describe('refreshJwtToken', () => {
    it('should get undefined token at start', async () => {
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });
      expect(apiClient.getJwtToken()).toEqual(undefined);
    });

    it('should call axios request with correct input params 1', async () => {
      const requestSpy = jest.spyOn(axios, 'request');
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      await apiClient.refreshJwtToken();

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(requestSpy).toHaveBeenCalledWith({
        method: 'POST',
        url: 'tokenUrl',
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
        data: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: 'clientId',
          client_secret: 'clientSecret',
        }),
      });
    });

    it('should call axios request with correct input params 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request');
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl2',
        clientId: 'clientId2',
        clientSecret: 'clientSecret2',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      await apiClient.refreshJwtToken();

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(requestSpy).toHaveBeenCalledWith({
        method: 'POST',
        url: 'tokenUrl2',
        headers: { 'content-type': 'application/x-www-form-urlencoded' },
        data: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: 'clientId2',
          client_secret: 'clientSecret2',
        }),
      });
    });

    it('should get token and set the variable if no token is present before', async () => {
      const firstToken = jwt.sign({ some: 'data' }, 'secret', { expiresIn: 10 }); // 10 seconds expiration
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: firstToken } });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      await apiClient.refreshJwtToken();

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(apiClient.getJwtToken()).toEqual(firstToken.toString());
    });
    it('should not set new token if previous one have not expired yet', async () => {
      const firstToken = jwt.sign({ some: 'data' }, 'secret', { expiresIn: 10 }); // 10 seconds expiration
      const secondToken = jwt.sign({ some: 'data2' }, 'secret', { expiresIn: 10 }); // 10 seconds expiration
      const requestSpy = jest
        .spyOn(axios, 'request')
        .mockResolvedValueOnce({ data: { access_token: firstToken } })
        .mockResolvedValueOnce({
          data: { access_token: secondToken },
        });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      await apiClient.refreshJwtToken();
      await apiClient.refreshJwtToken();

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(apiClient.getJwtToken()).toEqual(firstToken.toString());
    });

    it('should set new token if previous one have already expired', async () => {
      const firstToken = jwt.sign({ some: 'data' }, 'secret', { expiresIn: 0 }); // 0 seconds expiration
      const secondToken = jwt.sign({ some: 'data2' }, 'secret', { expiresIn: 0 }); // 0 seconds expiration
      const requestSpy = jest
        .spyOn(axios, 'request')
        .mockResolvedValueOnce({ data: { access_token: firstToken } })
        .mockResolvedValueOnce({
          data: { access_token: secondToken },
        });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      await apiClient.refreshJwtToken();
      await apiClient.refreshJwtToken();

      expect(requestSpy).toHaveBeenCalledTimes(2);
      expect(apiClient.getJwtToken()).toEqual(secondToken.toString());
    });
  });
  describe('getEstatesIdentifiers', () => {
    it('should call refreshJwtToken then call an axios.get, return data part of the response', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: [
            {
              address: {
                id: 2697728,
                streetName: 'Bjørkelunden',
                streetNumber: 7,
                streetLetter: null,
                municipality: 'DRAMMEN',
                postOffice: {
                  code: 3036,
                  name: 'DRAMMEN',
                },
              },
              attributes: {
                estateType: 'Leilighet',
              },
              cadastre: {
                kNr: 3005,
                gNr: 34,
                bNr: 205,
                fNr: 0,
                sNr: 14,
                municipality: 'DRAMMEN',
              },
              housingCooperativeShareIdentity: null,
              location: {
                latitude: 59.70010021517775,
                longitude: 10.264536579871379,
              },
            },
          ],
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'https://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const estates = await apiClient.getEstatesIdentifiers('ssn');

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`https://apiUrl/Owners/ssn/RealEstates`, {
        headers: { Authorization: 'Bearer jwtToken' },
      });
      expect(estates).toEqual([
        {
          address: {
            id: 2697728,
            streetName: 'Bjørkelunden',
            streetNumber: 7,
            streetLetter: null,
            municipality: 'DRAMMEN',
            postOffice: {
              code: 3036,
              name: 'DRAMMEN',
            },
          },
          attributes: {
            estateType: 'Leilighet',
          },
          cadastre: {
            kNr: 3005,
            gNr: 34,
            bNr: 205,
            fNr: 0,
            sNr: 14,
            municipality: 'DRAMMEN',
          },
          housingCooperativeShareIdentity: null,
          location: {
            latitude: 59.70010021517775,
            longitude: 10.264536579871379,
          },
        },
      ]);
    });
    it('should call refreshJwtToken then call an axios.get, return data part of the response 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken2' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: [
            {
              address: {
                id: 123,
                streetName: 'Bjørkelunden',
                streetNumber: 7,
                streetLetter: null,
                municipality: 'DRAMMEN',
                postOffice: {
                  code: 3036,
                  name: 'DRAMMEN',
                },
              },
              attributes: {
                estateType: 'Leilighet',
              },
              cadastre: {
                kNr: 300,
                gNr: 3,
                bNr: 20,
                fNr: 0,
                sNr: 1,
                municipality: 'DRAMMEN',
              },
              housingCooperativeShareIdentity: null,
              location: {
                latitude: 59.70010021517775,
                longitude: 10.264536579871379,
              },
            },
          ],
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const history = await apiClient.getEstatesIdentifiers('ssn2');

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/Owners/ssn2/RealEstates`, {
        headers: { Authorization: 'Bearer jwtToken2' },
      });
      expect(history).toEqual([
        {
          address: {
            id: 123,
            streetName: 'Bjørkelunden',
            streetNumber: 7,
            streetLetter: null,
            municipality: 'DRAMMEN',
            postOffice: {
              code: 3036,
              name: 'DRAMMEN',
            },
          },
          attributes: {
            estateType: 'Leilighet',
          },
          cadastre: {
            kNr: 300,
            gNr: 3,
            bNr: 20,
            fNr: 0,
            sNr: 1,
            municipality: 'DRAMMEN',
          },
          housingCooperativeShareIdentity: null,
          location: {
            latitude: 59.70010021517775,
            longitude: 10.264536579871379,
          },
        },
      ]);
    });
  });
  describe('getEstateAttributes', () => {
    it('should call refreshJwtToken then call an axios.get, return data part of the response', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { test: 'test' },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const attributes = await apiClient.getEstateAttributes({ knr: 1, gnr: 2, bnr: 3, fnr: 4, snr: 5 });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/1/2/3/4/5/attributes`, {
        headers: { Authorization: 'Bearer jwtToken' },
      });
      expect(attributes).toEqual({ test: 'test' });
    });
    it('should call refreshJwtToken then call an axios.get, return data part of the response 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken2' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { test: 'test2' },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const attributes = await apiClient.getEstateAttributes({ knr: 5, gnr: 4, bnr: 3, fnr: 2, snr: 1 });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/5/4/3/2/1/attributes`, {
        headers: { Authorization: 'Bearer jwtToken2' },
      });
      expect(attributes).toEqual({ test: 'test2' });
    });
  });
  describe('getHousingCooperativeEstateAttributes', () => {
    it('should call refreshJwtToken then call an axios.get, return data part of the response', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { test: 'test' },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const attributes = await apiClient.getHousingCooperativeEstateAttributes({
        organizationNumber: '1234',
        shareNumber: '12',
      });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/1234/12/attributes`, {
        headers: { Authorization: 'Bearer jwtToken' },
      });
      expect(attributes).toEqual({ test: 'test' });
    });
    it('should call refreshJwtToken then call an axios.get, return data part of the response 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken2' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { test: 'test2' },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const attributes = await apiClient.getHousingCooperativeEstateAttributes({
        organizationNumber: '12345',
        shareNumber: '125',
      });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/12345/125/attributes`, {
        headers: { Authorization: 'Bearer jwtToken2' },
      });
      expect(attributes).toEqual({ test: 'test2' });
    });
  });
  describe('getSaleHistory', () => {
    it('should call refreshJwtToken then call an axios.get, return data part of the response', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: [
            {
              askingPrice: 5700000,
              salesPrice: 5800000,
              judicialTransferDate: '2021-09-03T21:00:00',
              registrationDate: '2021-05-07T00:00:00',
              saleDate: '2021-05-18T00:00:00',
              commonDebt: 0,
              surveyorsValuation: 5700000,
              estateAgent: 'agent',
              isSitePrice: false,
              isDeviantPrice: false,
              deviantPriceReason: null,
            },
          ],
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const history = await apiClient.getSaleHistory({ knr: 1, gnr: 2, bnr: 3, fnr: 4, snr: 5 });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/1/2/3/4/5/sales`, {
        headers: { Authorization: 'Bearer jwtToken' },
      });
      expect(history).toEqual([
        {
          askingPrice: 5700000,
          salesPrice: 5800000,
          judicialTransferDate: '2021-09-03T21:00:00',
          registrationDate: '2021-05-07T00:00:00',
          saleDate: '2021-05-18T00:00:00',
          commonDebt: 0,
          surveyorsValuation: 5700000,
          estateAgent: 'agent',
          isSitePrice: false,
          isDeviantPrice: false,
          deviantPriceReason: null,
        },
      ]);
    });
    it('should call refreshJwtToken then call an axios.get, return data part of the response 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken2' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: [
            {
              askingPrice: 4700000,
              salesPrice: 4800000,
              judicialTransferDate: '2021-09-03T21:00:00',
              registrationDate: '2021-05-07T00:00:00',
              saleDate: '2021-05-18T00:00:00',
              commonDebt: 0,
              surveyorsValuation: 4700000,
              estateAgent: 'agent',
              isSitePrice: false,
              isDeviantPrice: false,
              deviantPriceReason: null,
            },
          ],
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const history = await apiClient.getSaleHistory({ knr: 5, gnr: 4, bnr: 3, fnr: 2, snr: 1 });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/5/4/3/2/1/sales`, {
        headers: { Authorization: 'Bearer jwtToken2' },
      });
      expect(history).toEqual([
        {
          askingPrice: 4700000,
          salesPrice: 4800000,
          judicialTransferDate: '2021-09-03T21:00:00',
          registrationDate: '2021-05-07T00:00:00',
          saleDate: '2021-05-18T00:00:00',
          commonDebt: 0,
          surveyorsValuation: 4700000,
          estateAgent: 'agent',
          isSitePrice: false,
          isDeviantPrice: false,
          deviantPriceReason: null,
        },
      ]);
    });
  });
  describe('getHousingCooperativeSaleHistory', () => {
    it('should call refreshJwtToken then call an axios.get, return data part of the response', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: [
            {
              askingPrice: 5700000,
              salesPrice: 5800000,
              judicialTransferDate: '2021-09-03T21:00:00',
              registrationDate: '2021-05-07T00:00:00',
              saleDate: '2021-05-18T00:00:00',
              commonDebt: 0,
              surveyorsValuation: 5700000,
              estateAgent: 'agent',
              isSitePrice: false,
              isDeviantPrice: false,
              deviantPriceReason: null,
            },
          ],
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const history = await apiClient.getHousingCooperativeSaleHistory({
        organizationNumber: '1234',
        shareNumber: '12',
      });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/1234/12/sales`, {
        headers: { Authorization: 'Bearer jwtToken' },
      });
      expect(history).toEqual([
        {
          askingPrice: 5700000,
          salesPrice: 5800000,
          judicialTransferDate: '2021-09-03T21:00:00',
          registrationDate: '2021-05-07T00:00:00',
          saleDate: '2021-05-18T00:00:00',
          commonDebt: 0,
          surveyorsValuation: 5700000,
          estateAgent: 'agent',
          isSitePrice: false,
          isDeviantPrice: false,
          deviantPriceReason: null,
        },
      ]);
    });
    it('should call refreshJwtToken then call an axios.get, return data part of the response 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken2' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: [
            {
              askingPrice: 4700000,
              salesPrice: 4800000,
              judicialTransferDate: '2021-09-03T21:00:00',
              registrationDate: '2021-05-07T00:00:00',
              saleDate: '2021-05-18T00:00:00',
              commonDebt: 0,
              surveyorsValuation: 4700000,
              estateAgent: 'agent',
              isSitePrice: false,
              isDeviantPrice: false,
              deviantPriceReason: null,
            },
          ],
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const history = await apiClient.getHousingCooperativeSaleHistory({
        organizationNumber: '12345',
        shareNumber: '125',
      });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://apiUrl/RealEstates/12345/125/sales`, {
        headers: { Authorization: 'Bearer jwtToken2' },
      });
      expect(history).toEqual([
        {
          askingPrice: 4700000,
          salesPrice: 4800000,
          judicialTransferDate: '2021-09-03T21:00:00',
          registrationDate: '2021-05-07T00:00:00',
          saleDate: '2021-05-18T00:00:00',
          commonDebt: 0,
          surveyorsValuation: 4700000,
          estateAgent: 'agent',
          isSitePrice: false,
          isDeviantPrice: false,
          deviantPriceReason: null,
        },
      ]);
    });
  });
  describe('getMarketEstimate', () => {
    it('should call refreshJwtToken then call an axios.get, return data part of the response', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { estimate: 6000000, estimateLow: 5400000, estimateHigh: 6600000 },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const estimate = await apiClient.getMarketEstimate({ knr: 1, gnr: 2, bnr: 3, fnr: 4, snr: 5 });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://estimateApiUrl/RealEstates/1/2/3/4/5/MarketEstimate`, {
        headers: { Authorization: 'Bearer jwtToken' },
      });
      expect(estimate).toEqual({ estimate: 6000000, estimateLow: 5400000, estimateHigh: 6600000 });
    });
    it('should call refreshJwtToken then call an axios.get, return data part of the response 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken2' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { estimate: 5000000, estimateLow: 4400000, estimateHigh: 5600000 },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const estimate = await apiClient.getMarketEstimate({ knr: 5, gnr: 4, bnr: 3, fnr: 2, snr: 1 });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://estimateApiUrl/RealEstates/5/4/3/2/1/MarketEstimate`, {
        headers: { Authorization: 'Bearer jwtToken2' },
      });
      expect(estimate).toEqual({ estimate: 5000000, estimateLow: 4400000, estimateHigh: 5600000 });
    });
  });
  describe('getHousingCooperativeMarketEstimate', () => {
    it('should call refreshJwtToken then call an axios.get, return data part of the response', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { estimate: 6000000, estimateLow: 5400000, estimateHigh: 6600000 },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const estimate = await apiClient.getHousingCooperativeMarketEstimate({
        organizationNumber: '1234',
        shareNumber: '12',
      });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://estimateApiUrl/RealEstates/1234/12/MarketEstimate`, {
        headers: { Authorization: 'Bearer jwtToken' },
      });
      expect(estimate).toEqual({ estimate: 6000000, estimateLow: 5400000, estimateHigh: 6600000 });
    });
    it('should call refreshJwtToken then call an axios.get, return data part of the response 2', async () => {
      const requestSpy = jest.spyOn(axios, 'request').mockResolvedValue({ data: { access_token: 'jwtToken2' } });
      const getSpy = jest.spyOn(axios, 'get').mockResolvedValue({
        data: {
          data: { estimate: 5000000, estimateLow: 4400000, estimateHigh: 5600000 },
        },
      });
      const apiClient = createEiendomsverdiRestApiClient({
        axios,
        tokenUrl: 'tokenUrl',
        clientId: 'clientId',
        clientSecret: 'clientSecret',
        apiUrl: 'http://apiUrl/',
        estimateApiUrl: 'http://estimateApiUrl/',
      });

      const history = await apiClient.getHousingCooperativeMarketEstimate({
        organizationNumber: '12345',
        shareNumber: '125',
      });

      expect(requestSpy).toHaveBeenCalledTimes(1);
      expect(getSpy).toHaveBeenCalledWith(`http://estimateApiUrl/RealEstates/12345/125/MarketEstimate`, {
        headers: { Authorization: 'Bearer jwtToken2' },
      });
      expect(history).toEqual({ estimate: 5000000, estimateLow: 4400000, estimateHigh: 5600000 });
    });
  });
});

describe('EiendomsverdiApiClient', () => {
  describe('#request', () => {
    const API_URL = 'https://doge.com';
    const act = async (): Promise<ParsedXmlTag | null> =>
      createEiendomsverdiSoapApiClient(
        axios,
        API_URL,
        'fake-user',
        'fake-password',
        '',
        eiendomsverdiAuditRepositoryFixtureFactory(),
      ).request('abc', 'abc');

    const respondWithDefaults = (): void =>
      respondWith(`
        <s:Envelope><s:Body><ActionResult><Response><abc></abc></Response></ActionResult></s:Body></s:Envelope>
      `);

    test('makes an API call to the provided API URL', async () => {
      respondWithDefaults();

      await act();

      expect(axios).toHaveBeenCalledWith(expect.objectContaining({ url: API_URL }));
    });

    test('always uses HTTP POST for requests', async () => {
      respondWithDefaults();

      await act();

      expect(axios).toHaveBeenCalledWith(expect.objectContaining({ method: 'POST' }));
    });

    test('adds a Content-Type header by default', async () => {
      respondWithDefaults();

      await act();

      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          // eslint-disable-next-line @typescript-eslint/naming-convention
          headers: { 'Content-Type': 'application/soap+xml' },
        }),
      );
    });

    test('should return some data', async () => {
      respondWithDefaults();

      await act();

      expect(axios).toHaveBeenCalledWith(expect.objectContaining({ data: expect.stringContaining('xml') as unknown }));
    });

    test('adds a security header based on the provided username', async () => {
      respondWithDefaults();

      const user = 'fake-user';
      await createEiendomsverdiSoapApiClient(
        axios,
        API_URL,
        user,
        'fake-password',
        '',
        eiendomsverdiAuditRepositoryFixtureFactory(),
      ).request('abc', 'abc');

      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.stringContaining(`<o:Username>${user}</o:Username>`) as unknown,
        }),
      );
    });

    test('adds a security header based on the provided password', async () => {
      respondWithDefaults();

      const password = 'fake/password';
      await createEiendomsverdiSoapApiClient(
        axios,
        API_URL,
        'fake-user',
        password,
        '',
        eiendomsverdiAuditRepositoryFixtureFactory(),
      ).request('abc', 'abc');

      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.stringContaining(`<o:Password>${password}</o:Password>`) as unknown,
        }),
      );
    });

    test('creates a request that must understand the given service', async () => {
      respondWithDefaults();

      const serviceName = 'fake-service';
      const expected = `<a:To s:mustUnderstand="1">https://test-api.eiendomsverdi.no/${serviceName}.svc</a:To>`;
      await createEiendomsverdiSoapApiClient(
        axios,
        API_URL,
        '',
        '',
        serviceName,
        eiendomsverdiAuditRepositoryFixtureFactory(),
      ).request('abc', 'abc');

      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({ data: expect.stringContaining(expected) as unknown }),
      );
    });

    test('combines the given service name with the current action name', async () => {
      respondWithDefaults();

      const action = 'abc';
      const serviceName = 'fake-service';
      await createEiendomsverdiSoapApiClient(
        axios,
        API_URL,
        '',
        '',
        serviceName,
        eiendomsverdiAuditRepositoryFixtureFactory(),
      ).request(action, 'abc');

      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.stringContaining(`<a:Action>http://tempuri.org/I${serviceName}/${action}</a:Action>`) as unknown,
        }),
      );
    });

    test('combines the given action and the given body', async () => {
      respondWithDefaults();

      const action = 'abc';
      const body = 'fake-body';
      await createEiendomsverdiSoapApiClient(
        axios,
        API_URL,
        '',
        '',
        '',
        eiendomsverdiAuditRepositoryFixtureFactory(),
      ).request(action, body);

      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.stringContaining(
            `<s:Body><${action} xmlns="http://tempuri.org/">${body}</${action}></s:Body>`,
          ) as unknown,
        }),
      );
    });

    describe('when the unwrapped response does not contain a response', () => {
      test('returns null', async () => {
        respondWith(`
          <s:Envelope><s:Body><ActionResult></ActionResult></s:Body></s:Envelope>
        `);

        const result = await act();

        expect(result).toBeNull();
      });
    });

    test('returns the parsed response', async () => {
      respondWith(`
        <s:Envelope>
          <s:Body>
            <ActionResult>
              <Something>
                <Abc></Abc>
              </Something>
            </ActionResult>
          </s:Body>
        </s:Envelope>
      `);

      const result = await act();

      expect(result).toEqual({ elements: [{ name: 'Abc', type: 'element' }], name: 'Something', type: 'element' });
    });

    const respondWith = (data: string): void => {
      ((axios as unknown) as jest.Mock).mockImplementationOnce(() => ({ data }));
    };
  });
});
