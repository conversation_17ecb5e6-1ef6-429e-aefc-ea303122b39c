import type { EiendomsverdiEstateCache } from './eiendomsverdi-estate-cache';
import { eiendomsverdiEstateCacheAttributes, EiendomsverdiEstateCacheModel } from './eiendomsverdi-estate-cache.model';

export type EiendomsverdiEstateCacheRepository = {
  get(queryHash: string): Promise<EiendomsverdiEstateCache | null>;
  update(args: { id: string; updates: Partial<EiendomsverdiEstateCache> }): Promise<EiendomsverdiEstateCache | null>;
  upsert(cache: Omit<EiendomsverdiEstateCache, 'createdAt' | 'id' | 'updatedAt'>): Promise<void>;
};

export const eiendomsverdiEstateCacheRepositoryFactory = (): EiendomsverdiEstateCacheRepository => ({
  get: async (queryHash) => {
    const cache = await EiendomsverdiEstateCacheModel.findOne({
      attributes: Object.keys(eiendomsverdiEstateCacheAttributes),
      where: {
        queryHash,
      },
    });
    return cache ? cache.toJSON() : null;
  },
  update: async ({ id, updates }) => {
    const existingCacheModel = await EiendomsverdiEstateCacheModel.findOne({
      where: { id },
    });
    if (!existingCacheModel) {
      return null;
    }
    const updatedCache = await existingCacheModel.update(updates);

    return updatedCache.toJSON();
  },
  upsert: async (cache) => {
    const values = [
      cache.query,
      cache.cacheType,
      cache.lastSynced,
      JSON.stringify(cache.data),
      cache.queryHash,
      new Date(),
      new Date(),
    ];
    const query =
      'INSERT INTO "EiendomsverdiEstateCache" ("query", "cacheType", "lastSynced", "data", "queryHash", "createdAt", "updatedAt") \
      VALUES (?, ?, ?, ?, ?, ?, ?) ON CONFLICT ("queryHash") \
      DO UPDATE SET "data" = excluded."data", "lastSynced" = excluded."lastSynced"';

    await EiendomsverdiEstateCacheModel.sequelize?.query({ query, values });
  },
});
