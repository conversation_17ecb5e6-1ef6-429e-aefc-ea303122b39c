import type { Resource } from '../../framework/sequelize/resource';
import type {
  EVEstateAttributesResponse,
  EVEstateSaleHistoryResponse,
  EVEstateValuationResponse,
} from './EiendomsverdiApiClient';
import type { ParsedXmlTag } from './SoapApiClient';

export enum CacheTypeEnum {
  GET_HOUSING_COOPERATIVE_SHARE_SALE_HISTORY = 'GetHousingCooperativeShareSaleHistory',
  GET_ESTATE_SALE_HISTORY = 'GetEstateSaleHistory',
  GET_HOUSING_COOPERATIVE_MARKET_ESTIMATE = 'GetHousingCooperativeMarketEstimate', // There is no soap endpoint for this, both estimates are available on the soap api under GetMarketEstimate
  GET_MARKET_ESTIMATE = 'GetMarketEstimate',
  GET_EXTENDED_ESTATE_INFO = 'GetExtendedEstateInfo',
  GET_HOUSING_COOPERATIVE_SHARE_INFORMATION = 'GetHousingCooperativeShareInformation',
  FIND_ESTATES = 'FindEstates', // This is not used in the Rest API
}
export type EiendomsverdiEstateCache = Resource & {
  query: string;
  cacheType: CacheTypeEnum;
  lastSynced: Date;
  data: ParsedXmlTag | EVEstateSaleHistoryResponse[] | EVEstateValuationResponse | EVEstateAttributesResponse;
  queryHash: string;
};
