export enum UserRole {
  USER = 'user',
  BROKER = 'broker',
}
type BrokerIdentity = {
  userID: string;
  role: UserRole.BROKER;
  phoneNumber: null;
};

type UserIdentity = {
  userID: string;
  role: UserRole.USER;
  phoneNumber: string;
};
export type Identity = UserIdentity | BrokerIdentity;

export type WithIdentity<T> = T & { identity: Identity };
export type WithOptionalIdentity<T> = T & { identity?: Identity };
export type WithoutIdentity<T> = Pick<T, Exclude<keyof T, 'identity'>>;
