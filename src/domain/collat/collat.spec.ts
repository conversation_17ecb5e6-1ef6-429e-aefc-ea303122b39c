import { norwegianComparator } from './collat';

describe('collat', () => {
  describe('#norwegianComparator', () => {
    describe('given single latin letters', () => {
      const input = ['a', 'x', 'y', 'b'];
      const expectedOutput = ['a', 'b', 'x', 'y'];

      it('should properly sort them', () => {
        expect(input.sort(norwegianComparator)).toEqual(expectedOutput);
      });
    });

    describe('given an upper-case and a lower-case letter', () => {
      const input = ['a', 'A'];
      const expectedOutput = ['A', 'a'];

      it('should sort the upper-case letter to the first place', () => {
        expect(input.sort(norwegianComparator)).toEqual(expectedOutput);
      });
    });

    describe('given single latin words', () => {
      const input = ['apple', 'and', 'bite', 'doctor'];
      const expectedOutput = ['and', 'apple', 'bite', 'doctor'];

      it('should properly sort them', () => {
        expect(input.sort(norwegianComparator)).toEqual(expectedOutput);
      });
    });

    describe('given equal words', () => {
      const input = ['apple', 'apple'];
      const expectedOutput = ['apple', 'apple'];

      it('should return them all', () => {
        expect(input.sort(norwegianComparator)).toEqual(expectedOutput);
      });
    });

    describe('given two words with same prefix (second one is longer)', () => {
      const input = ['apple', 'appleapple'];
      const expectedOutput = ['apple', 'appleapple'];

      it('should return the longer one at the end', () => {
        expect(input.sort(norwegianComparator)).toEqual(expectedOutput);
      });
    });

    describe('given two words with same prefix (first one is longer)', () => {
      const input = ['appleapple', 'apple'];
      const expectedOutput = ['apple', 'appleapple'];

      it('should return the longer one at the end', () => {
        expect(input.sort(norwegianComparator)).toEqual(expectedOutput);
      });
    });
  });
});
