import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import type { EstateImageFromUser } from './estate-image-from-user';
import { EstateImageFromUserModel } from './estate-image-from-user.model';

export type EstateImageFromUserRepository = {
  get(props: {
    userID: string;
    landIdentificationMatrix: LandIdentificationMatrix;
  }): Promise<EstateImageFromUser | null>;
  upsert(update: Partial<EstateImageFromUser>): Promise<EstateImageFromUser>;
};

export const estateImageFromUserRepositoryFactory = (): EstateImageFromUserRepository => {
  const get = async ({
    userID,
    landIdentificationMatrix,
  }: {
    userID: string;
    landIdentificationMatrix: LandIdentificationMatrix;
  }): Promise<EstateImageFromUser | null> => {
    const res = await EstateImageFromUserModel.findOne({
      where: {
        userID: userID,
        landIdentificationMatrix,
      },
    });
    if (!res) {
      return null;
    }
    return res.toJSON();
  };

  const upsert = async (update: EstateImageFromUser): Promise<EstateImageFromUser> => {
    const res = await EstateImageFromUserModel.upsert(update, { returning: true });
    return res[0].toJSON();
  };

  return {
    get,
    upsert,
  };
};
