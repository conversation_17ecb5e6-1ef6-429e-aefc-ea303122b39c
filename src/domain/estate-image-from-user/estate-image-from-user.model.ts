import type { Sequelize } from 'sequelize';
import { Model } from 'sequelize';
import type { ResourceModel } from '../../framework/sequelize/resource';
import {
  estateImagesFromUsersTableAttributes,
  ESTATE_IMAGES_FROM_USERS_TABLE_NAME,
} from '../../migrations/202208081200/estate-images-from-users.table';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import type { User } from '../user/user';

export class EstateImageFromUserModel extends Model {
  public userID: User['id'];
  public landIdentificationMatrix: LandIdentificationMatrix;
  public createdAt: ResourceModel['createdAt'];
  public updatedAt: ResourceModel['updatedAt'];
  public imagePublicUrl: string;
}

export const estateImageFromUserModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstateImageFromUserModel.init(estateImagesFromUsersTableAttributes, {
    sequelize,
    tableName: ESTATE_IMAGES_FROM_USERS_TABLE_NAME,
    timestamps: true,
  });
};
