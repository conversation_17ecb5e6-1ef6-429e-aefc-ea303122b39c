import { addMonths, differenceInCalendarDays, endOfDay, isFuture, isPast } from 'date-fns';
import { getTimezoneOffset, utcToZonedTime } from 'date-fns-tz';
import urljoin from 'url-join';
import i18n from '../../../i18n.config';
import { dateSortFunction, getBiddingDate, getFirstLastViewingDates } from '../../recommendation-engine/utils';
import { EstateVitecStatus } from '../estate';
import { EstateBuyerChecklistID, EstateChecklistID } from '../estate-checklist/estate-checklist';
import type { EstateChecklistReponse } from '../estate-checklist/estate-checklist.service';
import { EstateEventID, EstateEventType } from '../estate-event/estate-event';
import type { EstateEventWithDate } from '../estate-event/estate-event.service';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import type {
  TimelineBucketChecklistItem,
  TimelineBucketEventItem,
  TimelineBucketLinkItem,
  TimelineBucketTemplate,
} from './estate-timeline';
import { TimelineBucketID, TimelineBucketItemType, TimelineBucketLogName } from './estate-timeline';

export const checklistItemFactory = (
  checklists: EstateChecklistReponse[],
  id: EstateChecklistID | EstateBuyerChecklistID,
): TimelineBucketChecklistItem | null => {
  const entry = checklists.find((checklist) => checklist.checklistID === id);

  if (!entry) {
    return null;
  }

  const items = entry.items.map((item) => ({
    id: item.todoID,
    title: item.title,
    description: item.description,
    logName: item.logName,
    isComplete: item.isComplete,
  }));

  return {
    id: entry.checklistID,
    title: entry.title,
    type: TimelineBucketItemType.CHECKLIST,
    logName: entry.logName,
    isComplete: items.every((item) => item.isComplete),
    isIndependent: entry.checklistID === EstateChecklistID.INFORMATION_GATHERING,
    items,
  };
};

export const eventItemFactory = (
  events: EstateEventWithDate[],
  id: EstateEventID,
  dateOverride?: Date | null,
): TimelineBucketEventItem | null => {
  const entry = events.find((event) => event.eventID === id);

  if (!entry) {
    return null;
  }

  const date = dateOverride ? dateOverride : entry.date;

  return {
    id: entry.eventID,
    title: entry.title,
    type: entry.type === EstateEventType.USER ? TimelineBucketItemType.USER_EVENT : TimelineBucketItemType.VITEC_EVENT,
    logName: entry.logName,
    date,
    isComplete: date !== null && isPast(endOfDay(new Date(+date + getTimezoneOffset('Europe/Oslo')))),
  };
};

export const viewingEventItemFactory = (estate: EstateMongoose): (TimelineBucketEventItem | null)[] => {
  return estate.showings.sort(dateSortFunction).map((e, i) => {
    return {
      id: EstateEventID.VIEWING,
      title: `Visning #${i + 1}`,
      type: TimelineBucketItemType.VITEC_EVENT,
      logName: 'visiting',
      date: new Date(e.start),
      dateFrom: new Date(e.start),
      dateUntil: new Date(e.end),
      isComplete: e.start !== null && isPast(endOfDay(utcToZonedTime(new Date(e.start), 'Europe/Oslo'))),
    };
  });
};

export const linkItemFactory = (id: string, title: string, url: string): TimelineBucketLinkItem => {
  return {
    id,
    title,
    type: TimelineBucketItemType.LINK,
    logName: id,
    url,
    isComplete: true,
  };
};

export const otpLinkItemFactory = (estateId?: string): TimelineBucketLinkItem => {
  return linkItemFactory(
    'takeover_url',
    'Overtakelsesprotokoll',
    estateId ? urljoin('/', 'customer', 'overtake-protocol', estateId) : '#',
  );
};

export const hasOtpSoon = (estate?: EstateMongoose): boolean => {
  if (!estate) {
    return false;
  }

  if (estate.takeOverDate === null) {
    return false;
  }

  const takeOverDateWithOffset = new Date(+estate.takeOverDate + getTimezoneOffset('Europe/Oslo'));
  const numberOfDaysUntilOtp = differenceInCalendarDays(takeOverDateWithOffset, new Date());

  return !!estate && estate.takeOverDate !== null && (numberOfDaysUntilOtp === 0 || numberOfDaysUntilOtp === 1);
};

export const sellerPreparationBucketTemplateFactory = (
  checklists: EstateChecklistReponse[],
  events: EstateEventWithDate[],
): TimelineBucketTemplate => ({
  id: TimelineBucketID.PREPARATION,
  logName: TimelineBucketLogName.PREPARATION,
  title: i18n.__('Preparation'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [
    checklistItemFactory(checklists, EstateChecklistID.INFORMATION_GATHERING),
    eventItemFactory(events, EstateEventID.INTERIOR_GUIDANCE),
    checklistItemFactory(checklists, EstateChecklistID.PHOTOGRAPHER),
    eventItemFactory(events, EstateEventID.PHOTOGRAPHER),
    checklistItemFactory(checklists, EstateChecklistID.APPRAISER),
    eventItemFactory(events, EstateEventID.APPRAISER),
  ],
  isActive: (estate?: EstateMongoose) => {
    return estate?.status === EstateVitecStatus.PREPARATION;
  },
  isUserCompletable: true,
  digitalInspectionUrlEndpoint: '/info/finansiering',
});

export const sellerAudienceAnalysisBucketTemplateFactory = (): TimelineBucketTemplate => ({
  id: TimelineBucketID.AUDIENCE_ANALYSIS,
  logName: TimelineBucketLogName.AUDIENCE_ANALYSIS,
  title: i18n.__('Audience analysis'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [],
  isActive: () => {
    return false;
  },
  isUserCompletable: false,
  digitalInspectionUrlEndpoint: '/info/malgruppeanalyse',
});

export const sellerMarketingBucketTemplateFactory = (
  events: EstateEventWithDate[],
  estate?: EstateMongoose,
): TimelineBucketTemplate => ({
  id: TimelineBucketID.MARKETING,
  logName: TimelineBucketLogName.MARKETING,
  title: i18n.__('Marketing'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [eventItemFactory(events, EstateEventID.AD_LIVE_IN_CORE_CHANNELS, estate?.finnPublishDate)],
  isActive: (estate?: EstateMongoose) => {
    if (!estate || estate.status !== EstateVitecStatus.FOR_SALE) {
      return false;
    }

    if (!estate.showings.length) {
      return true;
    }

    const { firstViewingDate } = getFirstLastViewingDates(estate.showings);

    if (!firstViewingDate) {
      return true;
    }

    return differenceInCalendarDays(firstViewingDate, new Date()) > 5;
  },
  isUserCompletable: false,
  digitalInspectionUrlEndpoint: '/info/grunnpakken',
});

export const sellerViewingBucketTemplateFactory = (
  checklists: EstateChecklistReponse[],
  estate?: EstateMongoose,
): TimelineBucketTemplate => ({
  id: TimelineBucketID.VIEWING,
  logName: TimelineBucketLogName.VIEWING,
  title: i18n.__('Viewing'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [
    checklistItemFactory(checklists, EstateChecklistID.VIEWING),
    ...(estate ? viewingEventItemFactory(estate) : []),
  ],
  isActive: (estate?: EstateMongoose) => {
    if (!estate || estate.status !== EstateVitecStatus.FOR_SALE || !estate.showings.length) {
      return false;
    }

    return estate.showings.some((viewing) => {
      const difference = differenceInCalendarDays(new Date(viewing.start), new Date());
      return difference >= 0 && difference <= 5;
    });
  },
  isUserCompletable: false,
  digitalInspectionUrlEndpoint: '/info/visning',
});

export const sellerBiddingRoundBucketTemplateFactory = (
  events: EstateEventWithDate[],
  estate?: EstateMongoose,
): TimelineBucketTemplate => ({
  id: TimelineBucketID.BIDDING_ROUND,
  logName: TimelineBucketLogName.BIDDING_ROUND,
  title: i18n.__('Bidding'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [eventItemFactory(events, EstateEventID.BIDDING_ROUND, estate ? getBiddingDate(estate.showings) : null)],
  isActive: (estate?: EstateMongoose) => {
    if (!estate || estate.status !== EstateVitecStatus.FOR_SALE || !estate.showings.length) {
      return false;
    }

    const { lastViewingDate } = getFirstLastViewingDates(estate.showings);

    if (!lastViewingDate) {
      return false;
    }

    return differenceInCalendarDays(lastViewingDate, new Date()) < 0;
  },
  isUserCompletable: false,
  digitalInspectionUrlEndpoint: '/info/budrunde',
});

export const sellerFinishingBucketTemplateFactory = (
  checklists: EstateChecklistReponse[],
  events: EstateEventWithDate[],
  estate?: EstateMongoose,
): TimelineBucketTemplate => ({
  id: TimelineBucketID.FINISHING,
  logName: TimelineBucketLogName.FINISHING,
  title: i18n.__('Finishing'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [
    checklistItemFactory(checklists, EstateChecklistID.CONTRACT_SIGNING),
    eventItemFactory(events, EstateEventID.CONTRACT_SIGNING),
    checklistItemFactory(checklists, EstateChecklistID.HANDOVER),
    eventItemFactory(events, EstateEventID.TAKE_OVER, estate?.takeOverDate),
    ...(hasOtpSoon(estate) ? [otpLinkItemFactory(estate?.estateId)] : []),
  ],
  isActive: (estate?: EstateMongoose) => {
    if (!estate) {
      return false;
    }

    return estate.status === EstateVitecStatus.OVERSOLD || estate.status === EstateVitecStatus.RESERVED;
  },
  isUserCompletable: true,
  digitalInspectionUrlEndpoint: '/info/kontraktsmote',
});

export const buyerTakeOverBucketTemplateFactory = (
  checklists: EstateChecklistReponse[],
  events: EstateEventWithDate[],
  estate?: EstateMongoose,
): TimelineBucketTemplate => ({
  id: TimelineBucketID.TAKEOVER,
  logName: TimelineBucketLogName.TAKEOVER,
  title: i18n.__('Take over'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [
    checklistItemFactory(checklists, EstateBuyerChecklistID.CONTRACT_SIGNING),
    eventItemFactory(events, EstateEventID.CONTRACT_SIGNING),
    checklistItemFactory(checklists, EstateBuyerChecklistID.TAKEOVER),
    eventItemFactory(events, EstateEventID.TAKE_OVER, estate?.takeOverDate),
    ...(hasOtpSoon(estate) ? [otpLinkItemFactory(estate?.estateId)] : []),
  ],
  isActive: (estate?: EstateMongoose) => {
    return !!estate;
  },
  isUserCompletable: false,
  digitalInspectionUrlEndpoint: null,
});

export const buyerMovingInBucketTemplateFactory = (checklists: EstateChecklistReponse[]): TimelineBucketTemplate => ({
  id: TimelineBucketID.MOVING_IN,
  logName: TimelineBucketLogName.MOVING_IN,
  title: i18n.__('Moving in'),
  description: i18n.__('For more information see the digital inspection folder'),
  items: [checklistItemFactory(checklists, EstateBuyerChecklistID.MOVE_IN)],
  isActive: (estate?: EstateMongoose) => {
    if (!estate || !estate.takeOverDate) {
      return false;
    }

    return isPast(estate.takeOverDate) && isFuture(addMonths(estate.takeOverDate, 2));
  },
  isUserCompletable: false,
  digitalInspectionUrlEndpoint: null,
});
