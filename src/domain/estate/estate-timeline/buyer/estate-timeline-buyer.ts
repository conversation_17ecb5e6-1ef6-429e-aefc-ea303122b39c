import type { EstateTimelineItem } from '../estate-timeline-item';

export enum EstateTimelineBuyerBucketName {
  TAKEOVER = 'Overtakelse',
  MOVE_IN = 'Innflytting',
}

export type EstateTimelineBuyerSection = {
  name: EstateTimelineBuyerBucketName;
  items: EstateTimelineItem[];
  isActive: boolean;
  logName?: string;
};

export type EstateTimelineBucketBuyerStatus = {
  takeOver: boolean;
  moveIn: boolean;
};
