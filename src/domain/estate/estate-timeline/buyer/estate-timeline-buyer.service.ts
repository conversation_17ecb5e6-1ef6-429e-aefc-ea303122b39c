import { add, isPast } from 'date-fns';
import { EstateBuyerChecklistID } from '../../estate-checklist/estate-checklist';
import type { EstateMongoose } from '../../mongo/estate.mongoose-types';
import { EstateTimelineItemType } from '../estate-timeline-item';
import type { EstateTimelineBucketBuyerStatus, EstateTimelineBuyerSection } from './estate-timeline-buyer';
import { EstateTimelineBuyerBucketName } from './estate-timeline-buyer';

export type EstateTimelineBuyerService = {
  getActiveBuckets(estate: EstateMongoose): EstateTimelineBucketBuyerStatus;
  getTimeline: (estate: EstateMongoose) => EstateTimelineBuyerSection[];
  getActiveBucketName(estate: EstateMongoose): EstateTimelineBuyerBucketName;
};

export const estateTimelineBuyerServiceFactory = (): EstateTimelineBuyerService => {
  const getTimeline = (estate: EstateMongoose): EstateTimelineBuyerSection[] => {
    const timelineBucketsStatus = getActiveBuckets(estate);

    const bucketItems = [
      {
        name: EstateTimelineBuyerBucketName.TAKEOVER,
        items: [
          {
            id: EstateBuyerChecklistID.CONTRACT_SIGNING,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste før kontraktsmøtet',
            isComplete: false,
            value: null,
            logName: 'handover_finish_contract_signing',
          },
          {
            id: 'contractSignEvent',
            type: EstateTimelineItemType.ITEM,
            name: 'Kontraktsmøte',
            isComplete: isPast(estate.contractMeetingDate),
            value: estate.contractMeetingDate?.toString() || null,
          },
          {
            id: EstateBuyerChecklistID.TAKEOVER,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste før overtakelsen',
            isComplete: false,
            value: null,
            logName: 'handover_finish_handover',
          },
          {
            id: 'bankMeetingEvent',
            type: EstateTimelineItemType.ITEM,
            name: 'Bankmøte',
            isComplete: false,
            value: null,
          },
          {
            id: 'transferPurchasePriceEvent',
            type: EstateTimelineItemType.ITEM,
            name: 'Overføre kjøpesum',
            isComplete: false,
            value: null,
          },

          {
            id: 'takeOverProtocolEvent',
            type: EstateTimelineItemType.ITEM,
            name: 'Overtakelesesprotokoll',
            isComplete: isPast(estate.takeOverDate),
            value: estate.takeOverDate?.toString() || null,
          },
        ],
        isActive: timelineBucketsStatus.takeOver,
        logName: 'handover',
      },
      {
        name: EstateTimelineBuyerBucketName.MOVE_IN,
        items: [
          {
            id: EstateBuyerChecklistID.MOVE_IN,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste før flytteprosessen',
            isComplete: false,
            value: null,
            logName: 'moving_in_finish_moving_in',
          },
        ],
        isActive: timelineBucketsStatus.moveIn,
        logName: 'moving_in',
      },
    ];

    const firstActive = bucketItems.find((item) => item.isActive) || { name: null };
    return bucketItems.map((item) => ({ ...item, isActive: item.name === firstActive.name }));
  };

  const getActiveBucketName = (estate: EstateMongoose): EstateTimelineBuyerBucketName => {
    const activeBuckets = getActiveBuckets(estate);

    if (activeBuckets.moveIn) {
      return EstateTimelineBuyerBucketName.MOVE_IN;
    }
    return EstateTimelineBuyerBucketName.TAKEOVER;
  };

  const getActiveBuckets = (estate: EstateMongoose): EstateTimelineBucketBuyerStatus => {
    const soldDateInPast = isPast(estate.soldDate);
    const takeOverDateInPast = isPast(add(estate.takeOverDate, { days: 1 }));
    return {
      takeOver: soldDateInPast,
      moveIn: takeOverDateInPast,
    };
  };

  return {
    getActiveBuckets,
    getTimeline,
    getActiveBucketName,
  };
};
