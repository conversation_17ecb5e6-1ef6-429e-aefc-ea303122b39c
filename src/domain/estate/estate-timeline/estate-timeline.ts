import type { EstateMongoose, EstateMongooseAssignmentTypeGroups } from '../mongo/estate.mongoose-types';

export enum TimelineJourney {
  SELLER = 'seller',
  BUYER = 'buyer',
  PROSPECT = 'prospect',
}

export enum TimelineBucketStatus {
  COMPLETED = 'completed',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum TimelineBucketID {
  PREPARATION = 'preparation',
  AUDIENCE_ANALYSIS = 'audience-analysis',
  MARKETING = 'marketing',
  VIEWING = 'viewing',
  BIDDING_ROUND = 'bidding',
  FINISHING = 'finishing',
  TAKEOVER = 'takeover',
  MOVING_IN = 'moving-in',
}

export enum TimelineBucketLogName {
  PREPARATION = 'preparation',
  AUDIENCE_ANALYSIS = 'audience_analysis',
  MARKETING = 'marketing',
  VIEWING = 'viewing',
  BIDDING_ROUND = 'bidding_round',
  FINISHING = 'finish',
  TAKEOVER = 'handover',
  MOVING_IN = 'moving_in',
}

export type TimelineBucketItem = TimelineBucketChecklistItem | TimelineBucketEventItem | TimelineBucketLinkItem;

export type TimelineBucketTemplate = {
  id: TimelineBucketID;
  title: string;
  items: (TimelineBucketItem | null)[];
  logName: string;
  description: string;
  isActive: (estate?: EstateMongoose) => boolean;
  isUserCompletable: boolean;
  digitalInspectionUrlEndpoint: string | null;
};

export type TimelineBucket = {
  id: TimelineBucketID;
  title: string;
  items: (TimelineBucketItem | null)[];
  logName: string;
  description: string;
  step: number;
  status: TimelineBucketStatus;
  digitalInspectionUrlEndpoint: string | null;
};

export enum TimelineBucketItemType {
  USER_EVENT = 'userEvent',
  VITEC_EVENT = 'vitecEvent',
  CHECKLIST = 'checklist',
  LINK = 'link',
}

export type TimelineBucketItemMetadata = {
  id: string;
  type: TimelineBucketItemType;
  title: string;
  logName: string;
  isComplete: boolean;
};

export type TimelineBucketChecklistItem = TimelineBucketItemMetadata & {
  isIndependent: boolean;
  items: {
    id: number;
    title: string;
    description: string;
    logName: string;
    isComplete: boolean;
  }[];
};

export type TimelineBucketEventItem = TimelineBucketItemMetadata & {
  date: Date | null;
  dateFrom?: Date | null;
  dateUntil?: Date | null;
};

export type TimelineBucketLinkItem = TimelineBucketItemMetadata & {
  url: string;
};

export type Timeline = TimelineMetadata & {
  buckets: TimelineBucket[];
  broker: {
    name: string;
    image: {
      large: string;
      medium: string;
      small: string;
    };
    mobilePhone: string | null;
    workPhone: string | null;
    email: string | null;
  } | null;
};

export type TimelineMetadata = {
  estateID: string | null;
  journeyType: TimelineJourney;
  digitalInspectionUrl: string | null;
};

export enum TimelineTodoType {
  CHECKLIST = 'checklist',
  EVENT = 'event',
  SELLER_PREPARATION_INPUT_DATES = 'sellerPreparationInputDates',
}

export type TimelineTodo = {
  title: string;
  type: TimelineTodoType;
  item: TimelineBucketItem | null;
};

export type TimelineAssignmentTypeGroup = {
  assignmentTypeGroup: EstateMongooseAssignmentTypeGroups;
};
