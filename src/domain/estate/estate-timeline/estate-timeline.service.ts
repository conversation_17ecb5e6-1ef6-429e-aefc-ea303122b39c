import axios from 'axios';
import { findLastIndex } from 'ramda';
import type { User } from '../../user/user';
import { UserEstateRelation } from '../estate';
import { EstateChecklistType } from '../estate-checklist/estate-checklist';
import type { EstateChecklistService } from '../estate-checklist/estate-checklist.service';
import type { EstateEventService } from '../estate-event/estate-event.service';
import type { EmployeeMongoose } from '../mongo/employee/employee';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import type {
  Timeline,
  TimelineBucket,
  TimelineBucketItem,
  TimelineBucketTemplate,
  TimelineTodo,
  TimelineTodoType,
} from './estate-timeline';
import { TimelineBucketStatus, TimelineJourney } from './estate-timeline';
import {
  buyerMovingInBucketTemplateFactory,
  buyerTakeOverBucketTemplateFactory,
  sellerAudienceAnalysisBucketTemplateFactory,
  sellerBiddingRoundBucketTemplateFactory,
  sellerFinishingBucketTemplateFactory,
  sellerMarketingBucketTemplateFactory,
  sellerPreparationBucketTemplateFactory,
  sellerViewingBucketTemplateFactory,
} from './estate-timeline-buckets';
import {
  getChecklistTodoForBucket,
  getEventTodoForBucket,
  getSellerPreparationInputDatesTodo,
} from './estate-timeline-todo';

export type EstateTimelineService = {
  getJourneyForUserEstateRelation(userEstateRelation: UserEstateRelation | null): TimelineJourney;
  getTimelineForJourney(journey: TimelineJourney, userID?: User['id'], estate?: EstateMongoose): Promise<Timeline>;
  getBucketsForJourney(
    journey: TimelineJourney,
    userID?: User['id'],
    estate?: EstateMongoose,
  ): Promise<TimelineBucket[]>;
  getTimelineTodo(timeline: Timeline): TimelineTodo[];
};

export const estateTimelineServiceFactory = ({
  estateChecklistService,
  estateEventService,
}: {
  estateChecklistService: EstateChecklistService;
  estateEventService: EstateEventService;
}): EstateTimelineService => {
  const getJourneyForUserEstateRelation = (userEstateRelation: UserEstateRelation | null): TimelineJourney => {
    switch (userEstateRelation) {
      case UserEstateRelation.SELLER:
        return TimelineJourney.SELLER;
      case UserEstateRelation.BUYER:
        return TimelineJourney.BUYER;
      case UserEstateRelation.BROKER:
        return TimelineJourney.SELLER;
      default:
        return TimelineJourney.PROSPECT;
    }
  };

  const getTimelineForJourney = async (
    journey: TimelineJourney,
    userID?: User['id'],
    estate?: EstateMongoose,
  ): Promise<Timeline> => {
    return {
      estateID: estate?.estateId || null,
      journeyType: journey,
      digitalInspectionUrl: await getDigitalInspectionUrl(estate),
      buckets: await getBucketsForJourney(journey, userID, estate),
      broker: (estate?.employee as EmployeeMongoose) || null,
    };
  };

  const getBucketsForSellerJourney = async (
    userID?: User['id'],
    estate?: EstateMongoose,
  ): Promise<TimelineBucket[]> => {
    const checklists = await estateChecklistService.getChecklist(EstateChecklistType.SELLER, userID, estate?.estateId);
    const events = await estateEventService.getEstateEvents(userID, estate?.estateId);

    return processBucketTemplates(
      [
        sellerPreparationBucketTemplateFactory(checklists, events),
        sellerAudienceAnalysisBucketTemplateFactory(),
        sellerMarketingBucketTemplateFactory(events, estate),
        sellerViewingBucketTemplateFactory(checklists, estate),
        sellerBiddingRoundBucketTemplateFactory(events, estate),
        sellerFinishingBucketTemplateFactory(checklists, events, estate),
      ],
      estate,
    );
  };

  const getBucketsForBuyerJourney = async (userID?: User['id'], estate?: EstateMongoose): Promise<TimelineBucket[]> => {
    const checklists = await estateChecklistService.getChecklist(EstateChecklistType.BUYER, userID, estate?.estateId);
    const events = await estateEventService.getEstateEvents(userID, estate?.estateId);

    return processBucketTemplates(
      [buyerTakeOverBucketTemplateFactory(checklists, events, estate), buyerMovingInBucketTemplateFactory(checklists)],
      estate,
    );
  };

  const getBucketsForJourney = async (
    journey: TimelineJourney,
    userID?: User['id'],
    estate?: EstateMongoose,
  ): Promise<TimelineBucket[]> => {
    switch (journey) {
      case TimelineJourney.SELLER:
        return await getBucketsForSellerJourney(userID, estate);
      case TimelineJourney.BUYER:
        return await getBucketsForBuyerJourney(userID, estate);
      default:
        return [];
    }
  };

  const getTimelineTodo = (timeline: Timeline): TimelineTodo[] => {
    const firstActiveBucket = timeline.buckets.find((bucket) => bucket.status === TimelineBucketStatus.ACTIVE);

    if (!firstActiveBucket) {
      return [];
    }

    const sellerPreparationInputDatesTodo = getSellerPreparationInputDatesTodo(firstActiveBucket);
    const checklistTodo = !sellerPreparationInputDatesTodo ? getChecklistTodoForBucket(firstActiveBucket) : null;
    const eventTodo = getEventTodoForBucket(firstActiveBucket);

    return [sellerPreparationInputDatesTodo, checklistTodo, eventTodo].filter(
      (todo) => todo !== null,
    ) as TimelineTodo[];
  };

  return {
    getJourneyForUserEstateRelation,
    getTimelineForJourney,
    getBucketsForJourney,
    getTimelineTodo,
  };
};

export const itemToTodoTransformer = (
  title: string,
  item: TimelineBucketItem,
  type: TimelineTodoType,
): TimelineTodo => {
  return {
    title,
    type,
    item,
  };
};

export const processBucketTemplates = (
  bucketTemplates: TimelineBucketTemplate[],
  estate?: EstateMongoose,
): TimelineBucket[] => {
  const lastActiveBucketIndex = findLastIndex<TimelineBucketTemplate>((bucketTemplate) =>
    bucketTemplate.isActive(estate),
  )(bucketTemplates);

  return bucketTemplates.reduce((buckets: TimelineBucket[], bucket, index) => {
    const isBeforeLastActiveBucket = index < lastActiveBucketIndex;
    const isLastActiveBucket = index === lastActiveBucketIndex;
    const isUserCompleted = bucket.isUserCompletable && bucket.items.every((item) => item?.isComplete);

    const previousBucket = index > 0 ? buckets[buckets.length - 1] : null;
    const isEmptyBucketAfterACompletedBucket =
      previousBucket?.status === TimelineBucketStatus.COMPLETED && !bucket.items.length;

    const isCompleted = isBeforeLastActiveBucket || isUserCompleted;
    const isActive = isLastActiveBucket || isEmptyBucketAfterACompletedBucket;

    return [
      ...buckets,
      {
        ...bucket,
        step: index + 1,
        status: isCompleted
          ? TimelineBucketStatus.COMPLETED
          : isActive
          ? TimelineBucketStatus.ACTIVE
          : TimelineBucketStatus.INACTIVE,
      },
    ];
  }, []);
};

type DigitalInspectionUrlResponse = {
  success: boolean;
  entries: {
    url: string;
  }[];
};

const getDigitalInspectionUrl = async (estate?: EstateMongoose): Promise<string | null> => {
  if (!estate) {
    return null;
  }

  try {
    const response = await axios.post<DigitalInspectionUrlResponse>(
      'https://befaring.nordvikbolig.no/actions/common-module/befaring/url',
      {
        estateId: estate.estateId,
      },
    );

    if (!response.data.success || !response.data.entries.length) {
      return null;
    }

    return response.data.entries[0].url;
  } catch {
    return null;
  }
};
