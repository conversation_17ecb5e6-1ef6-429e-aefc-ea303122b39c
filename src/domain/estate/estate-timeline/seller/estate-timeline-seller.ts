import type { EstateTimelineItem } from '../estate-timeline-item';

export enum EstateTimelineSellerBucketName {
  PREPARATION = 'Forberedelse',
  MARKETING = 'Markedsføring',
  VIEWING = 'Visning',
  BIDDING = 'Budrunde',
  FINISHING = 'Etterbehandling',
}

export type EstateTimelineSellerSection = {
  name: EstateTimelineSellerBucketName;
  items: EstateTimelineItem[];
  isActive: boolean;
  logName?: string;
};

export type EstateTimelineBucketsStatus = {
  preparation: boolean;
  analysis: boolean;
  marketing: boolean;
  viewing: boolean;
  bidding: boolean;
  finishing: boolean;
};
