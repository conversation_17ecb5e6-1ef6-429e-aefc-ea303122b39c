import { addDays, differenceInDays, isPast, isSameDay } from 'date-fns';
import { getFirstLastViewingDates } from '../../../recommendation-engine/utils';
import { EstateVitecStatus } from '../../estate';
import { EstateActivityType } from '../../estate-activity/estate-activity';
import { EstateChecklistID } from '../../estate-checklist/estate-checklist';
import type { EstateMongoose } from '../../mongo/estate.mongoose-types';
import { EstateTimelineItemType } from '../estate-timeline-item';
import type { EstateTimelineBucketsStatus, EstateTimelineSellerSection } from './estate-timeline-seller';
import { EstateTimelineSellerBucketName } from './estate-timeline-seller';

export type EstateTimelineSellerService = {
  getActiveBuckets({
    estateStatus,
    viewings,
  }: {
    estateStatus: EstateVitecStatus;
    viewings: EstateMongoose['showings'];
  }): EstateTimelineBucketsStatus;
  getTimeline(estate: EstateMongoose): EstateTimelineSellerSection[];
  getActiveBucketName(estate: EstateMongoose): EstateTimelineSellerBucketName;
};

export const estateTimelineSellerServiceFactory = (): EstateTimelineSellerService => {
  const getISOString = (date: Date | undefined): string => {
    if (!date) {
      return '';
    }
    return date.toISOString();
  };

  const getActiveBuckets = ({
    estateStatus,
    viewings,
  }: {
    estateStatus: EstateVitecStatus;
    viewings: EstateMongoose['showings'];
  }): EstateTimelineBucketsStatus => {
    const { firstViewingDate, lastViewingDate } = getFirstLastViewingDates(viewings);
    const firstViewingInPast = firstViewingDate ? isPast(firstViewingDate) : false;
    const lastViewingInPast = lastViewingDate ? isPast(lastViewingDate) : false;
    const lastViewingSameDay = lastViewingDate ? isSameDay(lastViewingDate, new Date()) : false;
    const sevenDaysSinceLastViewing = lastViewingDate ? differenceInDays(Date.now(), lastViewingDate) > 7 : false;
    return {
      preparation: estateStatus === EstateVitecStatus.PREPARATION,
      analysis: false,
      marketing: estateStatus === EstateVitecStatus.FOR_SALE && (!firstViewingInPast || sevenDaysSinceLastViewing),
      viewing:
        estateStatus === EstateVitecStatus.FOR_SALE && firstViewingInPast && (!lastViewingInPast || lastViewingSameDay),
      bidding: estateStatus === EstateVitecStatus.FOR_SALE && lastViewingInPast && !sevenDaysSinceLastViewing,
      finishing: estateStatus === EstateVitecStatus.OVERSOLD || estateStatus === EstateVitecStatus.RESERVED,
    };
  };

  const getActiveBucketName = (estate: EstateMongoose): EstateTimelineSellerBucketName => {
    const activeBuckets = getActiveBuckets({
      viewings: estate.showings,
      estateStatus: estate.status,
    });

    if (activeBuckets.preparation) {
      return EstateTimelineSellerBucketName.PREPARATION;
    }
    if (activeBuckets.marketing) {
      return EstateTimelineSellerBucketName.MARKETING;
    }
    if (activeBuckets.viewing) {
      return EstateTimelineSellerBucketName.VIEWING;
    }
    if (activeBuckets.bidding) {
      return EstateTimelineSellerBucketName.BIDDING;
    }
    return EstateTimelineSellerBucketName.FINISHING;
  };

  const getBiddingActivityDate = (viewingActivities: EstateMongoose['showings']): Date | null => {
    const { lastViewingDate } = getFirstLastViewingDates(viewingActivities);

    if (lastViewingDate !== null) {
      return addDays(lastViewingDate, 1);
    }

    return null;
  };

  type ViewingActivity = {
    start: Date;
    name: string;
  };

  const viewingActivityMapper = (estate: EstateMongoose): ViewingActivity[] => {
    return estate.showings
      .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime())
      .map((e, i) => {
        return {
          start: new Date(e.start),
          name: `Visning #${i + 1}`,
        };
      });
  };

  const privateViewingActivityMapper = (estate: EstateMongoose): ViewingActivity[] => {
    return estate.activities
      .filter((e) => e.type === EstateActivityType.PRIVATE_VIEWING)
      .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime())
      .map((e, i) => {
        return {
          start: new Date(e.start),
          name: `Privatvisning #${i + 1}`,
        };
      });
  };

  const getTimeline = (estate: EstateMongoose): EstateTimelineSellerSection[] => {
    const viewingActivities = estate.showings.sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());
    const timelineBucketsStatus = getActiveBuckets({
      estateStatus: estate.status,
      viewings: estate.showings,
    });
    const biddingActivityDate = getBiddingActivityDate(viewingActivities);

    const mergedViewingActivites = viewingActivityMapper(estate)
      .concat(privateViewingActivityMapper(estate))
      .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());
    const appraiserDate = estate.activities.find((e) => e.type === EstateActivityType.APPRAISER);
    const photographerDate = estate.activities.find((e) => e.type === EstateActivityType.PHOTOGRAPHER);
    const bucketItems = [
      {
        name: EstateTimelineSellerBucketName.PREPARATION,
        items: [
          {
            id: EstateChecklistID.INFORMATION_GATHERING,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste for informasjonsinnsamling',
            isComplete: false,
            value: null,
            logName: 'preparation_information_gathering',
          },
          {
            id: 'interiorGuidanceID',
            type: EstateTimelineItemType.ITEM,
            name: 'Interiørveiledning',
            isComplete: false,
            value: null,
          },
          {
            id: EstateChecklistID.PHOTOGRAPHER,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste før fotografering',
            isComplete: false,
            value: null,
            logName: 'preparation_photographer',
          },
          {
            id: 'photographerID',
            type: EstateTimelineItemType.ITEM,
            name: 'Fotograf',
            isComplete: photographerDate?.end ? isPast(photographerDate?.end) : false,
            value: photographerDate?.start ? getISOString(photographerDate?.start) : null,
          },
          {
            id: EstateChecklistID.APPRAISER,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste før takstmann kommer',
            isComplete: false,
            value: null,
            logName: 'preparation_appraiser',
          },
          {
            id: 'appraiserID',
            type: EstateTimelineItemType.ITEM,
            name: 'Takstmann',
            isComplete: appraiserDate?.end ? isPast(appraiserDate?.end) : false,
            value: appraiserDate?.start ? getISOString(appraiserDate?.start) : null,
          },
        ],
        isActive: timelineBucketsStatus.preparation,
        logName: 'preparation',
      },
      {
        name: EstateTimelineSellerBucketName.MARKETING,
        items: [
          {
            id: 'targetSegmentAnalysisID',
            type: EstateTimelineItemType.ITEM,
            name: 'Målgruppeanalyse',
            isComplete: false,
            value: null,
          },
          {
            id: 'searchInNordvikID',
            type: EstateTimelineItemType.ITEM,
            name: 'Søk i Nordvik sitt boligsøkerregister',
            isComplete: false,
            value: null,
          },
          {
            id: 'listingAvailableID',
            type: EstateTimelineItemType.ITEM,
            name: 'Bolig tilgjengelig i «Kommer for salg»',
            isComplete: false,
            value: null,
          },
          {
            id: 'adLiveInCoreChannelsID',
            type: EstateTimelineItemType.ITEM,
            name: 'Markedsføring på Finn.no og Nordvikbolig.no',
            isComplete: isPast(estate.finnPublishDate),
            value: getISOString(estate.finnPublishDate),
          },
          {
            id: 'adLiveInWiderChannelsID',
            type: EstateTimelineItemType.ITEM,
            name: 'Markedsføring utover «Grunnpakke»',
            isComplete: false,
            value: null,
          },
        ],
        isActive: timelineBucketsStatus.marketing,
        logName: 'marketing',
      },
      {
        name: EstateTimelineSellerBucketName.VIEWING,
        items: [
          {
            id: EstateChecklistID.VIEWING,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste før visning',
            isComplete: false,
            value: null,
            logName: 'viewing_viewing',
          },
          ...mergedViewingActivites.map((viewingActivity) => ({
            id: 'viewingID',
            type: EstateTimelineItemType.ITEM,
            name: viewingActivity.name,
            isComplete: isPast(viewingActivity.start),
            value: getISOString(viewingActivity.start),
          })),
        ],
        isActive: timelineBucketsStatus.viewing,
        logName: 'viewing',
      },
      {
        name: EstateTimelineSellerBucketName.BIDDING,
        items: [
          {
            id: 'biddingRoundID',
            type: EstateTimelineItemType.ITEM,
            name: 'Budrunde',
            isComplete: biddingActivityDate !== null && isPast(biddingActivityDate),
            value: biddingActivityDate !== null ? getISOString(biddingActivityDate) : null,
          },
        ],
        isActive: timelineBucketsStatus.bidding,
        logName: 'bidding_round',
      },
      {
        name: EstateTimelineSellerBucketName.FINISHING,
        items: [
          {
            id: EstateChecklistID.CONTRACT_SIGNING,
            type: EstateTimelineItemType.CHECKLIST,
            name: 'Fullfør sjekkliste før kontraktsignering',
            isComplete: false,
            value: null,
            logName: 'finish_contract_signing',
          },
          {
            id: 'contractSigningID',
            type: EstateTimelineItemType.ITEM,
            name: 'Kontraktsignering',
            isComplete: isPast(estate.contractMeetingDate),
            value: getISOString(estate.contractMeetingDate),
          },
          {
            id: EstateChecklistID.HANDOVER,
            type: EstateTimelineItemType.CHECKLIST,
            /*name: 'Finn frem alle kvitteringer, bruksanvisninger og lignende som følger boligen',*/
            name: 'Fullfør sjekkliste før overtakelse',
            isComplete: false,
            value: null,
            logName: 'finish_handover',
          },
          {
            id: 'handoverID',
            type: EstateTimelineItemType.ITEM,
            name: 'Overtakelse',
            isComplete: isPast(estate.takeOverDate),
            value: getISOString(estate.takeOverDate),
          },
          {
            id: 'finalSettlementID',
            type: EstateTimelineItemType.ITEM,
            name: 'Endelig oppgjør',
            isComplete: false,
            value: null,
          },
        ],
        isActive: timelineBucketsStatus.finishing,
        logName: 'finish',
      },
    ];

    const firstActive = bucketItems.find((item) => item.isActive) || { name: null };
    return bucketItems.map((item) => ({ ...item, isActive: item.name === firstActive.name }));
  };

  return {
    getActiveBuckets,
    getTimeline,
    getActiveBucketName,
  };
};
