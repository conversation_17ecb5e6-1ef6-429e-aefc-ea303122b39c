import { addDays } from 'date-fns';
import type { EstateEventService } from '../estate-event/estate-event.service';
import { estateChecklistServiceFixtureFactory } from '../../../framework/test/fixtures/services/estateChecklistServiceFixtureFactory';
import { estateEventServiceFixtureFactory } from '../../../framework/test/fixtures/services/estateEventServiceFixtureFactory';
import { EstateBuyerChecklistID, EstateChecklistID, EstateChecklistType } from '../estate-checklist/estate-checklist';
import { estateChecklistResponseFixtureFactory } from '../../../framework/test/fixtures/misc/estateChecklistResponseFixtureFactory';
import { EstateEventID, EstateEventType } from '../estate-event/estate-event';
import {
  estateMongooseFactory,
  estateMongooseFactoryWithEstateId,
  estateMongooseFactoryWithStatus,
  estateMongooseFactoryWithStatusAndShowings,
  estateMongooseFactoryWithTakeOverDate,
} from '../../../framework/test/helpers/estate-mongoose-factory';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import { timelineBucketTemplateFixtureFactory } from '../../../framework/test/fixtures/misc/timelineBucketTemplateFixtureFactory';
import type { EstateChecklistService } from '../estate-checklist/estate-checklist.service';
import { EstateVitecStatus, UserEstateRelation } from '../estate';
import { estateEventWithDateFixtureFactory } from '../../../framework/test/fixtures/misc/estateEventFixtureWithDateFactory';
import { estateChecklistTodoFixtureFactory } from '../../../framework/test/fixtures/misc/estateChecklistTodoFixtureFactory';
import { timelineFixtureFactory } from '../../../framework/test/fixtures/misc/timelineFixtureFactory';
import { timelineBucketFixtureFactory } from '../../../framework/test/fixtures/misc/timelineBucketFixtureFactory';
import type { EmployeeMongoose } from '../mongo/employee/employee';
import { TimelineBucketID, TimelineBucketItemType, TimelineBucketStatus, TimelineJourney } from './estate-timeline';
import type { Timeline, TimelineBucket, TimelineBucketTemplate } from './estate-timeline';
import { estateTimelineServiceFactory, processBucketTemplates } from './estate-timeline.service';
import type { EstateTimelineService } from './estate-timeline.service';

const mockDefaultChecklistsAndEvents = (
  estateChecklistService: EstateChecklistService,
  estateEventService: EstateEventService,
  isComplete: boolean,
  date: Date,
): void => {
  estateChecklistService.getChecklist = jest
    .fn()
    .mockReturnValueOnce([
      estateChecklistResponseFixtureFactory(EstateChecklistID.INFORMATION_GATHERING, EstateChecklistType.SELLER, [
        estateChecklistTodoFixtureFactory(
          EstateChecklistID.INFORMATION_GATHERING,
          EstateChecklistType.SELLER,
          isComplete,
        ),
      ]),
      estateChecklistResponseFixtureFactory(EstateChecklistID.PHOTOGRAPHER, EstateChecklistType.SELLER, [
        estateChecklistTodoFixtureFactory(EstateChecklistID.PHOTOGRAPHER, EstateChecklistType.SELLER, isComplete),
      ]),
      estateChecklistResponseFixtureFactory(EstateChecklistID.APPRAISER, EstateChecklistType.SELLER, [
        estateChecklistTodoFixtureFactory(EstateChecklistID.APPRAISER, EstateChecklistType.SELLER, isComplete),
      ]),
      estateChecklistResponseFixtureFactory(EstateChecklistID.VIEWING, EstateChecklistType.SELLER, [
        estateChecklistTodoFixtureFactory(EstateChecklistID.VIEWING, EstateChecklistType.SELLER, isComplete),
      ]),
      estateChecklistResponseFixtureFactory(EstateChecklistID.CONTRACT_SIGNING, EstateChecklistType.SELLER, [
        estateChecklistTodoFixtureFactory(EstateChecklistID.CONTRACT_SIGNING, EstateChecklistType.SELLER, isComplete),
      ]),
      estateChecklistResponseFixtureFactory(EstateChecklistID.HANDOVER, EstateChecklistType.SELLER, [
        estateChecklistTodoFixtureFactory(EstateChecklistID.HANDOVER, EstateChecklistType.SELLER, isComplete),
      ]),
      estateChecklistResponseFixtureFactory(EstateBuyerChecklistID.CONTRACT_SIGNING, EstateChecklistType.BUYER, [
        estateChecklistTodoFixtureFactory(
          EstateBuyerChecklistID.CONTRACT_SIGNING,
          EstateChecklistType.BUYER,
          isComplete,
        ),
      ]),
      estateChecklistResponseFixtureFactory(EstateBuyerChecklistID.TAKEOVER, EstateChecklistType.BUYER, [
        estateChecklistTodoFixtureFactory(EstateBuyerChecklistID.MOVE_IN, EstateChecklistType.BUYER, isComplete),
      ]),
      estateChecklistResponseFixtureFactory(EstateBuyerChecklistID.MOVE_IN, EstateChecklistType.BUYER, [
        estateChecklistTodoFixtureFactory(EstateBuyerChecklistID.MOVE_IN, EstateChecklistType.BUYER, isComplete),
      ]),
    ]);

  estateEventService.getEstateEvents = jest
    .fn()
    .mockReturnValueOnce([
      estateEventWithDateFixtureFactory(EstateEventID.INTERIOR_GUIDANCE, EstateEventType.USER, date),
      estateEventWithDateFixtureFactory(EstateEventID.PHOTOGRAPHER, EstateEventType.USER, date),
      estateEventWithDateFixtureFactory(EstateEventID.APPRAISER, EstateEventType.USER, date),
      estateEventWithDateFixtureFactory(EstateEventID.AD_LIVE_IN_CORE_CHANNELS, EstateEventType.VITEC, date),
      estateEventWithDateFixtureFactory(EstateEventID.BIDDING_ROUND, EstateEventType.VITEC, date),
      estateEventWithDateFixtureFactory(EstateEventID.CONTRACT_SIGNING, EstateEventType.VITEC, date),
      estateEventWithDateFixtureFactory(EstateEventID.TAKE_OVER, EstateEventType.VITEC, date),
    ]);
};

describe('estate timeline service', () => {
  let estateChecklistService: EstateChecklistService;
  let estateEventService: EstateEventService;
  let estateTimelineService: EstateTimelineService;

  beforeEach(() => {
    estateChecklistService = estateChecklistServiceFixtureFactory();
    estateEventService = estateEventServiceFixtureFactory();

    const tomorrow = addDays(new Date(), 1);
    mockDefaultChecklistsAndEvents(estateChecklistService, estateEventService, false, tomorrow);

    estateTimelineService = estateTimelineServiceFactory({
      estateChecklistService,
      estateEventService,
    });
  });

  describe('#getJourneyForEstate', () => {
    describe('given a null user estate relation', () => {
      it('should return prospect', () => {
        expect(estateTimelineService.getJourneyForUserEstateRelation(null)).toEqual(TimelineJourney.PROSPECT);
      });
    });

    describe('given a seller user estate relation', () => {
      it('should return seller', () => {
        expect(estateTimelineService.getJourneyForUserEstateRelation(UserEstateRelation.SELLER)).toEqual(
          TimelineJourney.SELLER,
        );
      });
    });

    describe('given a buyer user estate relation', () => {
      it('should return buyer', () => {
        expect(estateTimelineService.getJourneyForUserEstateRelation(UserEstateRelation.BUYER)).toEqual(
          TimelineJourney.BUYER,
        );
      });
    });
  });

  describe.each([TimelineJourney.SELLER, TimelineJourney.BUYER])('#getTimelineForJourney (%s)', (journey) => {
    describe('given no user context', () => {
      let timeline: Timeline;

      beforeEach(async () => {
        timeline = await estateTimelineService.getTimelineForJourney(journey);
      });

      it('should contain some buckets', () => {
        expect(timeline.buckets.length).toBeGreaterThan(0);
      });

      it('should contain the journey type', () => {
        expect(timeline.journeyType).toEqual(journey);
      });

      it('should not contain an estate id', () => {
        expect(timeline.estateID).toBeNull();
      });

      it('should not contain a digital inspection url', () => {
        expect(timeline.digitalInspectionUrl).toBeNull();
      });

      it('should not contain a broker', () => {
        expect(timeline.broker).toBeNull();
      });
    });

    describe('given a user context', () => {
      let timeline: Timeline;
      let estate: EstateMongoose;

      beforeEach(async () => {
        estate = {
          ...estateMongooseFactoryWithEstateId('[fake estate id]'),
          employee: {} as EmployeeMongoose,
        };
        timeline = await estateTimelineService.getTimelineForJourney(journey, undefined, estate);
      });

      it('should contain some buckets', () => {
        expect(timeline.buckets.length).toBeGreaterThan(0);
      });

      it('should contain the journey type', () => {
        expect(timeline.journeyType).toEqual(journey);
      });

      it('should contain an estate id', () => {
        expect(timeline.estateID).toEqual(estate.estateId);
      });

      it('should contain a broker', () => {
        expect(timeline.broker).not.toBeNull();
      });
    });
  });

  describe('#getTimelineForJourney (user context acceptance tests)', () => {
    describe('seller journey', () => {
      describe('given an estate in the preparation bucket (with incomplete items)', () => {
        let timeline: Timeline;
        let estate: EstateMongoose;

        beforeEach(async () => {
          estate = estateMongooseFactoryWithStatus(EstateVitecStatus.PREPARATION);
          timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.SELLER, undefined, estate);
        });

        it('should contain an active preparation bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.PREPARATION);
          expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
        });

        it('should contain an inactive audience analysis bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.AUDIENCE_ANALYSIS);
          expect(bucket?.status).toEqual(TimelineBucketStatus.INACTIVE);
        });
      });

      describe('given an estate in the preparation bucket (with all items completed)', () => {
        let timeline: Timeline;
        let estate: EstateMongoose;

        beforeEach(async () => {
          mockDefaultChecklistsAndEvents(estateChecklistService, estateEventService, true, new Date(0));

          estate = estateMongooseFactoryWithStatus(EstateVitecStatus.PREPARATION);
          timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.SELLER, undefined, estate);
        });

        it('should contain a completed preparation bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.PREPARATION);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain an active audience analysis bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.AUDIENCE_ANALYSIS);
          expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
        });
      });

      describe('given an estate in the marketing bucket', () => {
        let timeline: Timeline;
        let estate: EstateMongoose;

        const afterSixDays = addDays(new Date(), 6).toISOString();

        beforeEach(async () => {
          estate = estateMongooseFactoryWithStatusAndShowings(EstateVitecStatus.FOR_SALE, [
            {
              showingId: '[fake showing id]',
              start: afterSixDays,
              end: afterSixDays,
            },
          ]);
          timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.SELLER, undefined, estate);
        });

        it('should contain a completed preparation bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.PREPARATION);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed audience analysis bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.AUDIENCE_ANALYSIS);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain an active marketing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.MARKETING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
        });

        it('should contain an inactive viewing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.VIEWING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.INACTIVE);
        });
      });

      describe('given an estate in the viewing bucket', () => {
        let timeline: Timeline;
        let estate: EstateMongoose;

        const yesterday = addDays(new Date(), -1).toISOString();
        const tomorrow = addDays(new Date(), 1).toISOString();

        beforeEach(async () => {
          estate = estateMongooseFactoryWithStatusAndShowings(EstateVitecStatus.FOR_SALE, [
            {
              showingId: '[fake showing id]',
              start: yesterday,
              end: yesterday,
            },
            {
              showingId: '[fake showing id]',
              start: tomorrow,
              end: tomorrow,
            },
          ]);
          timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.SELLER, undefined, estate);
        });

        it('should contain a completed preparation bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.PREPARATION);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed audience analysis bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.AUDIENCE_ANALYSIS);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed marketing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.MARKETING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain an active viewing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.VIEWING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
        });

        it('should contain an inactive bidding round bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.BIDDING_ROUND);
          expect(bucket?.status).toEqual(TimelineBucketStatus.INACTIVE);
        });
      });

      describe('given an estate in the bidding round bucket', () => {
        let timeline: Timeline;
        let estate: EstateMongoose;

        const sixDaysAgo = addDays(new Date(), -6).toISOString();

        beforeEach(async () => {
          estate = estateMongooseFactoryWithStatusAndShowings(EstateVitecStatus.FOR_SALE, [
            {
              showingId: '[fake showing id]',
              start: sixDaysAgo,
              end: sixDaysAgo,
            },
          ]);
          timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.SELLER, undefined, estate);
        });

        it('should contain a completed preparation bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.PREPARATION);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed audience analysis bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.AUDIENCE_ANALYSIS);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed marketing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.MARKETING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed viewing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.VIEWING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain an active bidding round bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.BIDDING_ROUND);
          expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
        });

        it('should contain an inactive finishing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.FINISHING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.INACTIVE);
        });
      });

      describe('given an estate in the finishing bucket (with no items completed)', () => {
        let timeline: Timeline;
        let estate: EstateMongoose;

        beforeEach(async () => {
          estate = estateMongooseFactoryWithStatus(EstateVitecStatus.OVERSOLD);
          timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.SELLER, undefined, estate);
        });

        it('should contain a completed preparation bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.PREPARATION);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed audience analysis bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.AUDIENCE_ANALYSIS);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed marketing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.MARKETING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed viewing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.VIEWING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed bidding round bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.BIDDING_ROUND);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain an active finishing round bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.FINISHING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
        });
      });

      describe('given an estate in the finishing bucket (with all items completed)', () => {
        let timeline: Timeline;
        let estate: EstateMongoose;

        beforeEach(async () => {
          mockDefaultChecklistsAndEvents(estateChecklistService, estateEventService, true, new Date(0));

          estate = estateMongooseFactoryWithStatus(EstateVitecStatus.OVERSOLD);
          timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.SELLER, undefined, estate);
        });

        it('should contain a completed preparation bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.PREPARATION);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed audience analysis bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.AUDIENCE_ANALYSIS);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed marketing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.MARKETING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed viewing bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.VIEWING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed bidding round bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.BIDDING_ROUND);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should contain a completed finishing round bucket', () => {
          const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.FINISHING);
          expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
        });
      });

      describe('buyer journey', () => {
        describe('given an estate in the take over bucket', () => {
          let timeline: Timeline;
          let estate: EstateMongoose;

          const yesterday = addDays(new Date(), -1);
          const tomorrow = addDays(new Date(), 1);

          beforeEach(async () => {
            estate = {
              ...estateMongooseFactory(),
              soldDate: yesterday,
              takeOverDate: tomorrow,
            };
            timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.BUYER, undefined, estate);
          });

          it('should contain an active take over bucket', () => {
            const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.TAKEOVER);
            expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
          });

          it('should contain an inactive moving in bucket', () => {
            const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.MOVING_IN);
            expect(bucket?.status).toEqual(TimelineBucketStatus.INACTIVE);
          });
        });

        describe('given an estate in the moving in bucket', () => {
          let timeline: Timeline;
          let estate: EstateMongoose;

          const yesterday = addDays(new Date(), -1);

          beforeEach(async () => {
            estate = estateMongooseFactoryWithTakeOverDate(yesterday);
            timeline = await estateTimelineService.getTimelineForJourney(TimelineJourney.BUYER, undefined, estate);
          });

          it('should contain a completed take over bucket', () => {
            const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.TAKEOVER);
            expect(bucket?.status).toEqual(TimelineBucketStatus.COMPLETED);
          });

          it('should contain an active moving in bucket', () => {
            const bucket = timeline.buckets.find((bucket) => bucket.id === TimelineBucketID.MOVING_IN);
            expect(bucket?.status).toEqual(TimelineBucketStatus.ACTIVE);
          });
        });
      });
    });

    describe('#getBucketsForJourney', () => {
      describe('given a seller journey', () => {
        describe('given no user context', () => {
          let buckets: TimelineBucket[];

          beforeEach(async () => {
            buckets = await estateTimelineService.getBucketsForJourney(TimelineJourney.SELLER);
          });

          it('should return 6 buckets', async () => {
            expect(buckets).toHaveLength(6);
          });

          it('should return all buckets belonging to the seller journey', async () => {
            const sellerBucketIds = [
              TimelineBucketID.PREPARATION,
              TimelineBucketID.AUDIENCE_ANALYSIS,
              TimelineBucketID.VIEWING,
              TimelineBucketID.MARKETING,
              TimelineBucketID.BIDDING_ROUND,
              TimelineBucketID.FINISHING,
            ];

            expect(buckets.every((bucket) => sellerBucketIds.includes(bucket.id))).toBeTruthy();
          });

          it('should calculate the bucket steps correctly', async () => {
            buckets.forEach((bucket, i) => expect(bucket.step === i + 1));
          });

          it('should only return inactive buckets', async () => {
            expect(buckets.every((bucket) => bucket.status === TimelineBucketStatus.INACTIVE)).toBeTruthy();
          });
        });

        describe('given a user context', () => {
          let estate: EstateMongoose;
          let buckets: TimelineBucket[];

          beforeEach(async () => {
            estate = estateMongooseFactory();
            buckets = await estateTimelineService.getBucketsForJourney(TimelineJourney.SELLER, undefined, estate);
          });

          it('should return 6 buckets', async () => {
            expect(buckets).toHaveLength(6);
          });

          it('should return all buckets belonging to the seller journey', async () => {
            const sellerBucketIds = [
              TimelineBucketID.PREPARATION,
              TimelineBucketID.AUDIENCE_ANALYSIS,
              TimelineBucketID.VIEWING,
              TimelineBucketID.MARKETING,
              TimelineBucketID.BIDDING_ROUND,
              TimelineBucketID.FINISHING,
            ];

            expect(buckets.every((bucket) => sellerBucketIds.includes(bucket.id))).toBeTruthy();
          });

          it('should calculate the bucket steps correctly', async () => {
            buckets.forEach((bucket, i) => expect(bucket.step === i + 1));
          });

          it('should only return inactive buckets', async () => {
            expect(buckets.every((bucket) => bucket.status === TimelineBucketStatus.INACTIVE)).toBeTruthy();
          });
        });
      });

      describe('given a buyer journey', () => {
        describe('given no user context', () => {
          let buckets: TimelineBucket[];

          beforeEach(async () => {
            buckets = await estateTimelineService.getBucketsForJourney(TimelineJourney.BUYER);
          });

          it('should return 2 buckets', async () => {
            expect(buckets).toHaveLength(2);
          });

          it('should return all buckets belonging to the buyer journey', async () => {
            const buyerBucketIds = [TimelineBucketID.TAKEOVER, TimelineBucketID.MOVING_IN];

            expect(buckets.every((bucket) => buyerBucketIds.includes(bucket.id))).toBeTruthy();
          });

          it('should calculate the bucket steps correctly', async () => {
            buckets.map((bucket, i) => expect(bucket.step === i + 1));
          });

          it('should only return inactive buckets', async () => {
            expect(buckets.every((bucket) => bucket.status === TimelineBucketStatus.INACTIVE)).toBeTruthy();
          });
        });

        describe('given a user context', () => {
          let estate: EstateMongoose;
          let buckets: TimelineBucket[];

          beforeEach(async () => {
            estate = estateMongooseFactory();
            buckets = await estateTimelineService.getBucketsForJourney(TimelineJourney.BUYER, undefined, estate);
          });

          it('should return 2 buckets', async () => {
            expect(buckets).toHaveLength(2);
          });

          it('should return all buckets belonging to the buyer journey', async () => {
            const buyerBucketIds = [TimelineBucketID.TAKEOVER, TimelineBucketID.MOVING_IN];

            expect(buckets.every((bucket) => buyerBucketIds.includes(bucket.id))).toBeTruthy();
          });

          it('should calculate the bucket steps correctly', async () => {
            buckets.map((bucket, i) => expect(bucket.step === i + 1));
          });
        });
      });

      describe('given a prospect journey', () => {
        describe('given no user context', () => {
          let buckets: TimelineBucket[];

          beforeEach(async () => {
            buckets = await estateTimelineService.getBucketsForJourney(TimelineJourney.PROSPECT);
          });

          it('should return 0 buckets', async () => {
            expect(buckets).toHaveLength(0);
          });
        });

        describe('given a user context', () => {
          let estate: EstateMongoose;
          let buckets: TimelineBucket[];

          beforeEach(async () => {
            estate = estateMongooseFactory();
            buckets = await estateTimelineService.getBucketsForJourney(TimelineJourney.PROSPECT, undefined, estate);
          });

          it('should return 0 buckets', async () => {
            expect(buckets).toHaveLength(0);
          });
        });
      });
    });

    describe('#processBucketTemplates', () => {
      describe('given an empty list of buckets', () => {
        it('should return an empty array', () => {
          expect(processBucketTemplates([])).toEqual([]);
        });
      });

      describe('given a single bucket which is inactive', () => {
        let bucketTemplates: TimelineBucketTemplate[];
        let buckets: TimelineBucket[];

        beforeEach(() => {
          bucketTemplates = [
            timelineBucketTemplateFixtureFactory([], () => {
              return false;
            }),
          ];

          buckets = processBucketTemplates(bucketTemplates);
        });

        it('should be marked as inactive', () => {
          expect(buckets[0].status).toEqual(TimelineBucketStatus.INACTIVE);
        });
      });

      describe('given a single bucket which active', () => {
        let bucketTemplates: TimelineBucketTemplate[];
        let buckets: TimelineBucket[];

        beforeEach(() => {
          bucketTemplates = [
            timelineBucketTemplateFixtureFactory([], () => {
              return true;
            }),
          ];

          buckets = processBucketTemplates(bucketTemplates);
        });

        it('should be marked as active', () => {
          expect(buckets[0].status).toEqual(TimelineBucketStatus.ACTIVE);
        });
      });

      describe('given two inactive buckets', () => {
        let bucketTemplates: TimelineBucketTemplate[];
        let buckets: TimelineBucket[];

        beforeEach(() => {
          bucketTemplates = [
            timelineBucketTemplateFixtureFactory([], () => {
              return false;
            }),
            timelineBucketTemplateFixtureFactory([], () => {
              return false;
            }),
          ];

          buckets = processBucketTemplates(bucketTemplates);
        });

        it('should be marked as inactive', () => {
          expect(buckets[0].status).toEqual(TimelineBucketStatus.INACTIVE);
          expect(buckets[1].status).toEqual(TimelineBucketStatus.INACTIVE);
        });
      });

      describe('given two active buckets', () => {
        let bucketTemplates: TimelineBucketTemplate[];
        let buckets: TimelineBucket[];

        beforeEach(() => {
          bucketTemplates = [
            timelineBucketTemplateFixtureFactory([], () => {
              return true;
            }),
            timelineBucketTemplateFixtureFactory([], () => {
              return true;
            }),
          ];

          buckets = processBucketTemplates(bucketTemplates);
        });

        it('should mark the first one as completed', () => {
          expect(buckets[0].status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should mark the second one as active', () => {
          expect(buckets[1].status).toEqual(TimelineBucketStatus.ACTIVE);
        });
      });

      describe('given an inactive, user completable bucket with all items completed', () => {
        let bucketTemplates: TimelineBucketTemplate[];
        let buckets: TimelineBucket[];

        beforeEach(() => {
          bucketTemplates = [
            timelineBucketTemplateFixtureFactory(
              [],
              () => {
                return false;
              },
              true,
            ),
          ];

          buckets = processBucketTemplates(bucketTemplates);
        });

        it('should mark it as completed', () => {
          expect(buckets[0].status).toEqual(TimelineBucketStatus.COMPLETED);
        });
      });

      describe('given a completed bucket and a bucket with zero items', () => {
        let bucketTemplates: TimelineBucketTemplate[];
        let buckets: TimelineBucket[];

        beforeEach(() => {
          bucketTemplates = [
            timelineBucketTemplateFixtureFactory(
              [],
              () => {
                return false;
              },
              true,
            ),
            timelineBucketTemplateFixtureFactory([], () => {
              return false;
            }),
          ];

          buckets = processBucketTemplates(bucketTemplates);
        });

        it('should mark the first one as completed', () => {
          expect(buckets[0].status).toEqual(TimelineBucketStatus.COMPLETED);
        });

        it('should mark the second one as active', () => {
          expect(buckets[1].status).toEqual(TimelineBucketStatus.ACTIVE);
        });
      });
    });
  });

  describe('#getTimelineTodo', () => {
    describe('given a timeline with no active buckets', () => {
      const timeline = timelineFixtureFactory([]);

      it('should return no todos', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)).toHaveLength(0);
      });
    });

    describe('given a timeline with an empty active bucket', () => {
      const timeline = timelineFixtureFactory([timelineBucketFixtureFactory([])]);

      it('should return no todos', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)).toHaveLength(0);
      });
    });

    describe('given a timeline with an active bucket containing multiple incomplete events', () => {
      const timeline = timelineFixtureFactory([
        timelineBucketFixtureFactory([
          {
            id: '[fake id 1]',
            title: '[fake title 1]',
            isComplete: false,
            logName: '[fake log name]',
            date: addDays(new Date(), 1),
            type: TimelineBucketItemType.USER_EVENT,
          },
          {
            id: '[fake id 2]',
            title: '[fake title 2]',
            isComplete: false,
            logName: '[fake log name]',
            date: addDays(new Date(), 1),
            type: TimelineBucketItemType.USER_EVENT,
          },
        ]),
      ]);

      it('should return a todo', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)).toHaveLength(1);
      });

      it('should return the todo related to the first event', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)[0].title).toEqual('[fake title 1]');
      });
    });

    describe('given a timeline with an active bucket containing multiple incomplete checklists', () => {
      const timeline = timelineFixtureFactory([
        timelineBucketFixtureFactory([
          {
            id: '[fake id 1]',
            title: '[fake title 1]',
            isComplete: false,
            logName: '[fake log name]',
            items: [],
            type: TimelineBucketItemType.CHECKLIST,
            isIndependent: false,
          },
          {
            id: '[fake id 2]',
            title: '[fake title 2]',
            isComplete: true,
            logName: '[fake log name]',
            date: new Date(0),
            type: TimelineBucketItemType.USER_EVENT,
          },
          {
            id: '[fake id 3]',
            title: '[fake title 3]',
            isComplete: false,
            logName: '[fake log name]',
            items: [],
            type: TimelineBucketItemType.CHECKLIST,
            isIndependent: false,
          },
        ]),
      ]);

      it('should return a todo', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)).toHaveLength(1);
      });

      it('should return the todo related to the first checklist', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)[0].title).toEqual('Tips og råd før [fake title 2]');
      });
    });

    describe('given a timeline with an active preparation bucket containing an event without a date and a checklist', () => {
      const timeline = timelineFixtureFactory([
        {
          ...timelineBucketFixtureFactory([
            {
              id: '[fake id 1]',
              title: '[fake title 1]',
              isComplete: false,
              logName: '[fake log name]',
              items: [],
              type: TimelineBucketItemType.CHECKLIST,
              isIndependent: false,
            },
            {
              id: '[fake id 2]',
              title: '[fake title 2]',
              isComplete: false,
              logName: '[fake log name]',
              date: null,
              type: TimelineBucketItemType.USER_EVENT,
            },
          ]),
          id: TimelineBucketID.PREPARATION,
        },
      ]);

      it('should return one todo', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)).toHaveLength(1);
      });

      it('should return the todo related to the missing event dates', () => {
        expect(estateTimelineService.getTimelineTodo(timeline)[0].title).toEqual(
          'Sett datoer for interiørveiledning, fotograf og takstmann',
        );
      });
    });
  });
});
