import { addDays } from 'date-fns';
import { timelineBucketFixtureFactory } from '../../../framework/test/fixtures/misc/timelineBucketFixtureFactory';
import {
  areAllEventDatesSet,
  getChecklistTodoForBucket,
  getEventTodoForBucket,
  getFirstIncompleteChecklistIndex,
  getFirstIncompleteEvent,
  getSellerPreparationInputDatesTodo,
} from './estate-timeline-todo';
import { TimelineBucketID, TimelineBucketItemType, TimelineTodoType } from './estate-timeline';

describe('estate timeline todo', () => {
  describe('#getFirstIncompleteChecklistIndex', () => {
    describe('given no checklist in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([]);

      it('should return -1', () => {
        expect(getFirstIncompleteChecklistIndex(bucket)).toEqual(-1);
      });
    });

    describe('given a complete checklist in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: true,
          logName: '[fake log name]',
          items: [],
          type: TimelineBucketItemType.CHECKLIST,
          isIndependent: false,
        },
      ]);

      it('should return -1', () => {
        expect(getFirstIncompleteChecklistIndex(bucket)).toEqual(-1);
      });
    });

    describe('given an incomplete checklist in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          items: [],
          type: TimelineBucketItemType.CHECKLIST,
          isIndependent: false,
        },
      ]);

      it('should return 0', () => {
        expect(getFirstIncompleteChecklistIndex(bucket)).toEqual(0);
      });
    });

    describe('given multiple incomplete checklists in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id 1]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          items: [],
          type: TimelineBucketItemType.CHECKLIST,
          isIndependent: false,
        },
        {
          id: '[fake id 2]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          items: [],
          type: TimelineBucketItemType.CHECKLIST,
          isIndependent: false,
        },
      ]);

      it('should return 0', () => {
        expect(getFirstIncompleteChecklistIndex(bucket)).toEqual(0);
      });
    });
  });

  describe('#getFirstIncompleteEvent', () => {
    describe('given no events in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([]);

      it('should return undefined', () => {
        expect(getFirstIncompleteEvent(bucket)).toBeUndefined();
      });
    });

    describe('given a complete event in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: true,
          logName: '[fake log name]',
          date: new Date(0),
          type: TimelineBucketItemType.USER_EVENT,
        },
      ]);

      it('should return undefined', () => {
        expect(getFirstIncompleteEvent(bucket)).toBeUndefined();
      });
    });

    describe('given an incomplete event in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          date: addDays(new Date(), 1),
          type: TimelineBucketItemType.USER_EVENT,
        },
      ]);

      it('should return the event', () => {
        expect(getFirstIncompleteEvent(bucket)?.id).toEqual('[fake id]');
      });
    });

    describe('given an incomplete event in the bucket without a date set', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          date: null,
          type: TimelineBucketItemType.USER_EVENT,
        },
      ]);

      it('should return undefined', () => {
        expect(getFirstIncompleteEvent(bucket)).toBeUndefined();
      });
    });
  });

  describe('#areAllEventDatesSet', () => {
    describe('given no events in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([]);

      it('should return true', () => {
        expect(areAllEventDatesSet(bucket)).toBeTruthy();
      });
    });

    describe('given an event with a date set in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          date: new Date(),
          type: TimelineBucketItemType.USER_EVENT,
        },
      ]);

      it('should return true', () => {
        expect(areAllEventDatesSet(bucket)).toBeTruthy();
      });
    });

    describe('given an event without a date set in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          date: null,
          type: TimelineBucketItemType.USER_EVENT,
        },
      ]);

      it('should return false', () => {
        expect(areAllEventDatesSet(bucket)).toBeFalsy();
      });
    });
  });

  describe('#getChecklistTodoForBucket', () => {
    describe('given no events in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([]);

      it('should return null', () => {
        expect(getChecklistTodoForBucket(bucket)).toBeNull();
      });
    });

    describe('given an incomplete checklist having no associated event', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id 1]',
          title: '[fake checklist title]',
          isComplete: false,
          logName: '[fake log name]',
          items: [],
          type: TimelineBucketItemType.CHECKLIST,
          isIndependent: false,
        },
      ]);

      it('should return a todo involving the title of the bucket', () => {
        expect(getChecklistTodoForBucket(bucket)).toMatchObject({
          item: {
            id: '[fake id 1]',
            isComplete: false,
            items: [],
            logName: '[fake log name]',
            title: '[fake checklist title]',
            type: 'checklist',
          },
          title: 'Tips og råd før [fake bucket title]',
          type: 'checklist',
        });
      });
    });

    describe('given an incomplete checklist having an associated event', () => {
      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id 1]',
          title: '[fake checklist title]',
          isComplete: false,
          logName: '[fake log name]',
          items: [],
          type: TimelineBucketItemType.CHECKLIST,
          isIndependent: false,
        },
        {
          id: '[fake id 2]',
          title: '[fake event title]',
          isComplete: false,
          logName: '[fake log name]',
          date: new Date(),
          type: TimelineBucketItemType.USER_EVENT,
        },
      ]);

      it('should return a todo involving the title of the event', () => {
        expect(getChecklistTodoForBucket(bucket)).toMatchObject({
          item: {
            id: '[fake id 1]',
            isComplete: false,
            items: [],
            logName: '[fake log name]',
            title: '[fake checklist title]',
            type: 'checklist',
          },
          title: 'Tips og råd før [fake event title]',
          type: 'checklist',
        });
      });
    });
  });

  describe('#getEventTodoForBucket', () => {
    describe('given no events in the bucket', () => {
      const bucket = timelineBucketFixtureFactory([]);

      it('should return null', () => {
        expect(getEventTodoForBucket(bucket)).toBeNull();
      });
    });

    describe('given an incomplete event in the bucket', () => {
      const tomorrow = addDays(new Date(), 1);

      const bucket = timelineBucketFixtureFactory([
        {
          id: '[fake id]',
          title: '[fake title]',
          isComplete: false,
          logName: '[fake log name]',
          date: tomorrow,
          type: TimelineBucketItemType.USER_EVENT,
        },
      ]);

      it('should return the todo', () => {
        expect(getEventTodoForBucket(bucket)).toMatchObject({
          item: {
            date: tomorrow,
            id: '[fake id]',
            isComplete: false,
            logName: '[fake log name]',
            title: '[fake title]',
            type: 'userEvent',
          },
          title: '[fake title]',
          type: 'event',
        });
      });
    });
  });

  describe('#getSellerPreparationInputDatesTodo', () => {
    describe.each([
      TimelineBucketID.AUDIENCE_ANALYSIS,
      TimelineBucketID.VIEWING,
      TimelineBucketID.MARKETING,
      TimelineBucketID.BIDDING_ROUND,
      TimelineBucketID.FINISHING,
      TimelineBucketID.TAKEOVER,
      TimelineBucketID.MOVING_IN,
    ])('given a bucket other than preparation (%s)', (id) => {
      const bucket = {
        ...timelineBucketFixtureFactory([]),
        id,
      };
      it('should return null', () => {
        expect(getSellerPreparationInputDatesTodo(bucket)).toBeNull();
      });
    });

    describe('given a preparation bucket with missing event dates', () => {
      const bucket = {
        ...timelineBucketFixtureFactory([
          {
            id: '[fake id 1]',
            title: '[fake event title]',
            isComplete: false,
            logName: '[fake log name]',
            date: null,
            type: TimelineBucketItemType.USER_EVENT,
          },
          {
            id: '[fake id 2]',
            title: '[fake event title]',
            isComplete: true,
            logName: '[fake log name]',
            date: addDays(new Date(), -1),
            type: TimelineBucketItemType.USER_EVENT,
          },
        ]),
        id: TimelineBucketID.PREPARATION,
      };

      it('should return the todo', () => {
        expect(getSellerPreparationInputDatesTodo(bucket)).toMatchObject({
          item: null,
          title: 'Sett datoer for interiørveiledning, fotograf og takstmann',
          type: TimelineTodoType.SELLER_PREPARATION_INPUT_DATES,
        });
      });
    });

    describe('given a preparation bucket with all event dates set', () => {
      const bucket = {
        ...timelineBucketFixtureFactory([
          {
            id: '[fake id 1]',
            title: '[fake event title]',
            isComplete: false,
            logName: '[fake log name]',
            date: addDays(new Date(), 1),
            type: TimelineBucketItemType.USER_EVENT,
          },
          {
            id: '[fake id 2]',
            title: '[fake event title]',
            isComplete: true,
            logName: '[fake log name]',
            date: addDays(new Date(), -1),
            type: TimelineBucketItemType.USER_EVENT,
          },
        ]),
        id: TimelineBucketID.PREPARATION,
      };

      it('should return null', () => {
        expect(getSellerPreparationInputDatesTodo(bucket)).toBeNull();
      });
    });
  });
});
