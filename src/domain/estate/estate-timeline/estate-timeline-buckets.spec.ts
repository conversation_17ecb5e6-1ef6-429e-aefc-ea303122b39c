import { addDays } from 'date-fns';
import { EstateVitecStatus } from '../estate';
import { estateChecklistResponseFixtureFactory } from '../../../framework/test/fixtures/misc/estateChecklistResponseFixtureFactory';
import { EstateChecklistID, EstateChecklistType } from '../estate-checklist/estate-checklist';
import { estateEventWithDateFixtureFactory } from '../../../framework/test/fixtures/misc/estateEventFixtureWithDateFactory';
import { EstateEventID, EstateEventType } from '../estate-event/estate-event';
import {
  estateMongooseFactory,
  estateMongooseFactoryWithStatus,
  estateMongooseFactoryWithStatusAndShowings,
  estateMongooseFactoryWithTakeOverDate,
} from '../../../framework/test/helpers/estate-mongoose-factory';
import { estateChecklistTodoFixtureFactory } from '../../../framework/test/fixtures/misc/estateChecklistTodoFixtureFactory';
import {
  buyerMovingInBucketTemplateFactory,
  buyerTakeOverBucketTemplateFactory,
  checklistItemFactory,
  eventItemFactory,
  linkItemFactory,
  sellerAudienceAnalysisBucketTemplateFactory,
  sellerBiddingRoundBucketTemplateFactory,
  sellerFinishingBucketTemplateFactory,
  sellerMarketingBucketTemplateFactory,
  sellerPreparationBucketTemplateFactory,
  sellerViewingBucketTemplateFactory,
  viewingEventItemFactory,
} from './estate-timeline-buckets';

describe('estate timeline buckets', () => {
  describe('#sellerPreparationBucketTemplateFactory', () => {
    const bucketTemplate = sellerPreparationBucketTemplateFactory(
      [estateChecklistResponseFixtureFactory(EstateChecklistID.INFORMATION_GATHERING, EstateChecklistType.SELLER)],
      [estateEventWithDateFixtureFactory(EstateEventID.INTERIOR_GUIDANCE, EstateEventType.USER, new Date(0))],
    );

    describe('isUserCompletable', () => {
      it('should be true', () => {
        expect(bucketTemplate.isUserCompletable).toBeTruthy();
      });
    });

    describe('items', () => {
      it('should contain 6 items', () => {
        expect(bucketTemplate.items).toHaveLength(6);
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe.each([EstateVitecStatus.PREPARATION])('given an estate in status %s', (status) => {
        it('should return true', () => {
          const estate = estateMongooseFactoryWithStatus(status);
          expect(bucketTemplate.isActive(estate)).toBeTruthy();
        });
      });

      describe.each([
        EstateVitecStatus.REQUEST,
        EstateVitecStatus.FOR_SALE,
        EstateVitecStatus.OVERSOLD,
        EstateVitecStatus.RESERVED,
        EstateVitecStatus.ARCHIVED,
        EstateVitecStatus.EXPIRED,
        EstateVitecStatus.TERMINATED,
      ])('given an estate in status %s', (status) => {
        it('should return false', () => {
          const estate = estateMongooseFactoryWithStatus(status);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });
    });
  });

  describe('#sellerAudienceAnalysisBucketTemplateFactory', () => {
    const bucketTemplate = sellerAudienceAnalysisBucketTemplateFactory();

    describe('items', () => {
      it('should contain 0 items', () => {
        expect(bucketTemplate.items).toHaveLength(0);
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe.each([
        EstateVitecStatus.PREPARATION,
        EstateVitecStatus.REQUEST,
        EstateVitecStatus.FOR_SALE,
        EstateVitecStatus.OVERSOLD,
        EstateVitecStatus.RESERVED,
        EstateVitecStatus.ARCHIVED,
        EstateVitecStatus.EXPIRED,
        EstateVitecStatus.TERMINATED,
      ])('given an estate in status %s', (status) => {
        it('should return false', () => {
          const estate = estateMongooseFactoryWithStatus(status);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });
    });
  });

  describe('#sellerMarketingBucketTemplateFactory', () => {
    const bucketTemplate = sellerMarketingBucketTemplateFactory([
      estateEventWithDateFixtureFactory(EstateEventID.AD_LIVE_IN_CORE_CHANNELS, EstateEventType.VITEC, new Date(0)),
    ]);

    describe('items', () => {
      it('should contain 1 item', () => {
        expect(bucketTemplate.items).toHaveLength(1);
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe.each([EstateVitecStatus.FOR_SALE])('given an estate in status %s', (status) => {
        describe('given no visitings', () => {
          it('should return true', () => {
            const estate = estateMongooseFactoryWithStatus(status);
            expect(bucketTemplate.isActive(estate)).toBeTruthy();
          });
        });

        describe('given some visitings', () => {
          describe('given a viewing in the future within 5 days', () => {
            const tomorrow = addDays(new Date(), 1).toISOString();

            it('should return false', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: tomorrow,
                  end: tomorrow,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeFalsy();
            });
          });

          describe('given a viewing in the future after 5 days', () => {
            const sixDaysInTheFuture = addDays(new Date(), 6).toISOString();

            it('should return true', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: sixDaysInTheFuture,
                  end: sixDaysInTheFuture,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeTruthy();
            });
          });

          describe('given a viewing in the past 7 days', () => {
            const sevenDaysAgo = addDays(new Date(), -7).toISOString();

            it('should return false', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: sevenDaysAgo,
                  end: sevenDaysAgo,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeFalsy();
            });
          });
        });
      });

      describe.each([
        EstateVitecStatus.REQUEST,
        EstateVitecStatus.PREPARATION,
        EstateVitecStatus.OVERSOLD,
        EstateVitecStatus.RESERVED,
        EstateVitecStatus.ARCHIVED,
        EstateVitecStatus.EXPIRED,
        EstateVitecStatus.TERMINATED,
      ])('given an estate in status %s', (status) => {
        it('should return false', () => {
          const estate = estateMongooseFactoryWithStatus(status);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });
    });
  });

  describe('#sellerViewingBucketTemplateFactory', () => {
    const bucketTemplate = sellerViewingBucketTemplateFactory([
      estateChecklistResponseFixtureFactory(EstateChecklistID.VIEWING, EstateChecklistType.SELLER),
    ]);

    describe('items', () => {
      it('should contain 1 item', () => {
        expect(bucketTemplate.items).toHaveLength(1);
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe.each([EstateVitecStatus.FOR_SALE])('given an estate in status %s', (status) => {
        describe('given no visitings', () => {
          it('should return false', () => {
            const estate = estateMongooseFactoryWithStatus(status);
            expect(bucketTemplate.isActive(estate)).toBeFalsy();
          });
        });

        describe('given some visitings', () => {
          describe('given a viewing in the past', () => {
            const yesterday = addDays(new Date(), -1).toISOString();

            it('should return false', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: yesterday,
                  end: yesterday,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeFalsy();
            });
          });

          describe('given a viewing today', () => {
            const today = new Date().toString();

            it('should return true', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: today,
                  end: today,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeTruthy();
            });
          });

          describe('given a viewing in the future within 5 days', () => {
            const tomorrow = addDays(new Date(), 1).toISOString();

            it('should return true', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: tomorrow,
                  end: tomorrow,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeTruthy();
            });
          });

          describe('given a viewing in the future after 5 days', () => {
            const afterFiveDays = addDays(new Date(), 6).toISOString();

            it('should return false', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: afterFiveDays,
                  end: afterFiveDays,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeFalsy();
            });
          });
        });
      });

      describe.each([
        EstateVitecStatus.REQUEST,
        EstateVitecStatus.PREPARATION,
        EstateVitecStatus.OVERSOLD,
        EstateVitecStatus.RESERVED,
        EstateVitecStatus.ARCHIVED,
        EstateVitecStatus.EXPIRED,
        EstateVitecStatus.TERMINATED,
      ])('given an estate in status %s', (status) => {
        it('should return false', () => {
          const estate = estateMongooseFactoryWithStatus(status);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });
    });
  });

  describe('#sellerBiddingRoundBucketTemplateFactory', () => {
    const bucketTemplate = sellerBiddingRoundBucketTemplateFactory([
      estateEventWithDateFixtureFactory(EstateEventID.BIDDING_ROUND, EstateEventType.VITEC, new Date(0)),
    ]);

    describe('items', () => {
      it('should contain 1 item', () => {
        expect(bucketTemplate.items).toHaveLength(1);
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe.each([EstateVitecStatus.FOR_SALE])('given an estate in status %s', (status) => {
        describe('given no visitings', () => {
          it('should return false', () => {
            const estate = estateMongooseFactoryWithStatus(status);
            expect(bucketTemplate.isActive(estate)).toBeFalsy();
          });
        });

        describe('given some visitings', () => {
          describe('given a viewing in the past', () => {
            const yesterday = addDays(new Date(), -1).toISOString();

            it('should return true', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: yesterday,
                  end: yesterday,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeTruthy();
            });
          });

          describe('given a viewing in the past 7 days', () => {
            const sevenDaysAgo = addDays(new Date(), -7).toISOString();

            it('should return true', () => {
              const estate = estateMongooseFactoryWithStatusAndShowings(status, [
                {
                  showingId: '[fake showing id]',
                  start: sevenDaysAgo,
                  end: sevenDaysAgo,
                },
              ]);
              expect(bucketTemplate.isActive(estate)).toBeTruthy();
            });
          });
        });
      });

      describe.each([
        EstateVitecStatus.REQUEST,
        EstateVitecStatus.PREPARATION,
        EstateVitecStatus.OVERSOLD,
        EstateVitecStatus.RESERVED,
        EstateVitecStatus.ARCHIVED,
        EstateVitecStatus.EXPIRED,
        EstateVitecStatus.TERMINATED,
      ])('given an estate in status %s', (status) => {
        it('should return false', () => {
          const estate = estateMongooseFactoryWithStatus(status);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });
    });
  });

  describe('#sellerFinishingBucketTemplateFactory', () => {
    const bucketTemplate = sellerFinishingBucketTemplateFactory(
      [estateChecklistResponseFixtureFactory(EstateChecklistID.CONTRACT_SIGNING, EstateChecklistType.SELLER)],
      [estateEventWithDateFixtureFactory(EstateEventID.TAKE_OVER, EstateEventType.VITEC, new Date(0))],
    );

    describe('items', () => {
      it('should contain 4 items', () => {
        expect(bucketTemplate.items).toHaveLength(4);
      });

      describe('given an estate having an otp today', () => {
        it('should contain 5 items', () => {
          const estate = estateMongooseFactoryWithTakeOverDate(new Date());
          expect(sellerFinishingBucketTemplateFactory([], [], estate).items).toHaveLength(5);
        });
      });
    });

    describe('isUserCompletable', () => {
      it('should be true', () => {
        expect(bucketTemplate.isUserCompletable).toBeTruthy();
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe.each([EstateVitecStatus.OVERSOLD, EstateVitecStatus.RESERVED])(
        'given an estate in status %s',
        (status) => {
          it('should return true', () => {
            const estate = estateMongooseFactoryWithStatus(status);
            expect(bucketTemplate.isActive(estate)).toBeTruthy();
          });
        },
      );

      describe.each([
        EstateVitecStatus.REQUEST,
        EstateVitecStatus.PREPARATION,
        EstateVitecStatus.FOR_SALE,
        EstateVitecStatus.ARCHIVED,
        EstateVitecStatus.EXPIRED,
        EstateVitecStatus.TERMINATED,
      ])('given an estate in status %s', (status) => {
        it('should return false', () => {
          const estate = estateMongooseFactoryWithStatus(status);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });
    });
  });

  describe('#buyerTakeOverBucketTemplateFactory', () => {
    const bucketTemplate = buyerTakeOverBucketTemplateFactory(
      [estateChecklistResponseFixtureFactory(EstateChecklistID.HANDOVER, EstateChecklistType.BUYER)],
      [estateEventWithDateFixtureFactory(EstateEventID.TAKE_OVER, EstateEventType.VITEC, new Date(0))],
    );

    describe('items', () => {
      it('should contain 4 items', () => {
        expect(bucketTemplate.items).toHaveLength(4);
      });

      describe('given an estate having an otp today', () => {
        it('should contain 5 items', () => {
          const estate = estateMongooseFactoryWithTakeOverDate(new Date());
          expect(buyerTakeOverBucketTemplateFactory([], [], estate).items).toHaveLength(5);
        });
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe('given an estate', () => {
        it('should return true', () => {
          const estate = estateMongooseFactory();
          expect(bucketTemplate.isActive(estate)).toBeTruthy();
        });
      });
    });
  });

  describe('#buyerMovingInBucketTemplateFactory', () => {
    const bucketTemplate = buyerMovingInBucketTemplateFactory([
      estateChecklistResponseFixtureFactory(EstateChecklistID.CONTRACT_SIGNING, EstateChecklistType.BUYER),
    ]);

    describe('items', () => {
      it('should contain 1 item', () => {
        expect(bucketTemplate.items).toHaveLength(1);
      });
    });

    describe('#isActive', () => {
      describe('given no estate', () => {
        it('should return false', () => {
          expect(bucketTemplate.isActive()).toBeFalsy();
        });
      });

      describe('given an estate without a take over date', () => {
        it('should return false', () => {
          const estate = estateMongooseFactoryWithTakeOverDate((null as unknown) as Date);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });

      describe('given an estate with a take over date in the future', () => {
        const tomorrow = addDays(new Date(), 1);

        it('should return false', () => {
          const estate = estateMongooseFactoryWithTakeOverDate(tomorrow);
          expect(bucketTemplate.isActive(estate)).toBeFalsy();
        });
      });
    });
  });

  describe('#checklistItemFactory', () => {
    describe('given a checklist with missing a checklist id', () => {
      it('should return null', () => {
        expect(checklistItemFactory([], EstateChecklistID.INFORMATION_GATHERING)).toBeNull();
      });
    });

    describe('given a checklist with only incomplete items', () => {
      it('should properly transform them', () => {
        expect(
          checklistItemFactory(
            [
              estateChecklistResponseFixtureFactory(
                EstateChecklistID.INFORMATION_GATHERING,
                EstateChecklistType.SELLER,
                [
                  estateChecklistTodoFixtureFactory(
                    EstateChecklistID.INFORMATION_GATHERING,
                    EstateChecklistType.SELLER,
                    false,
                  ),
                ],
              ),
            ],
            EstateChecklistID.INFORMATION_GATHERING,
          ),
        ).toMatchObject({
          id: 'informationGathering',
          isComplete: false,
          items: [
            {
              description: '[fake description]',
              id: 1234,
              isComplete: false,
              logName: '[fake todo log name]',
              title: '[fake title]',
            },
          ],
          logName: '[fake checklist log name]',
          title: 'informationGathering',
          type: 'checklist',
        });
      });
    });

    describe('given a checklist with only complete items', () => {
      it('should properly transform them', () => {
        expect(
          checklistItemFactory(
            [
              estateChecklistResponseFixtureFactory(
                EstateChecklistID.INFORMATION_GATHERING,
                EstateChecklistType.SELLER,
                [
                  estateChecklistTodoFixtureFactory(
                    EstateChecklistID.INFORMATION_GATHERING,
                    EstateChecklistType.SELLER,
                    true,
                  ),
                ],
              ),
            ],
            EstateChecklistID.INFORMATION_GATHERING,
          ),
        ).toMatchObject({
          id: 'informationGathering',
          isComplete: true,
          items: [
            {
              description: '[fake description]',
              id: 1234,
              isComplete: true,
              logName: '[fake todo log name]',
              title: '[fake title]',
            },
          ],
          logName: '[fake checklist log name]',
          title: 'informationGathering',
          type: 'checklist',
        });
      });
    });

    describe('given a checklist with both complete and incomplete items', () => {
      it('should properly transform them', () => {
        expect(
          checklistItemFactory(
            [
              estateChecklistResponseFixtureFactory(
                EstateChecklistID.INFORMATION_GATHERING,
                EstateChecklistType.SELLER,
                [
                  estateChecklistTodoFixtureFactory(
                    EstateChecklistID.INFORMATION_GATHERING,
                    EstateChecklistType.SELLER,
                    false,
                  ),
                  estateChecklistTodoFixtureFactory(
                    EstateChecklistID.INFORMATION_GATHERING,
                    EstateChecklistType.SELLER,
                    true,
                  ),
                ],
              ),
            ],
            EstateChecklistID.INFORMATION_GATHERING,
          ),
        ).toMatchObject({
          id: 'informationGathering',
          isComplete: false,
          items: [
            {
              description: '[fake description]',
              id: 1234,
              isComplete: false,
              logName: '[fake todo log name]',
              title: '[fake title]',
            },
            {
              description: '[fake description]',
              id: 1234,
              isComplete: true,
              logName: '[fake todo log name]',
              title: '[fake title]',
            },
          ],
          logName: '[fake checklist log name]',
          title: 'informationGathering',
          type: 'checklist',
        });
      });
    });
  });

  describe('#eventItemFactory', () => {
    describe('given an event with no date', () => {
      it('should properly transform them', () => {
        expect(
          eventItemFactory(
            [estateEventWithDateFixtureFactory(EstateEventID.INTERIOR_GUIDANCE, EstateEventType.USER, null)],
            EstateEventID.INTERIOR_GUIDANCE,
          ),
        ).toMatchObject({
          date: null,
          id: 'interiorGuidance',
          isComplete: false,
          logName: '[fake event log name]',
          title: 'interiorGuidance',
          type: 'userEvent',
        });
      });
    });

    describe('given an event with a date in the past', () => {
      it('should properly transform them', () => {
        const yesterday = addDays(new Date(), -1);

        expect(
          eventItemFactory(
            [estateEventWithDateFixtureFactory(EstateEventID.INTERIOR_GUIDANCE, EstateEventType.USER, yesterday)],
            EstateEventID.INTERIOR_GUIDANCE,
          ),
        ).toMatchObject({
          date: yesterday,
          id: 'interiorGuidance',
          isComplete: true,
          logName: '[fake event log name]',
          title: 'interiorGuidance',
          type: 'userEvent',
        });
      });
    });

    describe('given an event with a date in the future', () => {
      it('should properly transform them', () => {
        const tomorrow = addDays(new Date(), 1);

        expect(
          eventItemFactory(
            [estateEventWithDateFixtureFactory(EstateEventID.INTERIOR_GUIDANCE, EstateEventType.USER, tomorrow)],
            EstateEventID.INTERIOR_GUIDANCE,
          ),
        ).toMatchObject({
          date: tomorrow,
          id: 'interiorGuidance',
          isComplete: false,
          logName: '[fake event log name]',
          title: 'interiorGuidance',
          type: 'userEvent',
        });
      });
    });

    describe('given an event without a date and a date override in the past', () => {
      it('should properly transform them', () => {
        const yesterday = addDays(new Date(), -1);

        expect(
          eventItemFactory(
            [estateEventWithDateFixtureFactory(EstateEventID.INTERIOR_GUIDANCE, EstateEventType.USER, null)],
            EstateEventID.INTERIOR_GUIDANCE,
            yesterday,
          ),
        ).toMatchObject({
          date: yesterday,
          id: 'interiorGuidance',
          isComplete: true,
          logName: '[fake event log name]',
          title: 'interiorGuidance',
          type: 'userEvent',
        });
      });
    });

    describe('given an event without a date and a date override in the future', () => {
      it('should properly transform them', () => {
        const tomorrow = addDays(new Date(), 1);

        expect(
          eventItemFactory(
            [estateEventWithDateFixtureFactory(EstateEventID.INTERIOR_GUIDANCE, EstateEventType.USER, null)],
            EstateEventID.INTERIOR_GUIDANCE,
            tomorrow,
          ),
        ).toMatchObject({
          date: tomorrow,
          id: 'interiorGuidance',
          isComplete: false,
          logName: '[fake event log name]',
          title: 'interiorGuidance',
          type: 'userEvent',
        });
      });
    });
  });

  describe('#viewingEventItemFactory', () => {
    describe('given an estate with some viewings', () => {
      const yesterday = addDays(new Date(), -1);
      const tomorrow = addDays(new Date(), 1);

      const estate = estateMongooseFactoryWithStatusAndShowings(EstateVitecStatus.FOR_SALE, [
        {
          showingId: '[fake showing id 1]',
          start: yesterday.toISOString(),
          end: yesterday.toISOString(),
        },
        {
          showingId: '[fake showing id 2]',
          start: tomorrow.toISOString(),
          end: tomorrow.toISOString(),
        },
      ]);

      it('should properly transform them', () => {
        expect(viewingEventItemFactory(estate)).toMatchObject([
          {
            date: yesterday,
            id: 'viewing',
            isComplete: true,
            logName: 'visiting',
            title: 'Visning #1',
            type: 'vitecEvent',
          },
          {
            date: tomorrow,
            id: 'viewing',
            isComplete: false,
            logName: 'visiting',
            title: 'Visning #2',
            type: 'vitecEvent',
          },
        ]);
      });
    });
  });

  describe('#linkItemFactory', () => {
    describe('given some parameters', () => {
      it('should properly transform them', () => {
        const id = '[fake link id]';
        const title = '[fake link title]';
        const url = '[fake link url]';

        expect(linkItemFactory(id, title, url)).toMatchObject({
          id: '[fake link id]',
          isComplete: true,
          logName: '[fake link id]',
          title: '[fake link title]',
          type: 'link',
          url: '[fake link url]',
        });
      });
    });
  });
});
