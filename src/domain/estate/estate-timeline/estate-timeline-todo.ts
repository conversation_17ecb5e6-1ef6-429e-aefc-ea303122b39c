import i18n from '../../../i18n.config';
import type {
  TimelineBucket,
  TimelineBucketChecklistItem,
  TimelineBucketEventItem,
  TimelineBucketItem,
  TimelineTodo,
} from './estate-timeline';
import { TimelineBucketID, TimelineBucketItemType, TimelineTodoType } from './estate-timeline';

export const getFirstIncompleteChecklistIndex = (bucket: TimelineBucket): number => {
  return bucket.items.findIndex((item) => {
    return item && item.type === TimelineBucketItemType.CHECKLIST && !item.isComplete;
  });
};

export const getFirstIncompleteEvent = (bucket: TimelineBucket): TimelineBucketItem | null | undefined => {
  return bucket.items.find((item) => {
    if (
      !item ||
      (item.type !== TimelineBucketItemType.USER_EVENT && item.type !== TimelineBucketItemType.VITEC_EVENT)
    ) {
      return false;
    }

    const eventItem = item as TimelineBucketEventItem;
    return eventItem.date && !eventItem.isComplete;
  });
};

export const areAllEventDatesSet = (bucket: TimelineBucket): boolean => {
  return bucket.items.every((item) => {
    if (
      !item ||
      (item.type !== TimelineBucketItemType.USER_EVENT && item.type !== TimelineBucketItemType.VITEC_EVENT)
    ) {
      return true;
    }

    const eventItem = item as TimelineBucketEventItem;
    return !!eventItem.date;
  });
};

export const getChecklistTodoForBucket = (bucket: TimelineBucket): TimelineTodo | null => {
  const firstIncompleteChecklistIndex = getFirstIncompleteChecklistIndex(bucket);

  if (firstIncompleteChecklistIndex !== -1) {
    const item = bucket.items[firstIncompleteChecklistIndex] as TimelineBucketChecklistItem;

    if (item.isIndependent) {
      return {
        title: item.title,
        type: TimelineTodoType.CHECKLIST,
        item,
      };
    } else {
      const nextItem = bucket.items[firstIncompleteChecklistIndex + 1];

      if (nextItem) {
        return {
          title: `${i18n.__('Finish checklist before')} ${nextItem.title.toLocaleLowerCase('no')}`,
          type: TimelineTodoType.CHECKLIST,
          item,
        };
      } else {
        return {
          title: `${i18n.__('Finish checklist before')} ${bucket.title.toLocaleLowerCase('no')}`,
          type: TimelineTodoType.CHECKLIST,
          item,
        };
      }
    }
  }

  return null;
};

export const getEventTodoForBucket = (bucket: TimelineBucket): TimelineTodo | null => {
  const item = getFirstIncompleteEvent(bucket);

  if (item) {
    return {
      title: item.title,
      type: TimelineTodoType.EVENT,
      item,
    };
  }

  return null;
};

export const getSellerPreparationInputDatesTodo = (bucket: TimelineBucket): TimelineTodo | null => {
  if (bucket.id === TimelineBucketID.PREPARATION && !areAllEventDatesSet(bucket)) {
    return {
      title: i18n.__('Input dates for interior, photo and takstmann'),
      type: TimelineTodoType.SELLER_PREPARATION_INPUT_DATES,
      item: null,
    };
  }

  return null;
};
