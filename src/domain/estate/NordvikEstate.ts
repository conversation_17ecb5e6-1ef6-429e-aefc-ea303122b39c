import { pick } from 'lodash';
import { equals } from 'ramda';
import { cityToTitleCase, extractStreetAddress } from '../../utils/address.utils';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import type { EstateSalesHistory } from '../valuation/EstateSalesHistoryEntry';
import type { EstatePG, EstateSummary, SummarizableEstate } from './estate';
import { estateSummaryFactory } from './estate.factory';

export const getBaseLandIdentificationMatrixFromEVEstate = (
  estate: EstatePG | null,
): LandIdentificationMatrix | null => {
  if (!estate || !estate.landIdentificationMatrix) {
    return null;
  }

  return pick(estate.landIdentificationMatrix, ['bnr', 'fnr', 'gnr', 'knr', 'snr']);
};

export const createNordvikEstate = (
  dto: EstatePG,
  estateSalesHistory: EstateSalesHistory = [],
  estateValuation: number | null,
  valuationChange: { increasePercentage: number | null; increaseValue: number | null },
): SummarizableEstate => {
  const getBaseLandIdentificationMatrix = (): LandIdentificationMatrix | null => {
    return getBaseLandIdentificationMatrixFromEVEstate(dto);
  };

  const toEstateSummary = (): EstateSummary => {
    return estateSummaryFactory({
      id: dto.id,
      vitecID: null,
      pgEstateId: dto.id,
      type: dto.type,
      address: cityToTitleCase(dto.address),
      streetAddress: extractStreetAddress(dto.address),
      propertyType: dto.propertyType,
      area: dto.livingArea,
      numberOfBedrooms: dto.numberOfBedrooms,
      buildYear: dto.buildYear,
      floor: dto.floor,
      sellPreference: dto.sellPreference,
      connectToBroker: dto.connectToBroker,
      ownerSince: estateSalesHistory[0]?.date?.toDateString(),
      estimatedValue: estateValuation,
      orgNumber: dto.OrganizationNumber,
      shareNumber: dto.ShareNumber,
      image: dto.estateImage
        ? {
            small: dto.estateImage,
            medium: dto.estateImage,
            large: dto.estateImage,
          }
        : null,
      valuationChange: valuationChange,
      createdDate: dto.createdAt.toISOString(),
    });
  };

  const matchesLandIdentificationMatrix = (other: SummarizableEstate): boolean => {
    if (!dto.landIdentificationMatrix) {
      return false;
    }
    return equals(other.getBaseLandIdentificationMatrix(), getBaseLandIdentificationMatrix());
  };

  return { matchesLandIdentificationMatrix, getBaseLandIdentificationMatrix, toEstateSummary };
};
