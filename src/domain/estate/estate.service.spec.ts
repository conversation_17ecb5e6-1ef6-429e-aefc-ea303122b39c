import { addDays, addMonths } from 'date-fns';
import { omit } from 'ramda';
import type { Unleash } from 'unleash-client';
import { estatePGFixtureFactory } from '../../framework/test/fixtures/entities/estatePGFixtureFactory';
import { userFixtureFactory } from '../../framework/test/fixtures/entities/userFixtureFactory';
import { areaRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/areaRepositoryFixtureFactory';
import { estateMongooseRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/estateMongooseRepositoryFixtureFactory';
import { estateRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/estateRepositoryFixtureFactory';
import { storebrandAuditRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/storebrandAuditRepositoryFixtureFactory';
import { vitecEstateExtensionRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/vitecEstateExtensionRepositoryFixtureFactory';
import { timelineServiceFixtureFactory } from '../../framework/test/fixtures/services/timelineServiceFixtureFactory';
import { valuationServiceFixtureFactory } from '../../framework/test/fixtures/services/valuationServiceFixtureFactory';
import { wealthManagementServiceFixtureFactory } from '../../framework/test/fixtures/services/wealthManagementServiceFixtureFactory';
import { estateAddressFactory } from '../../framework/test/helpers/estate-address-factory';
import { acceptedEstateBidFactory, unAcceptedEstateBidFactory } from '../../framework/test/helpers/estate-bid-factory';
import {
  estateMongooseFactory,
  estateMongooseFactoryForSaleStatusChange,
  estateMongooseFactoryForSaleWithFinnPublishDate,
  estateMongooseFactoryForSaleWithOneShowing,
  estateMongooseFactoryForSaleWithPrivateAndPublicShowings,
  estateMongooseFactoryForSaleWithShowingAndStatusChange,
  estateMongooseFactoryWithEstateId,
  estateMongooseFactoryWithNoShowingsOrStatusChanges,
  estateMongooseFactoryWithPrice,
  estateMongooseFactoryWithStatus,
} from '../../framework/test/helpers/estate-mongoose-factory';
import { createFakeEstateRepository } from '../../framework/test/helpers/FakeEstateRepository';
import { createFakeEstateValuationService } from '../../framework/test/helpers/FakeEstateValuationService';
import { createFakeMongooseEstateRepository } from '../../framework/test/helpers/FakeRemoteEstateRepository';
import { createFakeEstateTimelineSellerService } from '../../framework/test/helpers/FakeTimelineService';
import { createFakeVitecEstateExtensionRepository } from '../../framework/test/helpers/FakeVitecEstateExtensionRepository';
import type { AreaRepository } from '../area/area.repository';
import type { RangeSelector } from '../user-options/user-options';
import type { User } from '../user/user';
import type { ValuationService } from '../valuation/ValuationService';
import type { EstateSummary } from './estate';
import { EstateType, EstateVitecStatus, UserEstateRelation } from './estate';
import { estateSummaryFactory } from './estate.factory';
import type { EstateRepository } from './estate.repository';
import type { EstateService } from './estate.service';
import {
  aggregateWealthManagementSummaryEntries,
  deduplicateEstates,
  estateServiceFactory,
  getEstateAddressText,
  getEstateStatusText,
  mapOwnershipToString,
  transformToWealthManagementSummaryEntry,
} from './estate.service';
import type { EstateMongooseRepository } from './mongo/estate.mogoose-repository';
import { SortField, SortMode } from './mongo/estate.mogoose-repository';
import type { EstateAddress, EstateMongoose } from './mongo/estate.mongoose-types';
import type { VitecEstateExtensionRepository } from './vitec-estate-extension/vitec-estate-repository';
import type { WealthManagementSummary, WealthManagementSummaryEntry } from './wealth-management/wealth-management';
import type { WealthManagementService } from './wealth-management/wealth-management.service';
import { wealthManagementServiceFactory } from './wealth-management/wealth-management.service';

describe('EstateService implementation', () => {
  describe('#getEstateStatusText', () => {
    describe('when the estate is in "PREPARATION" stage', () => {
      test('returns a text indicating the preparation stage', () => {
        const PREPARATION_STAGE_STATUS_TEXT = 'Som forberedelse til å bli markedsført';

        const result = getEstateStatusText(estateMongooseFactoryWithStatus(EstateVitecStatus.PREPARATION));

        expect(result).toEqual(PREPARATION_STAGE_STATUS_TEXT);
      });
    });

    describe.each([EstateVitecStatus.OVERSOLD, EstateVitecStatus.RESERVED, EstateVitecStatus.ARCHIVED])(
      'when the estate is in "%s" stage',
      (status: EstateVitecStatus) => {
        test('returns a text indicating the "sold" stage', () => {
          const SOLD_STAGE_STATUS_TEXT = 'Selges';

          const result = getEstateStatusText(estateMongooseFactoryWithStatus(status));

          expect(result).toEqual(SOLD_STAGE_STATUS_TEXT);
        });
      },
    );

    describe('when the status is unknown', () => {
      test('returns an empty string', () => {
        const result = getEstateStatusText(estateMongooseFactoryWithStatus(('' as unknown) as EstateVitecStatus));

        expect(result).toEqual('');
      });
    });

    describe('when the estate is for sale', () => {
      describe('when there were no viewings yet and there were no status changes', () => {
        const result = getEstateStatusText(estateMongooseFactoryWithNoShowingsOrStatusChanges());

        expect(result).toEqual('');
      });

      describe('when there was a status change', () => {
        test.each([
          [addDays(new Date(1), 1), 'Dag 1 av markedsføringsfasen'],
          [addDays(new Date(1), 2), 'Dag 2 av markedsføringsfasen'],
        ])('uses the date of the change for the status text', (now: Date, expected: string) => {
          const result = getEstateStatusText(estateMongooseFactoryForSaleStatusChange(), now.getTime());

          expect(result).toEqual(expected);
        });
      });

      describe('when there were past viewings but no status changes', () => {
        test('uses the "finnPublishDate" for the status text', () => {
          const now = addDays(new Date(1), 1);
          const result = getEstateStatusText(estateMongooseFactoryForSaleWithFinnPublishDate(), now.getTime());

          expect(result).toEqual('Dag 1 av markedsføringsfasen');
        });
      });

      describe('when the first viewing date is in the future and there was a status change', () => {
        test('uses the status change date for the status text', () => {
          const SECOND_DAY_EVER = 86400000;
          const result = getEstateStatusText(estateMongooseFactoryForSaleWithShowingAndStatusChange(), SECOND_DAY_EVER);

          expect(result).toEqual('Dag 1 av markedsføringsfasen');
        });
      });

      describe('when the first viewing date is in the past but the last one is in the future', () => {
        test('uses the sum of the private and public showings for the status text', () => {
          const SECOND_DAY_EVER = 86400000;
          const now = new Date(SECOND_DAY_EVER);
          const result = getEstateStatusText(estateMongooseFactoryForSaleWithPrivateAndPublicShowings(), now.getTime());

          expect(result).toEqual('2 flere visninger planlagt');
        });
      });

      describe('when the last viewing date has passed', () => {
        test('uses the "marketing" status text', () => {
          const MARKETING_STATUS_TEXT = 'Markedsføring';
          const now = addDays(new Date(0), 8);
          const result = getEstateStatusText(estateMongooseFactoryForSaleWithOneShowing(), now.getTime());

          expect(result).toEqual(MARKETING_STATUS_TEXT);
        });

        describe('when the last viewing date has been less than a week ago', () => {
          test('uses the "betting round" status text', () => {
            const BETTING_ROUND_STATUS_TEXT = 'Budrunde';
            const now = addDays(new Date(0), 6);
            const result = getEstateStatusText(estateMongooseFactoryForSaleWithOneShowing(), now.getTime());

            expect(result).toEqual(BETTING_ROUND_STATUS_TEXT);
          });
        });
      });
    });
  });

  describe('#getEstatePrice', () => {
    let subject: EstateService;

    beforeEach(() => {
      subject = estateServiceFactory({
        config: {
          awsS3Config: {
            accessKeyID: '',
            secret: '',
            bucketName: '',
          },
          mapboxConfig: {
            accessToken: '',
            zoom: 10,
            userName: '',
            baseUrl: '',
            styleID: '',
            minRelevance: 1,
          },
        },
        estateRepository: createFakeEstateRepository({}),
        valuationService: createFakeEstateValuationService({}),
        estateMongooseRepository: createFakeMongooseEstateRepository({}),
        areaRepository: areaRepositoryFixtureFactory(),
        vitecEstateExtensionRepository: {
          getEstateExtensionById: jest.fn(),
          modifyEstateExtensionById: jest.fn(),
          getEstateExtensions: jest.fn(),
        },
        mapboxService: { getGeoCodeFromAddress: jest.fn(), getImagefromGeoCode: jest.fn() },
        awsS3Service: { uploadFile: jest.fn(), deleteFile: jest.fn(), getFile: jest.fn() },
        wealthManagementService: wealthManagementServiceFixtureFactory(),
        timelineService: timelineServiceFixtureFactory(),
        storebrandAuditRepository: storebrandAuditRepositoryFixtureFactory(),
        unleash: (jest.fn() as unknown) as Unleash,
      });
    });

    describe.each([EstateVitecStatus.REQUEST, EstateVitecStatus.PREPARATION])(
      'when the estate is in "%s" state',
      (status: EstateVitecStatus) => {
        describe('when the last bid was accepted', () => {
          test('labels the price as the accepted bid', () => {
            const price = subject.getEstatePrice(estateMongooseFactoryWithPrice(status), [
              unAcceptedEstateBidFactory(),
              acceptedEstateBidFactory(),
            ]);

            expect(price).toMatchObject({ lastBidValue: 111, value: 1, label: 'Last accepted bid' });
          });

          describe('when there were no bids', () => {
            test('uses 0 for the last bid value and a different label', () => {
              const price = subject.getEstatePrice(estateMongooseFactoryWithPrice(status), []);

              expect(price).toMatchObject({ lastBidValue: 0, value: 1, label: 'Asking price' });
            });
          });

          describe('when there are bids but no accepted ones', () => {
            test('uses the last bid value', () => {
              const price = subject.getEstatePrice(estateMongooseFactoryWithPrice(status), [
                unAcceptedEstateBidFactory(),
              ]);

              expect(price).toMatchObject({ lastBidValue: 123, value: 1, label: 'Asking price' });
            });
          });
        });
      },
    );
  });

  describe('#getUserEstates', () => {
    const mockEstateService = ({
      estateRepository,
      estateMongooseRepository = createFakeMongooseEstateRepository({}),
      vitecEstateExtensionRepository = {
        getEstateExtensionById: jest.fn(),
        modifyEstateExtensionById: jest.fn(),
        getEstateExtensions: jest.fn(),
      },
    }: {
      estateRepository: EstateRepository;
      estateMongooseRepository?: EstateMongooseRepository;
      vitecEstateExtensionRepository?: VitecEstateExtensionRepository;
    }) =>
      estateServiceFactory({
        config: {
          awsS3Config: {
            accessKeyID: '',
            secret: '',
            bucketName: '',
          },
          mapboxConfig: {
            accessToken: '',
            zoom: 10,
            userName: '',
            baseUrl: '',
            styleID: '',
            minRelevance: 1,
          },
        },
        estateRepository,
        valuationService: createFakeEstateValuationService({}),
        estateMongooseRepository,
        areaRepository: areaRepositoryFixtureFactory(),
        vitecEstateExtensionRepository: vitecEstateExtensionRepository,
        mapboxService: { getGeoCodeFromAddress: jest.fn(), getImagefromGeoCode: jest.fn() },
        awsS3Service: { uploadFile: jest.fn(), deleteFile: jest.fn(), getFile: jest.fn() },
        wealthManagementService: wealthManagementServiceFixtureFactory(),
        timelineService: createFakeEstateTimelineSellerService(),
        storebrandAuditRepository: storebrandAuditRepositoryFixtureFactory(),
        unleash: ({ isEnabled: jest.fn() } as unknown) as Unleash,
      });

    describe('test Dashboard', () => {
      const mockGetUserEstatesCall: (
        expectedUser: User,
        evStatus: EstateVitecStatus,
      ) => Promise<EstateSummary[]> = async (expectedUser: User, evStatus: EstateVitecStatus) => {
        const expectedEstatePG = estatePGFixtureFactory(expectedUser);
        const expectedMongooseEstate = { ...estateMongooseFactoryWithStatus(evStatus), broker: null };
        const estateService = mockEstateService({
          estateRepository: createFakeEstateRepository({
            getByUserID: [expectedEstatePG],
          }),
          estateMongooseRepository: createFakeMongooseEstateRepository({
            findEstatesBySellerPhone: [],
            getBuyingEstates: [expectedMongooseEstate],
          }),
          vitecEstateExtensionRepository: createFakeVitecEstateExtensionRepository(expectedEstatePG),
        });
        return estateService.getUserEstates({
          userID: expectedUser.id,
          phone: expectedUser.phoneNumber,
          email: expectedUser.email,
        });
      };

      it('EV estate + VitecStatus 3 (buyer) -> expected EV estate', async () => {
        const result = await mockGetUserEstatesCall(userFixtureFactory(), EstateVitecStatus.OVERSOLD);
        expect(result.length).toBe(1);
        expect(result[0].pgEstateId).toBe('[fake id]');
        expect(result[0].type).toBe(EstateType.FOR_SALE);
      });
    });
  });

  describe('#mapOwnershipToString', () => {
    describe.each([
      [0, 'Eiet'],
      [1, 'Andel'],
      [2, 'Aksje'],
      [3, 'Obligasjon'],
      [4, 'Eierseksjon'],
    ])('when the ownership indicator number is %d', (ownershipIndicator: number, expectedLabel: string) => {
      test(`the label is ${expectedLabel}`, () => {
        expect(mapOwnershipToString(ownershipIndicator)).toEqual(expectedLabel);
      });
    });

    describe('when the ownership indicator is unknown', () => {
      test('returns an empty string', () => {
        expect(mapOwnershipToString(123456)).toEqual('');
      });
    });
  });

  describe('#getEstateAddressText', () => {
    test('joins the address descriptor into a string', () => {
      expect(getEstateAddressText(estateAddressFactory())).toEqual(`Fake Street 123 1234 Fake City`);
    });

    describe('when any of the properties is falsy', () => {
      test.each([
        omit(['streetAdress'], estateAddressFactory()),
        omit(['zipCode'], estateAddressFactory()),
        omit(['city'], estateAddressFactory()),
      ])('does not use the falsy value for the address text', (address: Partial<EstateAddress>) => {
        expect(getEstateAddressText((address as unknown) as EstateAddress)).not.toContain('undefined');
      });
    });
  });

  describe('#getPremarketEstates', () => {
    let estateRepository: EstateRepository;
    let estateMongooseRepository: EstateMongooseRepository;
    let valuationService: ValuationService;
    let areaRepository: AreaRepository;
    let vitecEstateExtensionRepository: VitecEstateExtensionRepository;
    let estateService: EstateService;

    beforeEach(() => {
      estateRepository = estateRepositoryFixtureFactory();
      estateMongooseRepository = estateMongooseRepositoryFixtureFactory();
      estateMongooseRepository.findEstatesByPredicate = jest.fn().mockReturnValueOnce([]);
      valuationService = valuationServiceFixtureFactory();
      areaRepository = areaRepositoryFixtureFactory();
      vitecEstateExtensionRepository = vitecEstateExtensionRepositoryFixtureFactory();
      estateService = estateServiceFactory({
        config: {
          awsS3Config: {
            accessKeyID: '',
            secret: '',
            bucketName: '',
          },
          mapboxConfig: {
            accessToken: '',
            zoom: 10,
            userName: '',
            baseUrl: '',
            styleID: '',
            minRelevance: 1,
          },
        },
        estateRepository,
        estateMongooseRepository,
        valuationService,
        areaRepository,
        vitecEstateExtensionRepository,
        awsS3Service: { uploadFile: jest.fn(), deleteFile: jest.fn(), getFile: jest.fn() },
        mapboxService: { getGeoCodeFromAddress: jest.fn(), getImagefromGeoCode: jest.fn() },
        wealthManagementService: wealthManagementServiceFixtureFactory(),
        timelineService: timelineServiceFixtureFactory(),
        storebrandAuditRepository: storebrandAuditRepositoryFixtureFactory(),
        unleash: (jest.fn() as unknown) as Unleash,
      });
    });

    describe('conditions', () => {
      describe('given a simple invocation', () => {
        beforeEach(async () => {
          await estateService.getPremarketEstates({
            priceRange: null,
            livingAreaRange: null,
            numberOfBedrooms: null,
            postalCodes: [],
            limit: 50,
            statuses: [1, 2, 3],
            skip: 0,
            sortBy: [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
            propertyTypes: [],
          });
        });
      });

      describe('given a postal code', () => {
        const expectedPostalCodes = ['[fake postal code]'];

        beforeEach(async () => {
          await estateService.getPremarketEstates({
            priceRange: null,
            livingAreaRange: null,
            numberOfBedrooms: null,
            postalCodes: expectedPostalCodes,
            limit: 50,
            statuses: [1, 2, 3],
            skip: 0,
            sortBy: [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
            propertyTypes: [],
          });
        });

        it('should add the proper MongoDB condition to the query', () => {
          expect(estateMongooseRepository.findEstatesByPredicate).toHaveBeenCalledWith(
            expect.anything(),
            expect.objectContaining({
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              $and: expect.arrayContaining([
                expect.anything(),
                expect.objectContaining({
                  // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unsafe-assignment
                  $and: expect.arrayContaining([
                    expect.objectContaining({
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      'address.zipCode': { $in: ['[fake postal code]'] },
                    }),
                  ]),
                }),
              ]),
            }),
            50,
            0,
            [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
          );
        });
      });

      describe('given a price range', () => {
        const expectedPriceRange: RangeSelector = {
          from: 0,
          to: 100,
        };

        beforeEach(async () => {
          await estateService.getPremarketEstates({
            priceRange: expectedPriceRange,
            livingAreaRange: null,
            numberOfBedrooms: null,
            postalCodes: [],
            limit: 50,
            statuses: [1, 2, 3],
            skip: 0,
            sortBy: [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
            propertyTypes: [],
          });
        });

        it('should add the proper MongoDB condition to the query', () => {
          expect(estateMongooseRepository.findEstatesByPredicate).toHaveBeenCalledWith(
            expect.anything(),
            expect.objectContaining({
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              $and: expect.arrayContaining([
                expect.anything(),
                expect.objectContaining({
                  // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unsafe-assignment
                  $and: expect.arrayContaining([
                    expect.objectContaining({
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      'estatePrice.priceSuggestion': { $gte: 0, $lte: 100 },
                    }),
                  ]),
                }),
              ]),
            }),
            50,
            0,
            [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
          );
        });
      });

      describe('given a living area range', () => {
        const expectedLivingAreaRange: RangeSelector = {
          from: 40,
          to: 60,
        };

        beforeEach(async () => {
          await estateService.getPremarketEstates({
            priceRange: null,
            livingAreaRange: expectedLivingAreaRange,
            numberOfBedrooms: null,
            postalCodes: [],
            limit: 50,
            statuses: [1, 2, 3],
            skip: 0,
            sortBy: [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
            propertyTypes: [],
          });
        });

        it('should add the proper MongoDB condition to the query', () => {
          expect(estateMongooseRepository.findEstatesByPredicate).toHaveBeenCalledWith(
            expect.anything(),
            expect.objectContaining({
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              $and: expect.arrayContaining([
                expect.anything(),
                expect.objectContaining({
                  // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unsafe-assignment
                  $and: expect.arrayContaining([
                    expect.objectContaining({
                      // eslint-disable-next-line @typescript-eslint/naming-convention
                      $or: [{ 'estateSize.primaryRoomArea': { $gte: 40, $lte: 60 } }, { estateBaseType: { $eq: 5 } }],
                    }),
                  ]),
                }),
              ]),
            }),
            50,
            0,
            [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
          );
        });
      });
    });

    describe('given a number of bedrooms', () => {
      const expectedNumberOfBedrooms: RangeSelector = {
        from: 2,
        to: 4,
      };

      beforeEach(async () => {
        await estateService.getPremarketEstates({
          priceRange: null,
          livingAreaRange: null,
          numberOfBedrooms: expectedNumberOfBedrooms,
          postalCodes: [],
          limit: 50,
          statuses: [1, 2, 3],
          skip: 0,
          sortBy: [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
          propertyTypes: [],
        });
      });

      it('should add the proper MongoDB condition to the query', () => {
        expect(estateMongooseRepository.findEstatesByPredicate).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            $and: expect.arrayContaining([
              expect.anything(),
              expect.objectContaining({
                // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unsafe-assignment
                $and: expect.arrayContaining([
                  expect.objectContaining({
                    noOfBedRooms: { $gte: 2 },
                  }),
                ]),
              }),
            ]),
          }),
          50,
          0,
          [{ sortField: SortField.DATE, sortMode: SortMode.ASCENDING }],
        );
      });
    });
  });

  describe('#getVitecEstateValuation', () => {
    let estateRepository: EstateRepository;
    let estateMongooseRepository: EstateMongooseRepository;
    let valuationService: ValuationService;
    let areaRepository: AreaRepository;
    let vitecEstateExtensionRepository: VitecEstateExtensionRepository;
    let wealthManagementService: WealthManagementService;
    let estateService: EstateService;

    beforeEach(() => {
      estateRepository = estateRepositoryFixtureFactory();
      estateMongooseRepository = estateMongooseRepositoryFixtureFactory();
      estateMongooseRepository.findEstatesByPredicate = jest.fn().mockReturnValueOnce([]);
      valuationService = valuationServiceFixtureFactory();
      areaRepository = areaRepositoryFixtureFactory();
      vitecEstateExtensionRepository = vitecEstateExtensionRepositoryFixtureFactory();
      wealthManagementService = wealthManagementServiceFactory();
      estateService = estateServiceFactory({
        config: {
          awsS3Config: {
            accessKeyID: '',
            secret: '',
            bucketName: '',
          },
          mapboxConfig: {
            accessToken: '',
            zoom: 10,
            userName: '',
            baseUrl: '',
            styleID: '',
            minRelevance: 1,
          },
        },
        estateRepository,
        estateMongooseRepository,
        valuationService,
        areaRepository,
        vitecEstateExtensionRepository,
        awsS3Service: { uploadFile: jest.fn(), deleteFile: jest.fn(), getFile: jest.fn() },
        mapboxService: { getGeoCodeFromAddress: jest.fn(), getImagefromGeoCode: jest.fn() },
        wealthManagementService,
        timelineService: timelineServiceFixtureFactory(),
        storebrandAuditRepository: storebrandAuditRepositoryFixtureFactory(),
        unleash: (jest.fn() as unknown) as Unleash,
      });
    });

    describe('given an estate without broker valuation', () => {
      let estate: EstateMongoose;

      beforeEach(() => {
        valuationService.getEstateEvaluation = jest.fn().mockResolvedValueOnce({
          valuation: 17850000,
          valuationIndex: null,
        });

        const baseEstate = estateMongooseFactory();

        estate = ({
          ...baseEstate,
          estatePrice: {
            ...baseEstate.estatePrice,
            priceSuggestion: null,
            estimatedValue: null,
            changedDate: null,
          },
        } as unknown) as EstateMongoose;
      });

      it('should return the valuation from Eiendomsverdi', async () => {
        expect(await estateService.getVitecEstateValuation(estate)).toMatchObject({
          isBrokerValuation: false,
          valuation: 17850000,
        });
      });
    });

    describe('given an estate without a broker valuation within the past three months', () => {
      let estate: EstateMongoose;

      beforeEach(() => {
        valuationService.getEstateEvaluation = jest.fn().mockResolvedValueOnce({
          valuation: 17850000,
          valuationIndex: null,
        });

        const baseEstate = estateMongooseFactory();

        estate = {
          ...baseEstate,
          estatePrice: {
            ...baseEstate.estatePrice,
            priceSuggestion: 1234,
            estimatedValue: 5678,
            changedDate: addDays(addMonths(new Date(), -7), -1),
          },
        };
      });

      it('should return the valuation from Eiendomsverdi', async () => {
        expect(await estateService.getVitecEstateValuation(estate)).toMatchObject({
          isBrokerValuation: false,
          valuation: 17850000,
        });
      });
    });

    describe('given an estate with a broker valuation within the past month', () => {
      let estate: EstateMongoose;

      beforeEach(() => {
        valuationService.getEstateEvaluation = jest.fn().mockResolvedValueOnce({
          valuation: 17850000,
          valuationIndex: null,
        });

        const baseEstate = estateMongooseFactory();

        estate = {
          ...baseEstate,
          estatePrice: {
            ...baseEstate.estatePrice,
            priceSuggestion: 1234,
            estimatedValue: 5678,
            changedDate: addDays(addMonths(new Date(), -1), 1),
          },
        };
      });

      it('should return the valuation from Vitec', async () => {
        expect(await estateService.getVitecEstateValuation(estate)).toMatchObject({
          isBrokerValuation: true,
          valuation: 1234,
        });
      });
    });
  });

  describe('#getEiendomsverdiEstateWealthManagementSummaryEntry', () => {
    let estateRepository: EstateRepository;
    let estateMongooseRepository: EstateMongooseRepository;
    let valuationService: ValuationService;
    let areaRepository: AreaRepository;
    let vitecEstateExtensionRepository: VitecEstateExtensionRepository;
    let wealthManagementService: WealthManagementService;
    let estateService: EstateService;

    beforeEach(() => {
      estateRepository = estateRepositoryFixtureFactory();
      estateMongooseRepository = estateMongooseRepositoryFixtureFactory();
      estateMongooseRepository.findEstatesByPredicate = jest.fn().mockReturnValueOnce([]);
      valuationService = valuationServiceFixtureFactory();
      areaRepository = areaRepositoryFixtureFactory();
      vitecEstateExtensionRepository = vitecEstateExtensionRepositoryFixtureFactory();
      wealthManagementService = wealthManagementServiceFactory();
      estateService = estateServiceFactory({
        config: {
          awsS3Config: {
            accessKeyID: '',
            secret: '',
            bucketName: '',
          },
          mapboxConfig: {
            accessToken: '',
            zoom: 10,
            userName: '',
            baseUrl: '',
            styleID: '',
            minRelevance: 1,
          },
        },
        estateRepository,
        estateMongooseRepository,
        valuationService,
        areaRepository,
        vitecEstateExtensionRepository,
        awsS3Service: { uploadFile: jest.fn(), deleteFile: jest.fn(), getFile: jest.fn() },
        mapboxService: { getGeoCodeFromAddress: jest.fn(), getImagefromGeoCode: jest.fn() },
        wealthManagementService,
        timelineService: timelineServiceFixtureFactory(),
        storebrandAuditRepository: storebrandAuditRepositoryFixtureFactory(),
        unleash: (jest.fn() as unknown) as Unleash,
      });
    });

    describe('given no estates', () => {
      const user = userFixtureFactory();
      let summary: WealthManagementSummary;

      beforeEach(async () => {
        summary = await estateService.getWealthManagementSummary(user, []);
      });

      it('should return an empty estates array', async () => {
        expect(summary.estates).toHaveLength(0);
      });

      it('should return a null aggregation', async () => {
        expect(summary.aggregation).toBeNull();
      });
    });

    describe('given an Eiendomsverdi estate', () => {
      const user = {
        ...userFixtureFactory(),
        annualIncome: 1000000,
        sumOfOtherLoans: 200000,
      };
      const estateSummary = estateSummaryFactory({
        id: '[fake id]',
        type: EstateType.OWNED,
        streetAddress: 'Test street 12',
        createdDate: new Date().toISOString(),
      });
      let summary: WealthManagementSummary;

      beforeEach(async () => {
        estateRepository.getByID = jest.fn().mockResolvedValueOnce({
          ...estatePGFixtureFactory(user),
          interestRate: 0.0125,
          originalMortgage: 10000000,
          mortgageYears: 20,
        });
        valuationService.getEstateEvaluation = jest.fn().mockResolvedValueOnce({
          valuation: 17850000,
          valuationIndex: null,
        });
        valuationService.getSaleHistory = jest.fn().mockResolvedValueOnce([
          {
            date: addMonths(new Date(), -1),
            value: 15750000,
          },
        ]);
        summary = await estateService.getWealthManagementSummary(user, [estateSummary]);
      });

      it('should return a wealth management summary entry for 1 estate', async () => {
        expect(summary.estates).toHaveLength(1);
      });

      it('should set the estate id', () => {
        expect(summary.estates[0].estateId).toEqual(estateSummary.id);
      });

      it('should set the estate street address', () => {
        expect(summary.estates[0].streetAddress).toEqual(estateSummary.streetAddress);
      });

      it('should set the estate totalEstimatedValue', () => {
        expect(summary.estates[0].totalEstimatedValue).toEqual(17850000);
      });

      it('should set the estate areFinancialDetailsFilled', () => {
        expect(summary.estates[0].areFinancialDetailsFilled).toEqual(true);
      });

      it('should set the estate hasRecentBrokerValuation', () => {
        expect(summary.estates[0].hasRecentBrokerValuation).toEqual(false);
      });

      it('should set the estate mortgage', () => {
        expect(summary.estates[0].mortgage.value).toEqual(9963000);
        expect(summary.estates[0].mortgage.percentage).toBeCloseTo(0.56, 2);
      });

      it('should set the estate equity', () => {
        expect(summary.estates[0].equity.value).toEqual(7887000);
        expect(summary.estates[0].equity.percentage).toBeCloseTo(0.44, 2);
      });

      it('should set the estate equity and mortgage such that it equals to the total estimated value', () => {
        expect((summary.estates[0].equity.value || 0) + (summary.estates[0].mortgage.value || 0)).toEqual(
          summary.estates[0].totalEstimatedValue,
        );
      });

      it('should set the estate value', () => {
        expect(summary.estates[0].value.initial).toEqual(15750000);
        expect(summary.estates[0].value.current).toEqual(17850000);
        expect(summary.estates[0].value.increaseValue).toEqual(2100000);
        expect(summary.estates[0].value.increasePercentage).toBeCloseTo(0.13, 2);
      });

      it('should set the estate storebrandSaving', () => {
        expect(summary.estates[0].storebrandSaving).toEqual(-34000);
      });

      it('should set the estate extendedMortgage', () => {
        expect(summary.estates[0].extendedMortgage).toEqual(0);
      });

      it('should set the estate moreExpensiveProperty', () => {
        expect(summary.estates[0].moreExpensiveProperty).toEqual(17850000);
      });

      it('should set the estate affordMoreSpace', () => {
        expect(summary.estates[0].affordMoreSpace).toEqual(0);
      });
    });
  });

  describe('#getVitecEstateWealthManagementSummaryEntry', () => {
    let estateRepository: EstateRepository;
    let estateMongooseRepository: EstateMongooseRepository;
    let valuationService: ValuationService;
    let areaRepository: AreaRepository;
    let vitecEstateExtensionRepository: VitecEstateExtensionRepository;
    let wealthManagementService: WealthManagementService;
    let estateService: EstateService;

    beforeEach(() => {
      estateRepository = estateRepositoryFixtureFactory();
      estateMongooseRepository = estateMongooseRepositoryFixtureFactory();
      estateMongooseRepository.findEstatesByPredicate = jest.fn().mockReturnValueOnce([]);
      valuationService = valuationServiceFixtureFactory();
      areaRepository = areaRepositoryFixtureFactory();
      vitecEstateExtensionRepository = vitecEstateExtensionRepositoryFixtureFactory();
      wealthManagementService = wealthManagementServiceFactory();
      estateService = estateServiceFactory({
        config: {
          awsS3Config: {
            accessKeyID: '',
            secret: '',
            bucketName: '',
          },
          mapboxConfig: {
            accessToken: '',
            zoom: 10,
            userName: '',
            baseUrl: '',
            styleID: '',
            minRelevance: 1,
          },
        },
        estateRepository,
        estateMongooseRepository,
        valuationService,
        areaRepository,
        vitecEstateExtensionRepository,
        awsS3Service: { uploadFile: jest.fn(), deleteFile: jest.fn(), getFile: jest.fn() },
        mapboxService: { getGeoCodeFromAddress: jest.fn(), getImagefromGeoCode: jest.fn() },
        wealthManagementService,
        timelineService: timelineServiceFixtureFactory(),
        storebrandAuditRepository: storebrandAuditRepositoryFixtureFactory(),
        unleash: (jest.fn() as unknown) as Unleash,
      });
    });

    describe('given no estates', () => {
      const user = userFixtureFactory();
      let summary: WealthManagementSummary;

      beforeEach(async () => {
        summary = await estateService.getWealthManagementSummary(user, []);
      });

      it('should return an empty estates array', async () => {
        expect(summary.estates).toHaveLength(0);
      });

      it('should return a null aggregation', async () => {
        expect(summary.aggregation).toBeNull();
      });
    });

    describe('given a Vitec estate', () => {
      const user = {
        ...userFixtureFactory(),
        annualIncome: 1000000,
        sumOfOtherLoans: 200000,
      };
      const estateSummary = estateSummaryFactory({
        id: '[fake id]',
        type: EstateType.FOR_SALE,
        streetAddress: 'Test street 25',
        createdDate: new Date().toISOString(),
      });
      let summary: WealthManagementSummary;

      beforeEach(async () => {
        estateMongooseRepository.findEstateByVitecEstateId = jest
          .fn()
          .mockResolvedValueOnce(estateMongooseFactoryWithEstateId('[fake id]'));
        vitecEstateExtensionRepository.getEstateExtensionById = jest.fn().mockResolvedValueOnce({
          vitecID: '[fake id]',
          loanAmount: null,
          interestRate: 0.0125,
          originalMortgage: 10000000,
          mortgageYears: 20,
        });
        valuationService.getEstateEvaluation = jest.fn().mockResolvedValueOnce({
          valuation: 17850000,
          valuationIndex: null,
        });
        valuationService.getSaleHistory = jest.fn().mockResolvedValueOnce([
          {
            date: addMonths(new Date(), -1),
            value: 15750000,
          },
        ]);
        summary = await estateService.getWealthManagementSummary(user, [estateSummary]);
      });

      it('should return a wealth management summary entry for 1 estate', async () => {
        expect(summary.estates).toHaveLength(1);
      });

      it('should set the estate id', () => {
        expect(summary.estates[0].estateId).toEqual(estateSummary.id);
      });

      it('should set the estate street address', () => {
        expect(summary.estates[0].streetAddress).toEqual(estateSummary.streetAddress);
      });

      it('should set the estate totalEstimatedValue', () => {
        expect(summary.estates[0].totalEstimatedValue).toEqual(17850000);
      });

      it('should set the estate areFinancialDetailsFilled', () => {
        expect(summary.estates[0].areFinancialDetailsFilled).toEqual(true);
      });

      it('should set the estate hasRecentBrokerValuation', () => {
        expect(summary.estates[0].hasRecentBrokerValuation).toEqual(false);
      });

      it('should set the estate mortgage', () => {
        expect(summary.estates[0].mortgage.value).toEqual(9963000);
        expect(summary.estates[0].mortgage.percentage).toBeCloseTo(0.56, 2);
      });

      it('should set the estate equity', () => {
        expect(summary.estates[0].equity.value).toEqual(7887000);
        expect(summary.estates[0].equity.percentage).toBeCloseTo(0.44, 2);
      });

      it('should set the estate equity and mortgage such that it equals to the total estimated value', () => {
        expect((summary.estates[0].equity.value || 0) + (summary.estates[0].mortgage.value || 0)).toEqual(
          summary.estates[0].totalEstimatedValue,
        );
      });

      it('should set the estate value', () => {
        expect(summary.estates[0].value.initial).toEqual(15750000);
        expect(summary.estates[0].value.current).toEqual(17850000);
        expect(summary.estates[0].value.increaseValue).toEqual(2100000);
        expect(summary.estates[0].value.increasePercentage).toBeCloseTo(0.13, 2);
      });

      it('should set the estate storebrandSaving', () => {
        expect(summary.estates[0].storebrandSaving).toEqual(-34000);
      });

      it('should set the estate extendedMortgage', () => {
        expect(summary.estates[0].extendedMortgage).toEqual(0);
      });

      it('should set the estate moreExpensiveProperty', () => {
        expect(summary.estates[0].moreExpensiveProperty).toEqual(17850000);
      });

      it('should set the estate affordMoreSpace', () => {
        expect(summary.estates[0].affordMoreSpace).toEqual(0);
      });
    });
  });

  describe('#transformToWealthManagementSummaryEntry', () => {
    describe('given null input values', () => {
      let entry: WealthManagementSummaryEntry;

      beforeEach(() => {
        entry = transformToWealthManagementSummaryEntry(
          '[fake id]',
          '[fake address]',
          null,
          false,
          {
            valueIncrease: {
              increasePercentage: null,
              increaseValue: null,
            },
            savingsBasedOnInterestRateDifference: null,
            moreExpensiveProperty: null,
            extendedMortgage: null,
            equityGrowth: {
              increasePercentage: null,
              increaseValue: null,
            },
            affordMoreSpace: null,
            currentMortgage: null,
            purchasePrice: null,
            valuation: null,
            valuationComponents: null,
          },
          null,
          null,
          null,
          null,
          null,
        );
      });

      it('should set totalEstimatedValue to null', () => {
        expect(entry.totalEstimatedValue).toBeNull();
      });

      it('should set areFinancialDetailsFilled to false', () => {
        expect(entry.areFinancialDetailsFilled).toBeFalsy();
      });

      it('should set hasRecentBrokerValuation to false', () => {
        expect(entry.hasRecentBrokerValuation).toBeFalsy();
      });

      it('should set mortgage properties to null', () => {
        expect(entry.mortgage.value).toBeNull();
        expect(entry.mortgage.percentage).toBeNull();
      });

      it('should set equity properties to null', () => {
        expect(entry.equity.value).toBeNull();
        expect(entry.equity.percentage).toBeNull();
      });

      it('should set value properties to null', () => {
        expect(entry.value.initial).toBeNull();
        expect(entry.value.current).toBeNull();
        expect(entry.value.increaseValue).toBeNull();
        expect(entry.value.increasePercentage).toBeNull();
      });

      it('should set storebrandSaving to null', () => {
        expect(entry.storebrandSaving).toBeNull();
      });

      it('should set extendedMortgage to null', () => {
        expect(entry.extendedMortgage).toBeNull();
      });

      it('should set moreExpensiveProperty to null', () => {
        expect(entry.moreExpensiveProperty).toBeNull();
      });

      it('should set affordMoreSpace to null', () => {
        expect(entry.affordMoreSpace).toBeNull();
      });
    });

    describe('given filled financial details', () => {
      let entry: WealthManagementSummaryEntry;

      beforeEach(() => {
        entry = transformToWealthManagementSummaryEntry(
          '[fake id]',
          '[fake address]',
          null,
          false,
          {
            valueIncrease: {
              increasePercentage: null,
              increaseValue: null,
            },
            savingsBasedOnInterestRateDifference: null,
            moreExpensiveProperty: null,
            extendedMortgage: null,
            equityGrowth: {
              increasePercentage: null,
              increaseValue: null,
            },
            affordMoreSpace: null,
            currentMortgage: null,
            purchasePrice: null,
            valuation: null,
            valuationComponents: null,
          },
          12,
          34,
          56,
          78,
          9,
        );
      });

      it('should set areFinancialDetailsFilled to true', () => {
        expect(entry.areFinancialDetailsFilled).toBeTruthy();
      });
    });
  });

  describe('#aggregateWealthManagementSummaryEntries', () => {
    describe('given some entries', () => {
      const entries = [
        {
          totalEstimatedValue: null,
          mortgage: {
            percentage: null,
            value: null,
          },
          equity: {
            percentage: null,
            value: null,
          },
          value: {
            initial: null,
            current: null,
            increasePercentage: null,
            increaseValue: null,
          },
          storebrandSaving: null,
          extendedMortgage: null,
          moreExpensiveProperty: null,
          affordMoreSpace: null,
        },
        {
          totalEstimatedValue: 1000,
          mortgage: {
            percentage: 0.4,
            value: 400,
          },
          equity: {
            percentage: 0.6,
            value: 600,
          },
          value: {
            initial: 800,
            current: 1000,
            increasePercentage: 0.2,
            increaseValue: 200,
          },
          storebrandSaving: 10,
          extendedMortgage: 20,
          moreExpensiveProperty: 30,
          affordMoreSpace: 40,
        },
        {
          totalEstimatedValue: 10000,
          mortgage: {
            percentage: 0.5,
            value: 5000,
          },
          equity: {
            percentage: 0.5,
            value: 5000,
          },
          value: {
            initial: 6000,
            current: 10000,
            increasePercentage: 0.4,
            increaseValue: 4000,
          },
          storebrandSaving: 1000,
          extendedMortgage: 2000,
          moreExpensiveProperty: 3000,
          affordMoreSpace: 4000,
        },
      ];

      it('should aggregate the values', () => {
        const aggregation = aggregateWealthManagementSummaryEntries(entries);

        expect(aggregation).toMatchObject({
          totalEstimatedValue: 11000,
          mortgage: {
            value: 5400,
          },
          equity: {
            value: 5600,
          },
          value: {
            initial: 6800,
            current: 11000,
            increaseValue: 4200,
          },
          storebrandSaving: 1010,
          extendedMortgage: 2020,
          moreExpensiveProperty: 3030,
          affordMoreSpace: 4040,
        });

        expect(aggregation.value.increasePercentage).toBeCloseTo(0.62, 2);
        expect(aggregation.equity.percentage).toBeCloseTo(0.51, 2);
        expect(aggregation.mortgage.percentage).toBeCloseTo(0.49, 2);
      });
    });
  });
});

type TestCase<TInput, TExpected> = { it: string; input: TInput; expected: TExpected };
type RunTestCases = <TInput, TExpected>(params: {
  functionToTest: (input: TInput) => TExpected;
  testCases: TestCase<TInput, TExpected>[];
}) => void;
const runTestCases: RunTestCases = ({ functionToTest, testCases }) =>
  testCases.forEach((testCase) =>
    it(testCase.it, () => expect(functionToTest(testCase.input)).toEqual(testCase.expected)),
  );

describe('deduplicateEstates', () => {
  const testCases: TestCase<EstateSummary[], EstateSummary[]>[] = [
    {
      it: 'should filter out duplicates by returning the estate with the greatest statuscode',
      input: ([
        { address: 'Address1', vitecStatus: EstateVitecStatus.REQUEST }, // vitecStatus: 0
        {
          address: 'Address1',
          vitecStatus: EstateVitecStatus.PREPARATION,
          createdDate: 'Fri Feb 05 1988 00:00:00 GMT+0100 (Central European Standard Time)',
        }, // vitecStatus: 1
        {
          address: 'Address1',
          vitecStatus: EstateVitecStatus.PREPARATION,
          createdDate: 'Mon Feb 01 1988 00:00:00 GMT+0100 (Central European Standard Time)',
        }, // vitecStatus: 1
        { address: 'Address2', vitecStatus: EstateVitecStatus.OVERSOLD, createdDate: '2022-02-01' }, // vitecStatus: 3
        { address: 'Address2', vitecStatus: EstateVitecStatus.REQUEST, createdDate: '2022-02-02' }, // vitecStatus: 0
        { address: 'Address2', vitecStatus: EstateVitecStatus.FOR_SALE }, // vitecStatus: 2
      ] as unknown) as EstateSummary[],
      expected: ([
        {
          address: 'Address1',
          vitecStatus: EstateVitecStatus.PREPARATION,
          createdDate: 'Fri Feb 05 1988 00:00:00 GMT+0100 (Central European Standard Time)',
        },
        { address: 'Address2', vitecStatus: EstateVitecStatus.OVERSOLD, createdDate: '2022-02-01' },
      ] as unknown) as EstateSummary[],
    },
    {
      it: 'should not filter out duplicates where address is same, but userEstateRelation is different',
      input: ([
        { address: 'Address1', vitecStatus: EstateVitecStatus.REQUEST, userEstateRelation: UserEstateRelation.BUYER }, // vitecStatus: 0
        {
          address: 'Address1',
          vitecStatus: EstateVitecStatus.PREPARATION,
          createdDate: 'Fri Feb 05 1988 00:00:00 GMT+0100 (Central European Standard Time)',
          userEstateRelation: UserEstateRelation.BUYER,
        }, // vitecStatus: 1
        {
          address: 'Address1',
          vitecStatus: EstateVitecStatus.PREPARATION,
          createdDate: 'Mon Feb 01 1988 00:00:00 GMT+0100 (Central European Standard Time)',
          userEstateRelation: UserEstateRelation.BUYER,
        }, // vitecStatus: 1
        {
          address: 'Address2',
          vitecStatus: EstateVitecStatus.OVERSOLD,
          createdDate: '2022-02-01',
          userEstateRelation: UserEstateRelation.BUYER,
        }, // vitecStatus: 3
        {
          address: 'Address2',
          vitecStatus: EstateVitecStatus.REQUEST,
          createdDate: '2022-02-02',
          userEstateRelation: UserEstateRelation.SELLER,
        }, // vitecStatus: 0
        { address: 'Address2', vitecStatus: EstateVitecStatus.FOR_SALE, userEstateRelation: UserEstateRelation.BUYER }, // vitecStatus: 2
      ] as unknown) as EstateSummary[],
      expected: ([
        {
          address: 'Address1',
          vitecStatus: EstateVitecStatus.PREPARATION,
          createdDate: 'Fri Feb 05 1988 00:00:00 GMT+0100 (Central European Standard Time)',
          userEstateRelation: UserEstateRelation.BUYER,
        },
        {
          address: 'Address2',
          vitecStatus: EstateVitecStatus.OVERSOLD,
          createdDate: '2022-02-01',
          userEstateRelation: UserEstateRelation.BUYER,
        },
        {
          address: 'Address2',
          vitecStatus: EstateVitecStatus.REQUEST,
          createdDate: '2022-02-02',
          userEstateRelation: UserEstateRelation.SELLER,
        },
      ] as unknown) as EstateSummary[],
    },
  ];
  runTestCases({ functionToTest: deduplicateEstates, testCases });
});
