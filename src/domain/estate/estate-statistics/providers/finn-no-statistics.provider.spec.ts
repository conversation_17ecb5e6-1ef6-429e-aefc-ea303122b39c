import axios from 'axios';
import type { StatisticsProvider } from '../statistics.service';
import type { FinnNoStatistics } from './finn-no-statistics.provider';
import { createFinnNoStatisticsProvider } from './finn-no-statistics.provider';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('finn.no statistics provider', () => {
  describe('#isPreconditionFulfilled', () => {
    describe('given a valid finn code', () => {
      let statisticsProvider: StatisticsProvider<FinnNoStatistics>;

      beforeEach(() => {
        statisticsProvider = createFinnNoStatisticsProvider('[fake finn code]');
      });

      it('should return true', () => {
        expect(statisticsProvider.isPreconditionFulfilled()).toEqual(true);
      });
    });

    describe.each([null, ''])('given an invalid finn code (finnCode="%s")', (finnCode) => {
      let statisticsProvider: StatisticsProvider<FinnNoStatistics>;

      beforeEach(() => {
        statisticsProvider = createFinnNoStatisticsProvider(finnCode);
      });

      it('should return false', () => {
        expect(statisticsProvider.isPreconditionFulfilled()).toEqual(false);
      });
    });
  });

  describe('#getStatistics', () => {
    let statisticsProvider: StatisticsProvider<FinnNoStatistics>;

    beforeEach(() => {
      statisticsProvider = createFinnNoStatisticsProvider('[fake finn code]');
    });

    describe('given a valid response', () => {
      beforeEach(() => {
        mockedAxios.get.mockResolvedValueOnce({
          data: 'pageviews,emails,stored,timestamp\r\n100,50,1,1970-01-01T00:00:00.000000\r\n',
        });
      });

      it('should return the properly parsed properties', async () => {
        const statistics = await statisticsProvider.getStatistics();

        expect(statistics).toMatchObject({ adViews: 100, receivedAdByEmail: 50, savedAd: 1 });
      });
    });

    describe('given a malformed response', () => {
      beforeEach(() => {
        mockedAxios.get.mockResolvedValueOnce({
          data: '[fake response]',
        });
      });

      it('should return the properties set to 0', async () => {
        const statistics = await statisticsProvider.getStatistics();
        expect(statistics).toMatchObject({ adViews: 0, receivedAdByEmail: 0, savedAd: 0 });
      });
    });
  });
});
