import { addDays, format } from 'date-fns';
import type { analyticsreporting_v4 } from 'googleapis';
import { google } from 'googleapis';
import { path } from 'ramda';
import { config } from '../../../../config';
import type { EstateMongoose } from '../../mongo/estate.mongoose-types';
import type { StatisticsProvider } from '../statistics.service';

export type NordvikboligNoStatistics = {
  totalNumberOfPageViews: number;
  dailyPageViews: number[];
  numberOfSalesProspectDownloads: number;
  numberOfDirectPageViews: number;
  numberOfPaidFinnNoPageViews: number;
  numberOfFinnNoPageViews: number;
  numberOfFacebookPageViews: number;
  numberOfGooglePageViews: number;
};

export const createNordvikboligNoStatisticsProvider = (
  vitecId: EstateMongoose['estateId'] | null,
): StatisticsProvider<NordvikboligNoStatistics> => {
  const GOOGLE_ANALYTICS_START_DATE = '2005-01-01';

  const isPreconditionFulfilled = (): boolean => {
    return !!vitecId && vitecId.length > 0;
  };

  const getStatistics = async (): Promise<NordvikboligNoStatistics | null> => {
    if (!isPreconditionFulfilled()) {
      return null;
    }

    try {
      const [
        { totalNumberOfPageViews },
        { dailyPageViews },
        { numberOfSalesProspectDownloads },
        {
          numberOfDirectPageViews,
          numberOfFinnNoPageViews,
          numberOfPaidFinnNoPageViews,
          numberOfFacebookPageViews,
          numberOfGooglePageViews,
        },
      ] = await Promise.all([
        getNumberOfTotalPageViews(),
        getNumberOfDailyPageViews(config.statistics.nordvikboligNo.numberOfDays),
        getNumberOfSalesProspectDownloads(),
        getNumberOfPageViewsByTrafficSource(),
      ]);

      return {
        totalNumberOfPageViews,
        dailyPageViews,
        numberOfSalesProspectDownloads,
        numberOfDirectPageViews,
        numberOfFinnNoPageViews,
        numberOfPaidFinnNoPageViews,
        numberOfFacebookPageViews,
        numberOfGooglePageViews,
      };
    } catch (e) {
      return null;
    }
  };

  const getNumberOfTotalPageViews = async (): Promise<{
    totalNumberOfPageViews: number;
  }> => {
    const batch = await query([
      {
        viewId: config.statistics.nordvikboligNo.googleAnalytics.viewId,
        dateRanges: [
          {
            startDate: GOOGLE_ANALYTICS_START_DATE,
            endDate: 'today',
          },
        ],
        metrics: [
          {
            expression: 'ga:pageviews',
            alias: 'pageviews',
          },
        ],
        dimensions: [
          {
            name: 'ga:pagePath',
          },
        ],
        dimensionFilterClauses: [
          {
            filters: [
              {
                dimensionName: 'ga:pagePath',
                operator: 'REGEXP',
                expressions: [`boliger/${vitecId ?? ''}$`, `boliger/.+/${vitecId ?? ''}$`],
              },
            ],
          },
        ],
      },
    ]);

    const reports = batch.data.reports;

    if (reports && reports.length > 0) {
      const data = reports[0].data;

      if (data) {
        const totals = data.totals;

        if (totals && totals.length > 0) {
          return {
            totalNumberOfPageViews: parseInt(path(['totals', 0, 'values', 0], data) || '', 10),
          };
        }
      }
    }

    return {
      totalNumberOfPageViews: 0,
    };
  };

  const getNumberOfDailyPageViews = async (
    numberOfDaysInThePast: number,
  ): Promise<{
    dailyPageViews: number[];
  }> => {
    const batch = await query([
      {
        viewId: config.statistics.nordvikboligNo.googleAnalytics.viewId,
        dateRanges: [
          {
            startDate: `${numberOfDaysInThePast - 1}daysAgo`,
            endDate: 'today',
          },
        ],
        metrics: [
          {
            expression: 'ga:pageviews',
            alias: 'pageviews',
          },
        ],
        dimensions: [
          {
            name: 'ga:date',
          },
        ],
        dimensionFilterClauses: [
          {
            filters: [
              {
                dimensionName: 'ga:pagePath',
                operator: 'REGEXP',
                expressions: [`boliger/${vitecId ?? ''}$`, `boliger/.+/${vitecId ?? ''}$`],
              },
            ],
          },
        ],
      },
    ]);

    const basePageViews = Array.from({ length: numberOfDaysInThePast }, (_, i) => {
      return addDays(new Date(), -i);
    })
      .map((date) => format(date, 'yyyyMMdd'))
      .map((date) => {
        return {
          [date]: 0,
        };
      })
      .reduce((acc, curr) => ({ ...acc, ...curr }), {});

    const reports = batch.data.reports;

    if (reports && reports.length > 0) {
      const data = reports[0].data;

      if (data) {
        const rows = data.rows;

        if (rows && rows.length > 0) {
          return {
            dailyPageViews: Object.values({
              ...basePageViews,
              ...rows
                .map((row) => {
                  const date = path<string>(['dimensions', 0], row) || '';
                  const pageViews = parseInt(path<string>(['metrics', 0, 'values', 0], row) || '0', 10);
                  return {
                    [date]: pageViews,
                  };
                })
                .reduce((acc, curr) => ({ ...acc, ...curr }), {}),
            }).reverse(),
          };
        }
      }
    }

    return {
      dailyPageViews: Object.values(basePageViews),
    };
  };

  const getNumberOfSalesProspectDownloads = async (): Promise<{
    numberOfSalesProspectDownloads: number;
  }> => {
    const batch = await query([
      {
        viewId: config.statistics.nordvikboligNo.googleAnalytics.viewId,
        dateRanges: [
          {
            startDate: GOOGLE_ANALYTICS_START_DATE,
            endDate: 'today',
          },
        ],
        metrics: [
          {
            expression: 'ga:pageviews',
            alias: 'pageviews',
          },
        ],
        dimensions: [
          {
            name: 'ga:pagePath',
          },
        ],
        dimensionFilterClauses: [
          {
            filters: [
              {
                dimensionName: 'ga:pagePath',
                operator: 'REGEXP',
                expressions: [
                  `boliger/${vitecId ?? ''}/download/salgsoppgave$`,
                  `boliger/.+/${vitecId ?? ''}/download/salgsoppgave$`,
                  `boliger/${vitecId ?? ''}/salgsoppgave$`,
                  `boliger/.+/${vitecId ?? ''}/salgsoppgave$`,
                  `boliger/${vitecId ?? ''}/salgsoppgave$`,
                  `boliger/.+/${vitecId ?? ''}/salgsoppgave$`,
                ],
              },
            ],
          },
        ],
      },
    ]);

    const reports = batch.data.reports;

    if (reports && reports.length > 0) {
      const data = reports[0].data;

      if (data) {
        const totals = data.totals;

        if (totals && totals.length > 0) {
          return {
            numberOfSalesProspectDownloads: parseInt(path(['totals', 0, 'values', 0], data) || '0', 10),
          };
        }
      }
    }

    return {
      numberOfSalesProspectDownloads: 0,
    };
  };

  const accumulateTrafficFromSourceMedium = (
    rows: analyticsreporting_v4.Schema$ReportRow[],
    sourceMediumPattern: RegExp,
  ): number => {
    return rows
      .filter((row: analyticsreporting_v4.Schema$ReportRow) => {
        return row?.dimensions && sourceMediumPattern.test(row.dimensions[1]);
      })
      .map((row: analyticsreporting_v4.Schema$ReportRow) => parseInt(path(['metrics', 0, 'values', 0], row) || '0', 10))
      .reduce((sum, value) => sum + value, 0);
  };

  const getNumberOfPageViewsByTrafficSource = async (): Promise<{
    numberOfDirectPageViews: number;
    numberOfFinnNoPageViews: number;
    numberOfPaidFinnNoPageViews: number;
    numberOfFacebookPageViews: number;
    numberOfGooglePageViews: number;
  }> => {
    const batch = await query([
      {
        viewId: config.statistics.nordvikboligNo.googleAnalytics.viewId,
        dateRanges: [
          {
            startDate: GOOGLE_ANALYTICS_START_DATE,
            endDate: 'today',
          },
        ],
        metrics: [
          {
            expression: 'ga:pageviews',
            alias: 'pageviews',
          },
        ],
        dimensions: [
          {
            name: 'ga:pagePath',
          },
          {
            name: 'ga:sourceMedium',
          },
        ],
        dimensionFilterClauses: [
          {
            filters: [
              {
                dimensionName: 'ga:pagePath',
                operator: 'REGEXP',
                expressions: [`boliger/${vitecId ?? ''}$`, `boliger/.+/${vitecId ?? ''}$`],
              },
            ],
          },
        ],
      },
    ]);

    const reports = batch.data.reports;

    if (reports && reports.length > 0) {
      const data = reports[0].data;

      if (data) {
        const rows = data.rows;

        if (!!rows && rows.length > 0) {
          return {
            // Based on https://analytics.google.com/analytics/web/#/report/trafficsources-all-traffic/a120748702w178460882p178915682/explorer-table.plotKeys=%5B%5D&explorer-table.rowStart=0&explorer-table.rowCount=5000/
            numberOfDirectPageViews: accumulateTrafficFromSourceMedium(rows, /\(direct\)/),
            numberOfFinnNoPageViews: accumulateTrafficFromSourceMedium(rows, /finn \/ (estate-page|netboard)/),
            numberOfPaidFinnNoPageViews: accumulateTrafficFromSourceMedium(rows, /finn\.no \/ paid social/),
            numberOfFacebookPageViews: accumulateTrafficFromSourceMedium(rows, /facebook/),
            numberOfGooglePageViews: accumulateTrafficFromSourceMedium(rows, /google/),
          };
        }
      }
    }

    return {
      numberOfDirectPageViews: 0,
      numberOfFinnNoPageViews: 0,
      numberOfPaidFinnNoPageViews: 0,
      numberOfFacebookPageViews: 0,
      numberOfGooglePageViews: 0,
    };
  };

  return { getStatistics, isPreconditionFulfilled };
};

const query = async (
  reports: Record<string, unknown>[],
): Promise<{ data: analyticsreporting_v4.Schema$GetReportsResponse }> => {
  const jwt = new google.auth.JWT(
    config.statistics.nordvikboligNo.googleAnalytics.email,
    undefined,
    config.statistics.nordvikboligNo.googleAnalytics.privateKey.replace(/\\n/gm, '\n'),
    'https://www.googleapis.com/auth/analytics.readonly',
  );

  const response = await google
    .analyticsreporting('v4')
    .reports.batchGet({ auth: jwt, requestBody: { reportRequests: reports } });

  console.log('Nordvikbolig.no statistics response', response.data);
  return response;
};
