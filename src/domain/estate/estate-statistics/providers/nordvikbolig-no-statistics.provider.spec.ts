import * as googleapis from 'googleapis';
import type { StatisticsProvider } from '../statistics.service';
import type { NordvikboligNoStatistics } from './nordvikbolig-no-statistics.provider';
import { createNordvikboligNoStatisticsProvider } from './nordvikbolig-no-statistics.provider';

jest.mock('googleapis');
const mockedGoogleapis = googleapis as jest.Mocked<typeof googleapis>;

describe('nordvikbolig.no statistics provider', () => {
  describe('#isPreconditionFulfilled', () => {
    describe('given a valid finn code', () => {
      let statisticsProvider: StatisticsProvider<NordvikboligNoStatistics>;

      beforeEach(() => {
        statisticsProvider = createNordvikboligNoStatisticsProvider('[fake vitec id]');
      });

      it('should return true', () => {
        expect(statisticsProvider.isPreconditionFulfilled()).toEqual(true);
      });
    });

    describe.each([null, ''])('given an invalid vitec id (vitecId="%s")', (vitecId) => {
      let statisticsProvider: StatisticsProvider<NordvikboligNoStatistics>;

      beforeEach(() => {
        statisticsProvider = createNordvikboligNoStatisticsProvider(vitecId);
      });

      it('should return false', () => {
        expect(statisticsProvider.isPreconditionFulfilled()).toEqual(false);
      });
    });
  });

  describe('#getStatistics', () => {
    let statisticsProvider: StatisticsProvider<NordvikboligNoStatistics>;

    beforeEach(() => {
      statisticsProvider = createNordvikboligNoStatisticsProvider('[fake vitec id]');
    });

    describe('given valid responses', () => {
      beforeEach(() => {
        mockedGoogleapis.google.analyticsreporting = jest
          .fn()
          .mockReturnValueOnce({
            reports: {
              batchGet: jest.fn().mockReturnValueOnce({
                data: {
                  reports: [
                    {
                      data: {
                        totals: [{ values: ['100'] }, { values: ['200'] }],
                      },
                    },
                  ],
                },
              }),
            },
          })
          .mockReturnValueOnce({
            reports: {
              batchGet: jest.fn().mockReturnValueOnce({
                data: {
                  reports: [
                    {
                      data: {
                        rows: [],
                      },
                    },
                  ],
                },
              }),
            },
          })
          .mockReturnValueOnce({
            reports: {
              batchGet: jest.fn().mockResolvedValueOnce({
                data: {
                  reports: [
                    {
                      data: {
                        totals: [{ values: ['300'] }],
                      },
                    },
                  ],
                },
              }),
            },
          })
          .mockReturnValueOnce({
            reports: {
              batchGet: jest.fn().mockResolvedValueOnce({
                data: {
                  reports: [
                    {
                      data: {
                        rows: [
                          {
                            dimensions: ['[fake url]', '(direct)'],
                            metrics: [{ values: [10] }],
                          },
                          {
                            dimensions: ['[fake url]', 'finn.no / paid social'],
                            metrics: [{ values: [20] }],
                          },
                          {
                            dimensions: ['[fake url]', 'finn / estate-page'],
                            metrics: [{ values: [15] }],
                          },
                          {
                            dimensions: ['[fake url]', 'finn / netboard'],
                            metrics: [{ values: [15] }],
                          },
                          {
                            dimensions: ['[fake url]', 'facebook.com'],
                            metrics: [{ values: [40] }],
                          },
                          {
                            dimensions: ['[fake url]', 'google.com'],
                            metrics: [{ values: [50] }],
                          },
                        ],
                      },
                    },
                  ],
                },
              }),
            },
          });
      });

      it('should return the properly parsed properties', async () => {
        const statistics = await statisticsProvider.getStatistics();

        expect(statistics).toMatchObject({
          dailyPageViews: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          numberOfDirectPageViews: 10,
          numberOfFacebookPageViews: 40,
          numberOfFinnNoPageViews: 30,
          numberOfGooglePageViews: 50,
          numberOfPaidFinnNoPageViews: 20,
          numberOfSalesProspectDownloads: 300,
          totalNumberOfPageViews: 100,
        });
      });
    });

    describe('given an empty response', () => {
      beforeEach(() => {
        mockedGoogleapis.google.analyticsreporting = jest.fn().mockReturnValue({
          reports: {
            batchGet: jest.fn().mockReturnValue({
              data: {
                reports: [],
              },
            }),
          },
        });
      });

      it('should return the properties set to 0', async () => {
        const statistics = await statisticsProvider.getStatistics();
        expect(statistics).toMatchObject({
          dailyPageViews: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          numberOfDirectPageViews: 0,
          numberOfFacebookPageViews: 0,
          numberOfFinnNoPageViews: 0,
          numberOfGooglePageViews: 0,
          numberOfPaidFinnNoPageViews: 0,
          numberOfSalesProspectDownloads: 0,
          totalNumberOfPageViews: 0,
        });
      });
    });
  });
});
