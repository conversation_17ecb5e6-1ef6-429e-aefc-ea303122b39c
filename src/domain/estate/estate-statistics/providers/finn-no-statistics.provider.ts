import axios from 'axios';
import { config } from '../../../../config';
import type { EstateMongoose } from '../../mongo/estate.mongoose-types';
import type { StatisticsProvider } from '../statistics.service';

type FinnNoResponse = {
  pageviews: number;
  emails: number;
  stored: number;
};

export type FinnNoStatistics = {
  adViews: number;
  receivedAdByEmail: number;
  savedAd: number;
  adUrl: string;
};

export const getAdUrl = (finnCode: string | null): string => {
  return `${config.statistics.finnNo.adUrl}/${finnCode}`;
};

export const createFinnNoStatisticsProvider = (
  finnCode: EstateMongoose['finnCode'] | null,
): StatisticsProvider<FinnNoStatistics> => {
  const isPreconditionFulfilled = (): boolean => {
    return !!finnCode && finnCode.length > 0;
  };

  const getStatistics = async (): Promise<FinnNoStatistics | null> => {
    if (!isPreconditionFulfilled()) {
      return null;
    }

    try {
      const statistics = await requestStatistics();

      return {
        adViews: statistics.pageviews ?? 0,
        receivedAdByEmail: statistics.emails ?? 0,
        savedAd: statistics.stored ?? 0,
        adUrl: getAdUrl(finnCode),
      };
    } catch (error) {
      return null;
    }
  };

  const requestStatistics = async (): Promise<FinnNoResponse> => {
    const response = await axios.get(formatUrl(), {
      headers: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'X-FINN-ApiKey': config.statistics.finnNo.apiKey,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        'Content-Type': 'application/json',
      },
    });

    return parseResponse((response.data as unknown) as string);
  };

  const parseResponse = (response: string): FinnNoResponse => {
    const splitLines = response.split('\r\n').map((line) => line.split(','));
    const keys = splitLines[0];
    const values = splitLines[1];

    const requiredFields = ['pageviews', 'emails', 'stored'];
    return keys.reduce(
      (acc, curr, i) => (requiredFields.includes(curr) ? { ...acc, [curr]: parseInt(values[i], 10) } : acc),
      {},
    ) as FinnNoResponse;
  };

  const formatUrl = (): string => {
    return `${config.statistics.finnNo.url}/ad/realestate-project-unit/${finnCode ?? ''}/statistics`;
  };

  return { getStatistics, isPreconditionFulfilled };
};
