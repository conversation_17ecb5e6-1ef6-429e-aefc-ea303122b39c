import type { HjemNoConfig } from '../../../config';
import type { HjemNoService, HjemNoStatistics } from '../../hjem-no/hjem-no.service';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import type { FinnNoStatistics } from './providers/finn-no-statistics.provider';
import { createFinnNoStatisticsProvider } from './providers/finn-no-statistics.provider';
import type { NordvikboligNoStatistics } from './providers/nordvikbolig-no-statistics.provider';

export type Statistics = {
  finnNo: FinnNoStatistics | null;
  nordvikboligNo: NordvikboligNoStatistics | null;
  hjemNo: HjemNoStatistics | null;
};

export type StatisticsService = {
  getStatistics(
    finnCode: EstateMongoose['finnCode'],
    estateId: EstateMongoose['estateId'],
    systemName?: string,
    installationId?: string,
  ): Promise<Statistics>;
};

export type StatisticsProvider<T> = {
  isPreconditionFulfilled(): boolean;
  getStatistics(): Promise<T | null>;
};

export const statisticsServiceFactory = ({
  hjemNoService,
  hjemNoConfig,
}: {
  hjemNoService?: HjemNoService;
  hjemNoConfig?: HjemNoConfig;
} = {}): StatisticsService => {
  const getStatistics = async (
    finnCode: EstateMongoose['finnCode'],
    _estateId: EstateMongoose['estateId'],
    systemName?: string,
    installationId?: string,
  ): Promise<Statistics> => {
    let hjemNoStats: HjemNoStatistics | null = null;

    // Get Hjem.no statistics if service is available and required parameters are provided
    if (hjemNoService && hjemNoConfig && systemName && installationId && _estateId) {
      try {
        hjemNoStats = await hjemNoService.getStatistics(systemName, installationId, _estateId);
      } catch (error) {
        // Log error but don't fail the entire request
        console.error('Failed to fetch Hjem.no statistics:', error);
        hjemNoStats = null;
      }
    }

    return {
      finnNo: await createFinnNoStatisticsProvider(finnCode).getStatistics(),
      // nordvikboligNo: await createNordvikboligNoStatisticsProvider(estateId).getStatistics(),
      nordvikboligNo: null,
      hjemNo: hjemNoStats,
    };
  };

  return { getStatistics };
};
