import type { User } from '../../user/user';
import type { EstateChecklist } from '../estate-checklist/estate-checklist';
import type { EstateTodo } from '../estate-todo/estate-todo';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import type { DoneEstateChecklistTodo } from './estate-checklist-connection';
import { EstateChecklistConnectionModel } from './estate-checklist-connection.model';

export type EstateChecklistConnectionRepository = {
  getDoneTodos(
    userID: User['id'],
    checklistID: EstateChecklist['checklistID'],
    vitecID: EstateMongoose['estateId'],
  ): Promise<DoneEstateChecklistTodo[]>;
  toggleDoneTodo(
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
    checklistID: EstateChecklist['checklistID'],
    doneTodoID: EstateTodo['todoID'],
  ): Promise<void>;
};

export const estateChecklistConnectionTransformer = (
  estateChecklistModel: EstateChecklistConnectionModel,
): DoneEstateChecklistTodo => ({
  id: estateChecklistModel.id,
  createdAt: estateChecklistModel.createdAt,
  updatedAt: estateChecklistModel.updatedAt,
  checklistID: estateChecklistModel.checklistID,
  vitecID: estateChecklistModel.vitecID,
  userID: estateChecklistModel.userID,
  doneTodoID: estateChecklistModel.doneTodoID,
});

export const estateChecklistConnectionRepositoryFactory = (): EstateChecklistConnectionRepository => {
  const getDoneTodos = async (
    userID: User['id'],
    checklistID: EstateChecklist['checklistID'],
    vitecID: EstateMongoose['estateId'],
  ): Promise<DoneEstateChecklistTodo[]> => {
    const todos = await EstateChecklistConnectionModel.findAll({
      where: {
        vitecID,
        checklistID,
        userID,
      },
      attributes: ['userID', 'vitecID', 'checklistID', 'doneTodoID'],
    });
    return todos.map((todo) => estateChecklistConnectionTransformer(todo));
  };

  const toggleDoneTodo = async (
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
    checklistID: EstateChecklist['checklistID'],
    doneTodoID: EstateTodo['todoID'],
  ): Promise<void> => {
    const doneTodo = await EstateChecklistConnectionModel.findOne({
      limit: 3000,
      where: {
        userID,
        vitecID,
        checklistID,
        doneTodoID,
      },
      attributes: ['userID', 'vitecID', 'checklistID', 'doneTodoID'],
    });
    if (!doneTodo) {
      return createDoneTodo(userID, vitecID, checklistID, doneTodoID);
    }
    return deleteDoneTodo(userID, vitecID, checklistID, doneTodoID);
  };

  const createDoneTodo = async (
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
    checklistID: EstateChecklist['checklistID'],
    doneTodoID: EstateTodo['todoID'],
  ): Promise<void> => {
    await EstateChecklistConnectionModel.create({
      userID,
      vitecID,
      checklistID,
      doneTodoID,
    });
  };

  const deleteDoneTodo = async (
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
    checklistID: EstateChecklist['checklistID'],
    doneTodoID: EstateTodo['todoID'],
  ): Promise<void> => {
    await EstateChecklistConnectionModel.destroy({
      where: {
        userID,
        vitecID,
        checklistID,
        doneTodoID,
      },
    });
  };

  return { getDoneTodos, toggleDoneTodo };
};
