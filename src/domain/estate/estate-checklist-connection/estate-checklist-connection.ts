import type { Resource } from '../../../framework/sequelize/resource';
import type { User } from '../../user/user';
import type { EstateChecklist } from '../estate-checklist/estate-checklist';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';

export type DoneEstateChecklistTodo = Resource & {
  vitecID: EstateMongoose['estateId'];
  userID: User['id'];
  checklistID: EstateChecklist['checklistID'];
  doneTodoID: number;
};
