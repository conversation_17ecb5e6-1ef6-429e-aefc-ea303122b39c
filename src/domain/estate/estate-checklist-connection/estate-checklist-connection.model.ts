import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../../framework/sequelize/resource';
import { UserModel } from '../../user/user.model';
import { EstateChecklistModel } from '../estate-checklist/estate-checklist.model';
import { EstateTodoModel } from '../estate-todo/estate-todo.model';

const ESTATE_CHECKLIST_CONNECTION = 'EstateChecklistConnection';
const ESTATE_CHECKLIST_ID_ENUM_NAME = 'estate_checklist_id_enum';

const estateChecklistConnectionAttributes: ModelAttributes = {
  ...baseModelAttributes,
  vitecID: {
    type: DataTypes.TEXT,
  },
  userID: {
    type: DataTypes.UUID,
    references: {
      model: UserModel,
      key: 'id',
    },
    allowNull: false,
  },
  checklistID: {
    type: ESTATE_CHECKLIST_ID_ENUM_NAME,
    references: {
      model: EstateChecklistModel,
      key: 'checklistID',
    },
    allowNull: false,
  },
  doneTodoID: {
    type: DataTypes.INTEGER,
    references: {
      model: EstateTodoModel,
      key: 'todoID',
    },
    allowNull: false,
  },
};

export class EstateChecklistConnectionModel extends ResourceModel {
  public vitecID!: string;
  public userID!: UserModel['id'];
  public checklistID!: EstateChecklistModel['checklistID'];
  public doneTodoID!: number;
}

export const estateChecklistConnectionModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstateChecklistConnectionModel.init(estateChecklistConnectionAttributes, {
    sequelize,
    tableName: ESTATE_CHECKLIST_CONNECTION,
    timestamps: true,
  });
};

export const setEstateChecklistConnectionModelReferences = (): void => {
  EstateChecklistConnectionModel.belongsTo(EstateChecklistModel, { foreignKey: 'checklistID' });
  EstateChecklistConnectionModel.belongsTo(UserModel, {
    foreignKey: 'userID',
    as: 'user',
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  });
  EstateChecklistConnectionModel.belongsTo(EstateTodoModel, { foreignKey: 'doneTodoID' });
};
