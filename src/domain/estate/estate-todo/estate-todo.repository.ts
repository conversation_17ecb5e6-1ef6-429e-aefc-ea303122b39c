import { EstateChecklistType } from '../estate-checklist/estate-checklist';
import type { EstateTodo } from './estate-todo';
import { EstateTodoModel } from './estate-todo.model';

export type EstateTodoRepository = {
  getSellerTodos(): Promise<EstateTodo[]>;
};

export const todoTransformer = (todoModel: EstateTodoModel): EstateTodo => ({
  id: todoModel.id,
  createdAt: todoModel.createdAt,
  updatedAt: todoModel.updatedAt,
  checklistID: todoModel.checklistID,
  todoID: todoModel.todoID,
  title: todoModel.title,
  description: todoModel.description,
  type: todoModel.type,
  logName: todoModel.logName,
});

export const estateTodoRepositoryFactory = (): EstateTodoRepository => {
  const getSellerTodos = async (): Promise<EstateTodo[]> => {
    const todos = await EstateTodoModel.findAll({ type: EstateChecklistType.SELLER, order: [['todoID', 'ASC']] });
    return todos.map((todo) => todoTransformer(todo));
  };
  return { getSellerTodos };
};
