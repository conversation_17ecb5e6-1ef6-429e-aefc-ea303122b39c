import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../../framework/sequelize/resource';
import type { EstateChecklistType } from '../estate-checklist/estate-checklist';
import { EstateChecklistModel } from '../estate-checklist/estate-checklist.model';

export const ESTATE_TODO_TABLE = 'EstateTodos';
const ESTATE_CHECKLIST_ID_ENUM_NAME = 'estate_checklist_id_enum';
const ESTATE_CHECKLIST_TYPE_ENUM_NAME = 'estate_checklist_type_enum';

const estateTodoAttributes: ModelAttributes = {
  ...baseModelAttributes,
  checklistID: {
    type: ESTATE_CHECKLIST_ID_ENUM_NAME,
    references: {
      model: EstateChecklistModel,
      key: 'checklistID',
    },
    allowNull: false,
  },
  todoID: {
    type: DataTypes.INTEGER,
    allowNull: false,
    autoIncrement: true,
    unique: true,
  },
  title: {
    type: DataTypes.TEXT,
  },
  description: {
    type: DataTypes.TEXT,
  },
  type: {
    type: ESTATE_CHECKLIST_TYPE_ENUM_NAME,
  },
  logName: {
    type: DataTypes.STRING,
  },
};

export class EstateTodoModel extends ResourceModel {
  public checklistID!: EstateChecklistModel['checklistID'];
  public todoID!: number;
  public title: string;
  public description: string;
  public type: EstateChecklistType;
  public logName: string;
}

export const estateTodoModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstateTodoModel.init(estateTodoAttributes, {
    sequelize,
    tableName: ESTATE_TODO_TABLE,
    timestamps: true,
  });
};

export const setEstateTodoModelReferences = (): void => {
  EstateTodoModel.belongsTo(EstateChecklistModel, { foreignKey: 'checklistID' });
};
