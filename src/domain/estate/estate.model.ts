import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import { UserModel } from '../user/user.model';
import type { EstateType, UserPreference } from './estate';

export const ESTATE_TYPE_ENUM_NAME = 'estate_type_enum';
export const ESTATES_TABLE_NAME = 'Estates';

export const PROPERTY_ENUM_NAME = 'property_enum';
export const USER_PREFERENCE_ENUM_NAME = 'user_preference_enum';

export const estateAttributes: ModelAttributes = {
  ...baseModelAttributes,
  userID: {
    type: DataTypes.UUID,
    references: {
      model: UserModel,
      key: 'id',
    },
    allowNull: false,
  },
  type: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  address: {
    type: DataTypes.TEXT,
  },
  propertyType: {
    type: DataTypes.TEXT,
  },
  numberOfBedrooms: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  livingArea: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  buildYear: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  floor: {
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  sellPreference: {
    type: USER_PREFERENCE_ENUM_NAME,
  },
  landIdentificationMatrix: {
    type: DataTypes.JSONB,
    allowNull: true,
  },
  connectToBroker: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  ownership: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  // Don't use this as REST API does not give back this
  EVEstateID: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  EVAddressID: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  loanAmount: {
    type: DataTypes.INTEGER,
  },
  interestRate: {
    type: DataTypes.FLOAT,
  },
  originalMortgage: {
    type: DataTypes.INTEGER,
  },
  mortgageYears: {
    type: DataTypes.INTEGER,
  },
  imageUrl: {
    type: DataTypes.STRING,
  },
  OrganizationNumber: {
    type: DataTypes.STRING,
  },
  ShareNumber: {
    type: DataTypes.STRING,
  },
  isArchived: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  estimationOffset: {
    type: DataTypes.REAL,
    allowNull: true,
  },
};

export class EstateModel extends ResourceModel {
  public userID!: UserModel['id'];
  public type!: EstateType;
  public valuation: boolean;
  public address: string;
  public propertyType: string;
  public numberOfBedrooms: number | null;
  public livingArea: number | null;
  public buildYear: number | null;
  public floor: number | null;
  public sellPreference: UserPreference;
  public landIdentificationMatrix: LandIdentificationMatrix;
  public connectToBroker: boolean;
  public ownership: string;
  public EVEstateID: string; // Don't use this as REST API does not give back this
  public EVAddressID: string;
  public loanAmount: number;
  public interestRate: number;
  public originalMortgage: number;
  public mortgageYears: number;
  public imageUrl: string;
  public OrganizationNumber: string;
  public ShareNumber: string;
  public isArchived: boolean;
  public estimationOffset: number;
}

export const estateModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstateModel.init(estateAttributes, { sequelize, tableName: ESTATES_TABLE_NAME, timestamps: true });
};

export const setEstateModelReferences = (): void => {
  EstateModel.belongsTo(UserModel, { foreignKey: 'id', as: 'estates', onUpdate: 'CASCADE', onDelete: 'CASCADE' });
};
