import type { Resource } from '../../framework/sequelize/resource';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import type { User } from '../user/user';
import type { EstateBid } from './estate-bid/estate-bid';
import type { Employee } from './estate-employee/estate-employee';
import type { Statistics } from './estate-statistics/statistics.service';
import type { EstateMongoose } from './mongo/estate.mongoose-types';

export enum EstateType {
  FOR_SALE = 'forSale',
  OWNED = 'owned',
}

export enum AdsAdStatus {
  LIVE = 2, //Aktiv
  SOLD = 3, //Solgt
  STOPPED = 4, //Stoppet
  EXPIRED = 5, //Utløpt
}

export enum AdsChannels {
  FINN = 1, // Finn.no
  ZETT = 8, // Amedia
  NE = 16, // NE.NO
  PUBLISH = 32, // Egenpublisering
}

export enum PropertyType {
  HOUSE = 'house',
  APARTMENT = 'apartment',
  DETACHED_HOUSE = 'detached house',
  TOWNHOUSE = 'townhouse',
  SEMI_DETACHED_HOUSE = 'semi-detached house',
  PLOT = 'plot',
  HOLIDAY_HOME = 'holiday home',
}

export enum UserPreference {
  NOW = 'now',
  WITHIN_SIX_MONTHS = 'withinSixMonths',
  RIGHT_PRICE = 'rightPrice',
  DONT = 'dont',
}

export enum EstateVitecStatus {
  OWN_ARCHIVED = -1, // does no exists on vitec
  REQUEST = 0,
  PREPARATION = 1,
  FOR_SALE = 2,
  OVERSOLD = 3,
  RESERVED = 4, // Bjørnar: it's fine that the seller sees the estate if it is in this status
  ARCHIVED = 5, // Bjørnar: if it is archived, we lose access to it in HUB and set it to -1
  EXPIRED = 6, // Bjørnar: the expiration date of the contract between seller and broker is expired, and at this point it probably doesnt matter because the customer no longer uses the app
  TERMINATED = 7, // Bjørnar: customer is dissatisfied, and terminates the contract (e.g. because the broker was not able to sell the property), and has no interest in using the app
}

export type EstatePG = Resource & {
  userID: User['id'];
  type: EstateType;
  address: string;
  propertyType: string;
  numberOfBedrooms: number | null;
  livingArea: number | null;
  buildYear: number | null;
  floor: number | null;
  sellPreference: UserPreference;
  landIdentificationMatrix: LandIdentificationMatrix;
  connectToBroker: boolean;
  ownership: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  EVEstateID: string; // Don't use this as REST API does not give back this
  // eslint-disable-next-line @typescript-eslint/naming-convention
  EVAddressID: string;
  loanAmount: number | null;
  interestRate: number | null;
  originalMortgage: number | null;
  mortgageYears: number | null;
  estateImage: string | null;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  ShareNumber: string | null;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  OrganizationNumber: string | null;
  isArchived: boolean;
  estimationOffset: number | null;
};

export type EstatePGUpdates = Partial<Omit<EstatePG, 'id' | 'createdAt'>>;

export type EstateImage = {
  small: string;
  medium: string;
  large: string;
};

export enum UserEstateRelation {
  BUYER = 'buyer',
  SELLER = 'seller',
  BROKER = 'broker',
}

export type EstateSummary = {
  id: string;
  vitecID: EstateMongoose['estateId'] | null;
  pgEstateId: EstatePG['id'] | null;
  type: EstateType;
  propertyType: string;
  image: EstateImage | null;
  address: string;
  zipCode: string;
  streetAddress: string;
  status: {
    remainingSteps: number;
    stepName: string;
  };
  broker: {
    name: string;
    image: EstateImage;
    mobilePhone: string | null;
    workPhone: string | null;
    email: string | null;
  } | null;
  soldDate: string | null;
  soldPrice: number | null;
  takeOverDate: string | null;
  createdDate: string | null;
  statusText: string;
  vitecStatus: EstateVitecStatus | null;
  floor: number | null;
  area: number | null;
  buildYear: number | null;
  numberOfBedrooms: number | null;
  sellPreference: string;
  connectToBroker: boolean;
  ownerSince: string | null;
  estimatedValue: number | null;
  valuationChange: { increasePercentage: number | null; increaseValue: number | null };
  salesProcess: { stepName: string | undefined; nextEvent: string | undefined };
  bidValue: number | null;
  userEstateRelation: UserEstateRelation | null;
  shareNumber: string | null;
  orgNumber: string | null;
  areEstateImagesShowableInPictureGallery: boolean | null;
  departmentId: number | null;
};

export type EstatePrice = {
  label: string;
  value: number;
  lastBidValue: number;
};

export type Estate = {
  id: string;
  vitecID: EstateMongoose['estateId'] | null;
  pgEstateId: EstatePG['id'] | null;
  address: string;
  image: EstateImage | null;
  matches: number;
  primaryRoomArea: number | null;
  numberOfBedrooms: number | null;
  propertyType: string;
  type: EstateType;
  valuation: number | null;
  purchasePrice: number;
  statusText: string;
  brokers: Employee[];
  bids: EstateBid[];
  price: EstatePrice;
  statistics: Statistics | null;
  floor: number | null;
  estimatedValue: {
    value: number;
    acquisitionDate: string;
    increaseSinceAcquisition: number;
  } | null;
  ownership: string;
  constructionYear: number | null;
  sellPreference: string;
  connectToBroker: boolean;
  status: number;
  userEstateRelation: UserEstateRelation | null;
  wentToViewingNo: number | null;
  interestedNo: number | null;
};

export type EstateUpdates = {
  id: string;
  sellPreference: UserPreference;
  connectToBroker: boolean;
};

export type PremarketEstateSummary = {
  id: EstateMongoose['estateId'];
  status: EstateMongoose['status'];
  heading: EstateMongoose['heading'];
  address: string;
  propertyType: EstateMongoose['estateType'];
  postalCode: string;
  ownership: string;
  estimatedPrice: EstateMongoose['estatePrice']['estimatedValue'];
  numberOfBedrooms: EstateMongoose['noOfBedRooms'];
  // livingArea: EstateMongoose['estateSize']['primaryRoomArea'];
  livingArea: number;
  broker: { name: string; image: EstateImage; email: string; mobilePhone: string; workPhone: string };
  image: { large: string; medium: string; small: string } | null;
  builtYear: EstateMongoose['constructionYear'];
  expireDate: EstateMongoose['expireDate'];
  assignmentNum: EstateMongoose['assignmentNum'];
};

export type EstateImagesNordvikboligResponse = {
  data: Record<string, EstateImages>;
};

export type EstateImages = {
  estateId: string;
  images: [
    {
      url: {
        small: string;
        medium: string;
        large: string;
      };
      imageSequence: number;
    },
  ];
};

export type SummarizableEstate = {
  getBaseLandIdentificationMatrix(): LandIdentificationMatrix | null;
  matchesLandIdentificationMatrix(other: SummarizableEstate): boolean;
  toEstateSummary(): EstateSummary;
};
