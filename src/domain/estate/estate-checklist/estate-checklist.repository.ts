import type { EstateChecklist } from './estate-checklist';
import { EstateChecklistModel } from './estate-checklist.model';

export type EstateChecklistRepository = {
  getSellerChecklists(): Promise<EstateChecklist[]>;
};

export const checklsitTransformer = (checklistModel: EstateChecklistModel): EstateChecklist => {
  return {
    id: checklistModel.id,
    createdAt: checklistModel.createdAt,
    updatedAt: checklistModel.updatedAt,
    checklistID: checklistModel.checklistID,
    title: checklistModel.title,
    type: checklistModel.type,
    logName: checklistModel.logName,
    sortOrder: checklistModel.sortOrder,
  };
};

export const estateChecklistRepositoryFactory = (): EstateChecklistRepository => {
  const getSellerChecklists = async (): Promise<EstateChecklist[]> => {
    const checklists = await EstateChecklistModel.findAll();
    return checklists.map((checklist) => checklsitTransformer(checklist));
  };

  return { getSellerChecklists };
};
