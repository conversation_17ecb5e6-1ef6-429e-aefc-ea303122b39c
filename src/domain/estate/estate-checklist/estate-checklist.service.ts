import Bluebird from 'bluebird';
import type { User } from '../../user/user';
import type { EstateChecklistConnectionRepository } from '../estate-checklist-connection/estate-checklist-connection.repository';
import type { EstateTodo } from '../estate-todo/estate-todo';
import type { EstateTodoRepository } from '../estate-todo/estate-todo.repository';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import type { EstateChecklist, EstateChecklistType } from './estate-checklist';
import type { EstateChecklistRepository } from './estate-checklist.repository';

export type EstateChecklistReponse = EstateChecklist & {
  items: (EstateTodo & { isComplete: boolean })[];
};

export type EstateChecklistService = {
  getChecklist(
    checklistType: EstateChecklistType,
    userID?: User['id'],
    vitecID?: EstateMongoose['estateId'],
  ): Promise<EstateChecklistReponse[]>;
};

export const estateChecklistServiceFactory = ({
  estateChecklistRepository,
  estateTodoRepository,
  estateChecklistConnectionRepository,
}: {
  estateChecklistRepository: EstateChecklistRepository;
  estateTodoRepository: EstateTodoRepository;
  estateChecklistConnectionRepository: EstateChecklistConnectionRepository;
}): EstateChecklistService => {
  const getChecklist = async (
    checklistType: EstateChecklistType,
    userID?: User['id'],
    vitecID?: EstateMongoose['estateId'],
  ): Promise<EstateChecklistReponse[]> => {
    const checklists = (await estateChecklistRepository.getSellerChecklists())
      .filter((c) => c.type === checklistType)
      .sort((a, b) => a.sortOrder - b.sortOrder);
    const todos = await estateTodoRepository.getSellerTodos();
    const formattedChecklists = await Bluebird.map(checklists, async (checklist) => {
      const doneTodos =
        !!userID && !!vitecID
          ? await estateChecklistConnectionRepository.getDoneTodos(userID, checklist.checklistID, vitecID)
          : [];
      const todoItems = todos
        .filter((todo) => todo.checklistID === checklist.checklistID)
        .map((todo) => ({
          ...todo,
          isComplete: !!doneTodos.filter((doneTodo) => todo.todoID === doneTodo.doneTodoID).length,
        }));

      return {
        ...checklist,
        items: todoItems,
      };
    });
    return (formattedChecklists as unknown) as EstateChecklistReponse[];
  };
  return { getChecklist };
};
