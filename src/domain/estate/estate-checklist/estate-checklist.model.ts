import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../../framework/sequelize/resource';
import { EstateTodoModel } from '../estate-todo/estate-todo.model';
import type { EstateChecklistID, EstateChecklistType } from './estate-checklist';

export const ESTATE_CHECKLIST_ID_ENUM_NAME = 'estate_checklist_id_enum';
export const ESTATE_CHECKLIST_TYPE_ENUM_NAME = 'estate_checklist_type_enum';
export const ESTATE_CHECKLIST_TABLE = 'EstateChecklists';
export const ESTATE_CHECKLIST_CONNECTION = 'EstateChecklistConnection';

const estateChecklistAttributes: ModelAttributes = {
  ...baseModelAttributes,
  checklistID: {
    type: ESTATE_CHECKLIST_ID_ENUM_NAME,
    unique: true,
  },
  title: {
    type: DataTypes.TEXT,
  },
  type: {
    type: ESTATE_CHECKLIST_TYPE_ENUM_NAME,
  },
  logName: {
    type: DataTypes.STRING,
  },
  sortOrder: {
    type: DataTypes.INTEGER,
  },
};

export class EstateChecklistModel extends ResourceModel {
  public checklistID!: EstateChecklistID;
  public title: string;
  public type: EstateChecklistType;
  public todos: EstateTodoModel[];
  public logName: string;
  public sortOrder!: number;
}

export const estateChecklistModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstateChecklistModel.init(estateChecklistAttributes, {
    sequelize,
    tableName: ESTATE_CHECKLIST_TABLE,
    timestamps: true,
  });
};

export const setEstateChecklistModelReferences = (): void => {
  EstateChecklistModel.hasMany(EstateTodoModel, { foreignKey: 'checklistID', as: 'todos' });
};
