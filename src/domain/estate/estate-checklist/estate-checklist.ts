import type { Resource } from '../../../framework/sequelize/resource';

export enum EstateChecklistID {
  CONTRACT_SIGNING = 'contractSigning',
  INFORMATION_GATHERING = 'informationGathering',
  PHOTOGRAPHER = 'photographer',
  APPRAISER = 'appraiser',
  VIEWING = 'viewing',
  HANDOVER = 'handover',
}

export enum EstateBuyerChecklistID {
  CONTRACT_SIGNING = 'buyerContracSigning',
  TAKEOVER = 'takeover',
  MOVE_IN = 'move-in',
}

export enum EstateChecklistType {
  SELLER = 'seller',
  BUYER = 'buyer',
}

export type EstateChecklist = Resource & {
  checklistID: EstateChecklistID;
  title: string;
  type: EstateChecklistType;
  logName: string;
  sortOrder: number;
};
