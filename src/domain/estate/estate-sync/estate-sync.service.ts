import { isEqual } from 'lodash';
import { logger } from '../../../logger';
import type { User } from '../../user/user';
import type { ValuationService } from '../../valuation/ValuationService';
import type { EstatePG } from '../estate';
import type { EstateService } from '../estate.service';
import type { EstateSyncResult } from './estate-sync';

export type EstateSyncService = {
  syncUserEstates(user: User, ssn: string): Promise<EstateSyncResult>;
};

export const estateSyncServiceFactory = ({
  estateService,
  valuationService,
}: {
  estateService: EstateService;
  valuationService: ValuationService;
}): EstateSyncService => {
  const calculateEstimationOffset = async (
    priceSuggestion: number,
    estate: Partial<EstatePG>,
  ): Promise<number | null> => {
    if (!estate.landIdentificationMatrix) {
      return null;
    }
    const evEstateEvaluation = await valuationService.getEstateEvaluation(estate.landIdentificationMatrix, {
      organizationNumber: estate.OrganizationNumber || null,
      shareNumber: estate.ShareNumber || null,
    });
    if (!evEstateEvaluation) {
      return null;
    }
    return priceSuggestion - (evEstateEvaluation.valuation ?? 0);
  };

  // eslint-disable-next-line @typescript-eslint/naming-convention
  const getEvEstateString = (estate: { ShareNumber?: string | null; EVAddressID?: string }): string =>
    `EstateShareNumber:${estate.ShareNumber || ''}_AddressId:${estate.EVAddressID || ''}`;

  const areTwoEstatesMatching = (e1: Partial<EstatePG>, e2: Partial<EstatePG>): boolean =>
    isEqual(e1.landIdentificationMatrix, e2.landIdentificationMatrix) && e1.ShareNumber === e2.ShareNumber;

  const syncUserEstates = async (user: User, ssn: string): Promise<EstateSyncResult> => {
    const creatableEstates = await valuationService.getCreatableEstates(user.id, ssn);
    const estatesInEiendomsverdiForUser = creatableEstates.map((creatableEstate) =>
      creatableEstate.toEstateCreatePayload(user),
    );

    logger.info(
      `Estates in EV: ${estatesInEiendomsverdiForUser.map((estate) => getEvEstateString(estate)).join(', ')}`,
    );

    const estatesInOurDatabaseForUser = await estateService.getEiendomsverdiEstatesByUserID(user.id);

    logger.info(`Estates in PG: ${estatesInOurDatabaseForUser.map((estate) => getEvEstateString(estate)).join(', ')}`);

    const estatesToBeAdded = estatesInEiendomsverdiForUser.filter(
      (estateInEiendomsverdi) =>
        !estatesInOurDatabaseForUser.find((estateInOurDatabase) =>
          areTwoEstatesMatching(estateInOurDatabase, estateInEiendomsverdi),
        ),
    );

    logger.info(`Estates to be added: ${estatesToBeAdded.map((estate) => getEvEstateString(estate)).join(', ')}`);

    const estatesToBeRemoved = estatesInOurDatabaseForUser.filter(
      (estateInOurDatabase) =>
        !estatesInEiendomsverdiForUser.find((estateInEiendomsverdi) =>
          areTwoEstatesMatching(estateInEiendomsverdi, estateInOurDatabase),
        ),
    );

    logger.info(`Estates to be removed: ${estatesToBeRemoved.map((estate) => getEvEstateString(estate)).join(', ')}`);

    // update estimation offset for estates that are already in our database
    const estatesToUpdate = estatesInOurDatabaseForUser.filter((estateInOurDatabase) =>
      estatesInEiendomsverdiForUser.find((estateInEiendomsverdi) =>
        areTwoEstatesMatching(estateInOurDatabase, estateInEiendomsverdi),
      ),
    );

    await Promise.all([
      addEstates(estatesToBeAdded),
      ...estatesToBeRemoved.map(
        async (estate) => void estateService.updateEiendomsverdiEstateByID(estate.id, user.id, { isArchived: true }),
      ),
      checkOffsetForEstates(estatesToUpdate, user.id),
    ]);

    return {
      numberOfAddedEstates: estatesToBeAdded.length,
      numberOfRemovedEstates: estatesToBeRemoved.length,
    };
  };

  async function checkOffsetForEstates(estates: EstatePG[], userId: string): Promise<void> {
    await Promise.all(
      estates.map(async (estate) => {
        const vitecEstate = await estateService.getActiveEstateFromVitecByLandIDMatrix(
          estate.landIdentificationMatrix,
          estate.ShareNumber,
        );

        if (!vitecEstate?.estatePrice) {
          if (estate.estimationOffset !== null) {
            logger.info(`Updating offset for estate ${getEvEstateString(estate)} to null`);
            await estateService.updateEiendomsverdiEstateByID(estate.id, userId, { estimationOffset: null });
          }
          return;
        }

        const offset = await calculateEstimationOffset(vitecEstate.estatePrice.priceSuggestion, estate);
        if (offset !== estate.estimationOffset) {
          logger.info(`Updating offset for estate ${getEvEstateString(estate)} to ${offset ?? 'null'}`);
          await estateService.updateEiendomsverdiEstateByID(estate.id, userId, { estimationOffset: offset });
        }
      }),
    );
  }

  async function addEstates(estatesToBeAdded: Partial<EstatePG>[]): Promise<void> {
    if (estatesToBeAdded.length === 0) {
      return;
    }
    const updatedEstatesToBeAddedWithOffset = await Promise.all(
      estatesToBeAdded.map(async (estate) => {
        if (!estate.landIdentificationMatrix) {
          return estate;
        }

        const vitecEstate = await estateService.getActiveEstateFromVitecByLandIDMatrix(
          estate.landIdentificationMatrix,
          estate.ShareNumber,
        );

        if (!vitecEstate || !vitecEstate.estatePrice) {
          return estate;
        }

        const offset = await calculateEstimationOffset(vitecEstate.estatePrice.priceSuggestion, estate);
        return {
          ...estate,
          estimationOffset: offset,
        };
      }),
    );
    await estateService.createEstates(updatedEstatesToBeAddedWithOffset);
  }

  return {
    syncUserEstates,
  };
};
