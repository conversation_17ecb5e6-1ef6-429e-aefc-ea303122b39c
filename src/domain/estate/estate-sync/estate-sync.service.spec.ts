import { estatePGFixtureFactory } from '../../../framework/test/fixtures/entities/estatePGFixtureFactory';
import { userFixtureFactory } from '../../../framework/test/fixtures/entities/userFixtureFactory';
import { estateServiceFixtureFactory } from '../../../framework/test/fixtures/services/estateServiceFixtureFactory';
import { valuationServiceFixtureFactory } from '../../../framework/test/fixtures/services/valuationServiceFixtureFactory';
import type { User } from '../../user/user';
import type { ValuationService } from '../../valuation/ValuationService';
import type { EstatePG } from '../estate';
import type { EstateService } from '../estate.service';
import type { EstateSyncService } from './estate-sync.service';
import { estateSyncServiceFactory } from './estate-sync.service';

describe('Estate sync service', () => {
  let valuationService: ValuationService;
  let estateService: EstateService;
  let estateSyncService: EstateSyncService;
  let user: User;

  beforeEach(() => {
    user = userFixtureFactory();
    estateService = estateServiceFixtureFactory();
    valuationService = valuationServiceFixtureFactory();
    estateSyncService = estateSyncServiceFactory({
      estateService,
      valuationService,
    });
  });

  describe('given a user with no estates in the database', () => {
    beforeEach(() => {
      estateService.getEiendomsverdiEstatesByUserID = jest.fn().mockResolvedValueOnce([]);
      estateService.getActiveEstateFromVitecByLandIDMatrix = jest
        .fn()
        .mockResolvedValueOnce({ estatePrice: { priceSuggestion: null } });
    });

    describe('given no new estates in Eiendomsverdi', () => {
      beforeEach(async () => {
        valuationService.getCreatableEstates = jest.fn().mockResolvedValueOnce([]);

        await estateSyncService.syncUserEstates(user, '[fake ssn]');
      });

      it('should not remove any estates from the database', () => {
        expect(estateService.updateEiendomsverdiEstateByID).not.toHaveBeenCalled();
      });

      it('should not add any estates to the database', () => {
        expect(estateService.createEstates).not.toHaveBeenCalled();
      });
    });

    describe('given a new estate in Eiendomsverdi', () => {
      let estate: EstatePG;

      beforeEach(async () => {
        estate = estatePGFixtureFactory(user);

        valuationService.getCreatableEstates = jest.fn().mockResolvedValueOnce([
          {
            toEstateCreatePayload: jest.fn().mockReturnValueOnce(estate),
          },
        ]);

        await estateSyncService.syncUserEstates(user, '[fake ssn]');
      });

      it('should not remove any estates from the database', () => {
        expect(estateService.updateEiendomsverdiEstateByID).not.toHaveBeenCalled();
      });

      it('should add the estate to the database', () => {
        expect(estateService.createEstates).toHaveBeenCalled();
        expect(estateService.createEstates).toHaveBeenCalledTimes(1);
        expect(estateService.createEstates).toHaveBeenCalledWith([estate]);
      });
    });
  });

  describe('given a user with an estate in the database', () => {
    let estate: EstatePG;

    beforeEach(() => {
      estate = estatePGFixtureFactory(user);
      estateService.getEiendomsverdiEstatesByUserID = jest.fn().mockResolvedValueOnce([estate]);
    });

    describe('given no estates in Eiendomsverdi', () => {
      beforeEach(async () => {
        valuationService.getCreatableEstates = jest.fn().mockResolvedValueOnce([]);

        await estateSyncService.syncUserEstates(user, '[fake ssn]');
      });

      it('should remove the estate from the database', () => {
        expect(estateService.updateEiendomsverdiEstateByID).toHaveBeenCalled();
        expect(estateService.updateEiendomsverdiEstateByID).toHaveBeenCalledTimes(1);
        expect(estateService.updateEiendomsverdiEstateByID).toHaveBeenCalledWith(estate.id, user.id, {
          isArchived: true,
        });
      });
    });

    describe('given an estate in Eiendomsverdi with same matrix and sharenumbers', () => {
      beforeEach(async () => {
        valuationService.getCreatableEstates = jest.fn().mockResolvedValueOnce([
          {
            toEstateCreatePayload: jest.fn().mockReturnValueOnce(estate),
          },
        ]);

        await estateSyncService.syncUserEstates(user, '[fake ssn]');
      });

      it('should not remove any estates from the database', () => {
        expect(estateService.updateEiendomsverdiEstateByID).not.toHaveBeenCalled();
      });

      it('should not add any estates to the database', () => {
        expect(estateService.createEstates).not.toHaveBeenCalled();
      });
    });

    describe('given an estate in Eiendomsverdi with same matrix BUT different sharenumbers', () => {
      let newEstate: EstatePG;

      beforeEach(async () => {
        newEstate = {
          ...estate,
          id: '[another fake id]',
          ShareNumber: '11',
        };

        valuationService.getCreatableEstates = jest.fn().mockResolvedValueOnce([
          {
            toEstateCreatePayload: jest.fn().mockReturnValueOnce(newEstate),
          },
        ]);
        estateService.getActiveEstateFromVitecByLandIDMatrix = jest
          .fn()
          .mockResolvedValueOnce({ estatePrice: { priceSuggestion: null } });

        await estateSyncService.syncUserEstates(user, '[fake ssn]');
      });

      it('should set the existing estate archived in the database', () => {
        expect(estateService.updateEiendomsverdiEstateByID).toHaveBeenCalled();
        expect(estateService.updateEiendomsverdiEstateByID).toHaveBeenCalledTimes(1);
        expect(estateService.updateEiendomsverdiEstateByID).toHaveBeenCalledWith(estate.id, user.id, {
          isArchived: true,
        });
      });

      it('should add the new estate to the database', () => {
        expect(estateService.createEstates).toHaveBeenCalled();
        expect(estateService.createEstates).toHaveBeenCalledTimes(1);
        expect(estateService.createEstates).toHaveBeenCalledWith([newEstate]);
      });
    });

    describe('given two estates in Eiendomsverdi, one with same matrix and sharenumbers, one with same matrix BUT different sharenumbers', () => {
      let newEstate: EstatePG;

      beforeEach(async () => {
        newEstate = {
          ...estate,
          id: '[another fake id]',
          ShareNumber: '11',
        };

        valuationService.getCreatableEstates = jest.fn().mockResolvedValueOnce([
          {
            toEstateCreatePayload: jest.fn().mockReturnValueOnce(estate),
          },
          {
            toEstateCreatePayload: jest.fn().mockReturnValueOnce(newEstate),
          },
        ]);
        estateService.getActiveEstateFromVitecByLandIDMatrix = jest
          .fn()
          .mockResolvedValueOnce({ estatePrice: { priceSuggestion: null } });

        await estateSyncService.syncUserEstates(user, '[fake ssn]');
      });

      it('should not set existing estate archived in the database', () => {
        expect(estateService.updateEiendomsverdiEstateByID).not.toHaveBeenCalled();
      });

      it('should add the new estate to the database', () => {
        expect(estateService.createEstates).toHaveBeenCalled();
        expect(estateService.createEstates).toHaveBeenCalledTimes(1);
        expect(estateService.createEstates).toHaveBeenCalledWith([newEstate]);
      });
    });
  });
});
