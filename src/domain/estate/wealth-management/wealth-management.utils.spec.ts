import { estateLatestSaleFixtureFactory } from '../../../framework/test/fixtures/misc/latestEstateSaleFixtureFactory';
import {
  getDocumentCost,
  getEquityPurchase,
  getEquityRatio,
  getInterestPayment,
  getMonthlyInterestRate,
  getPartnerBankInterestRate,
} from './wealth-management.utils';

describe('wealth management utils', () => {
  describe('#getMonthlyInterestRate', () => {
    const monthlyInterestRate = 0.05;
    const monthsPassedSincePurchase = 16;
    const expectedValue = 0.068;

    it('should return null if any input is null', () => {
      expect(getMonthlyInterestRate(null, 0)).toBeNull();
    });
    it('should return the correct value', () => {
      expect(getMonthlyInterestRate(monthlyInterestRate, monthsPassedSincePurchase)).toBeCloseTo(expectedValue, 2);
    });
  });

  describe('#getEquityPurchase', () => {
    const latestSaleTest = estateLatestSaleFixtureFactory(new Date(0), 300);
    const originalLoan = 200;
    const expectedValue = 100;

    it('should return null if any input is undefined', () => {
      expect(getEquityPurchase(null, null)).toBeNull();
      expect(getEquityPurchase(latestSaleTest, null)).toBeNull();
      expect(getEquityPurchase(null, originalLoan)).toBeNull();
    });
    it('should return the correct value', () => {
      expect(getEquityPurchase(latestSaleTest, originalLoan)).toBeCloseTo(expectedValue, 2);
    });
  });

  describe('#getDocumentCost', () => {
    const expectedValue = 2.25;
    const latestSaleTest = estateLatestSaleFixtureFactory(new Date(0), 100);
    let isOwnProperty: boolean;

    it('should return the expected value if isOwnProperty is true', () => {
      isOwnProperty = true;
      expect(getDocumentCost(isOwnProperty, latestSaleTest)).toBeCloseTo(expectedValue, 2);
    });
    it('should return 0 if isOwnProperty is false', () => {
      isOwnProperty = false;
      expect(getDocumentCost(isOwnProperty, latestSaleTest)).toBe(0);
    });

    it('should return null if any input is undefined', () => {
      expect(getDocumentCost(isOwnProperty, null)).toBeNull();
    });
  });

  describe('#getInterestPayment', () => {
    const originalLoan = 100;
    const monthlyInterestRate = 0.1;
    const expectedValue = 10;

    it('should return null if any input is undefined', () => {
      expect(getInterestPayment(null, null)).toBeNull();
      expect(getInterestPayment(originalLoan, null)).toBeNull();
      expect(getInterestPayment(null, monthlyInterestRate)).toBeNull();
    });
    it('should return the correct value', () => {
      expect(getInterestPayment(originalLoan, monthlyInterestRate)).toBeCloseTo(expectedValue, 2);
    });
  });

  describe('#getEquityRatio', () => {
    describe('given no valuation', () => {
      it('should return null', () => {
        expect(getEquityRatio(null, { date: new Date(0), value: 4100000 }, 1000)).toBeNull();
      });
    });
    describe('given no latest sale value', () => {
      it('should return null', () => {
        expect(getEquityRatio(null, { date: new Date(0), value: null }, 1000)).toBeNull();
      });
    });

    test('acceptance', () => {
      expect(getEquityRatio(4200000, { date: new Date(0), value: 4100000 }, 1200000)).toBeCloseTo(0.73, 2);
      expect(getEquityRatio(7690000, { date: new Date(0), value: 7400000 }, 3000000)).toBeCloseTo(0.63, 2);
      expect(getEquityRatio(7690000, { date: new Date(0), value: 7400000 }, 4000000)).toBeCloseTo(0.5, 2);
      expect(getEquityRatio(7690000, { date: new Date(0), value: 7400000 }, 5000000)).toBeCloseTo(0.36, 2);
    });
  });

  describe('#getInterestPayment', () => {
    describe('given no valuation', () => {
      it('should return null', () => {
        expect(getInterestPayment(null, 0.015)).toBeNull();
      });
    });
  });

  describe('#getPartnerBankInterestRate', () => {
    describe('acceptances', () => {
      expect(getPartnerBankInterestRate(0.1961, 3.97e6)).toEqual(0.0249);
      expect(getPartnerBankInterestRate(0.625, 4e6)).toEqual(0.0159);
    });

    describe('given an equity ratio below 15%', () => {
      it('should return null regardless of the loan on the property', () => {
        expect(getPartnerBankInterestRate(0.149, 0)).toBeNull();
        expect(getPartnerBankInterestRate(0.0, 0)).toBeNull();
      });
    });

    describe('given an equity ratio between 15% (inclusive) and 30% (exclusive)', () => {
      it('should return a constant interest rate regardless of the loan on the property', () => {
        expect(getPartnerBankInterestRate(0.15, 0)).toEqual(0.0249);
        expect(getPartnerBankInterestRate(0.29, 0)).toEqual(0.0249);
      });
    });

    describe('given an equity ratio above 30%', () => {
      const equityRatio = 0.31;

      describe('given a loan on the property (>4M)', () => {
        const loanOnTheProperty = 4e6;

        it('should return the correct interest rate', () => {
          expect(getPartnerBankInterestRate(equityRatio, loanOnTheProperty)).toEqual(0.0159);
        });
      });

      describe('given a loan on the property (4M-2M)', () => {
        const loanOnTheProperty = 3e6;

        it('should return the correct interest rate', () => {
          expect(getPartnerBankInterestRate(equityRatio, loanOnTheProperty)).toEqual(0.0179);
        });
      });

      describe('given a loan on the property (2M-1M)', () => {
        const loanOnTheProperty = 1.5e6;

        it('should return the correct interest rate', () => {
          expect(getPartnerBankInterestRate(equityRatio, loanOnTheProperty)).toEqual(0.0209);
        });
      });

      describe('given a loan on the property (<1M)', () => {
        const loanOnTheProperty = 5e5;

        it('should return the correct interest rate', () => {
          expect(getPartnerBankInterestRate(equityRatio, loanOnTheProperty)).toEqual(0.0249);
        });
      });
    });
  });
});
