import type { EstateSalesHistory } from '../../valuation/EstateSalesHistoryEntry';
import { utils } from './wealth-management.utils';

export type LatestSaleParsed = {
  date: Date | null;
  value: number | null;
};
type GetValuationComponentsInput = {
  valuation: number | null;
  currentMortgage: number | null;
};
type GetExtendMortgageInput = {
  valuation: number | null;
  totalIncome: number | null;
  otherDebt: number | null;
  livingArea: number;
  loanYears: number | null;
  originalLoan: number | null;
  interestRate: number | null;
  purchaseDate: Date | null;
  now?: Date;
};
type GetSavingsBasedOnInterestRateDifferenceInput = {
  valuation: number | null;
  latestSale: LatestSaleParsed;
  interestRate: number | null;
  loanYears: number | null;
  purchaseDate: Date | null;
  originalLoan: number | null;
  now?: Date;
};
type GetEquityGrowthInput = {
  valuation: number | null;
  latestSale: LatestSaleParsed;
  originalLoan: number | null;
  isOwnProperty: boolean;
  interestRate: number | null;
  now?: Date;
  loanYears: number | null;
  purchaseDate: Date | null;
};
type GetValueIncreaseInput = {
  valuation: number | null;
  latestSale: LatestSaleParsed;
};
type ValuationComponents = {
  mortgage: {
    percentage: number;
    value: number;
  };
  equity: {
    percentage: number;
    value: number;
  };
};
type CombinedInput = Omit<
  GetExtendMortgageInput &
    GetSavingsBasedOnInterestRateDifferenceInput &
    GetEquityGrowthInput &
    GetValueIncreaseInput & { salesHistory: EstateSalesHistory },
  'latestSale' | 'purchaseDate'
>;
export type CombinedOutput = {
  valueIncrease: {
    increasePercentage: number | null;
    increaseValue: number | null;
  };
  savingsBasedOnInterestRateDifference: number | null;
  moreExpensiveProperty: number | null;
  extendedMortgage: number | null;
  equityGrowth: {
    increasePercentage: number | null;
    increaseValue: number | null;
  };
  affordMoreSpace: number | null;
  currentMortgage: number | null;
  purchasePrice: number | null;
  valuation: number | null;
  valuationComponents: ValuationComponents | null;
};

export type WealthManagementService = {
  getLatestSale: (estateSalesHistory: EstateSalesHistory) => LatestSaleParsed;
  getLatestSaleDate: (estateSalesHistory: EstateSalesHistory) => Date | null;
  getValueIncrease: (i: {
    latestSale: LatestSaleParsed;
    valuation: number | null;
  }) => { increasePercentage: number | null; increaseValue: number | null };
  getSavingsBasedOnInterestRateDifference: (i: GetSavingsBasedOnInterestRateDifferenceInput) => number | null;
  getMoreExpensiveProperty: (i: GetExtendMortgageInput) => number | null;
  getExtendedMortgage: (i: GetExtendMortgageInput) => number | null;
  getEquityGrowth: (i: GetEquityGrowthInput) => { increasePercentage: number | null; increaseValue: number | null };
  getAffordMoreSpace: (i: GetExtendMortgageInput) => number | null;
  getValuationComponents: (i: GetValuationComponentsInput) => ValuationComponents | null;
  getCombinedCalculations: (i: CombinedInput) => CombinedOutput;
};

export const wealthManagementServiceFactory = (): WealthManagementService => {
  const getLatestSaleDate = (estateSalesHistory: EstateSalesHistory): Date | null => {
    const latestSale = estateSalesHistory
      .map(({ value, date }) => ({ date: date ? new Date(date) : null, value }))
      .sort(utils.sortByDate)
      .reverse()[0];
    if (!latestSale) {
      return null;
    }
    return latestSale.date;
  };
  const getLatestSale = (estateSalesHistory: EstateSalesHistory): LatestSaleParsed => {
    const latestSale = estateSalesHistory
      .map(({ value, date }) => ({ date: date ? new Date(date) : null, value }))
      .sort(utils.sortByDate)
      .reverse()[0];

    if (latestSale) {
      const value = parseInt(latestSale.value, 10);

      return {
        date: latestSale.date,
        value: isNaN(value) ? null : value,
      };
    }

    return {
      value: null,
      date: null,
    };
  };

  const getValueIncrease = (
    i: GetValueIncreaseInput,
  ): { increasePercentage: number | null; increaseValue: number | null } => {
    if (i.latestSale.value !== null && i.valuation !== null) {
      const increaseValue = utils.roundToNearestThousand(i.valuation - i.latestSale.value);
      const increasePercentage = increaseValue / utils.roundToNearestThousand(Math.abs(i.latestSale.value));

      return {
        increasePercentage,
        increaseValue,
      };
    }

    return {
      increasePercentage: null,
      increaseValue: null,
    };
  };

  const getSavingsBasedOnInterestRateDifference = (i: GetSavingsBasedOnInterestRateDifferenceInput): number | null => {
    if (i.latestSale.value === null || i.valuation === null || i.interestRate === null) {
      return null;
    }
    const currentLoan = utils.getCurrentMortgage(
      i.purchaseDate,
      i.now || new Date(),
      i.originalLoan,
      i.interestRate,
      i.loanYears,
    );
    if (!currentLoan) {
      return null;
    }
    const equityRatio = utils.getEquityRatio(i.valuation, i.latestSale, currentLoan);

    if (equityRatio === null) {
      return null;
    }

    const maxEquityShare = 0.3;

    const mainMortgageGiven = Math.max(Math.min(i.valuation - i.latestSale.value * maxEquityShare, currentLoan), 0);
    const mainMortgageInterestRate = utils.getPartnerBankInterestRate(equityRatio, mainMortgageGiven);
    const savingsFromMainMortgage = mainMortgageInterestRate
      ? mainMortgageGiven * (i.interestRate - mainMortgageInterestRate)
      : 0;

    const additionalMortgageGiven = Math.max(currentLoan - mainMortgageGiven, 0);
    const additionalMortgageInterestRate = utils.getPartnerBankInterestRate(equityRatio, additionalMortgageGiven);
    const savingsFromAdditionalMortgage = additionalMortgageInterestRate
      ? additionalMortgageGiven * (i.interestRate - additionalMortgageInterestRate)
      : 0;

    return utils.roundToNearestThousand(savingsFromMainMortgage + savingsFromAdditionalMortgage);
  };

  const getExtendedMortgage = (i: GetExtendMortgageInput): number | null => {
    if (i.valuation === null || i.totalIncome === null || i.otherDebt === null) {
      return null;
    }
    const currentMorgage = utils.getCurrentMortgage(
      i.purchaseDate,
      i.now || new Date(),
      i.originalLoan,
      i.interestRate,
      i.loanYears,
    );
    if (!currentMorgage) {
      return null;
    }
    const equityNow = i.valuation - currentMorgage;

    if (equityNow > i.valuation * 0.15) {
      return Math.max(0, utils.roundToNearestThousand(5 * i.totalIncome - currentMorgage - i.otherDebt));
    } else {
      return 0;
    }
  };

  const getMoreExpensiveProperty = (i: GetExtendMortgageInput): number | null => {
    if (i.valuation !== null) {
      const extendedMortgage = getExtendedMortgage(i);

      if (extendedMortgage !== null) {
        return utils.roundToNearestThousand(i.valuation + extendedMortgage);
      }
    }

    return null;
  };

  const getAffordMoreSpace = (i: GetExtendMortgageInput): number | null => {
    if (i.valuation !== null) {
      const extendedMortgage = getExtendedMortgage(i);

      if (extendedMortgage !== null) {
        return Math.round(extendedMortgage / (i.valuation / i.livingArea));
      }
    }

    return null;
  };

  const getEquityGrowth = (
    i: GetEquityGrowthInput,
  ): { increasePercentage: number | null; increaseValue: number | null } => {
    if (i.latestSale.date !== null && i.originalLoan !== null && i.valuation !== null) {
      const equityPurchase = utils.getEquityPurchase(i.latestSale, i.originalLoan);
      const currentMorgage = utils.getCurrentMortgage(
        i.purchaseDate,
        i.now || new Date(),
        i.originalLoan,
        i.interestRate,
        i.loanYears,
      );

      if (equityPurchase !== null && currentMorgage !== null) {
        const increasedValue = i.valuation - currentMorgage;

        return {
          increasePercentage: increasedValue / equityPurchase - 1,
          increaseValue: increasedValue - equityPurchase,
        };
      }
    }

    return {
      increasePercentage: null,
      increaseValue: null,
    };
  };

  const getValuationComponents = (i: GetValuationComponentsInput): ValuationComponents | null => {
    if (i.currentMortgage === null || i.valuation === null) {
      return null;
    }

    const roundedCurrentMortgage = utils.roundToNearestThousand(i.currentMortgage);
    const roundedValuation = utils.roundToNearestThousand(i.valuation);

    return {
      equity: {
        value: roundedValuation - roundedCurrentMortgage,
        percentage: (roundedValuation - roundedCurrentMortgage) / roundedValuation,
      },
      mortgage: {
        value: roundedCurrentMortgage,
        percentage: roundedCurrentMortgage / roundedValuation,
      },
    };
  };

  const getCombinedCalculations = (i: CombinedInput): CombinedOutput => {
    const latestSale = getLatestSale(i.salesHistory);
    const purchaseDate = getLatestSaleDate(i.salesHistory);
    const currentMortgage = utils.getCurrentMortgage(
      purchaseDate,
      i.now || new Date(),
      i.originalLoan,
      i.interestRate,
      i.loanYears,
    );
    const input = { ...i, latestSale, purchaseDate, currentMortgage };
    const valueIncrease = getValueIncrease(input);
    const savingsBasedOnInterestRateDifference = getSavingsBasedOnInterestRateDifference(input);
    const moreExpensiveProperty = getMoreExpensiveProperty(input);
    const extendedMortgage = getExtendedMortgage(input);
    const equityGrowth = getEquityGrowth(input);
    const affordMoreSpace = getAffordMoreSpace(input);
    const valuationComponents = getValuationComponents(input);

    return {
      valueIncrease,
      savingsBasedOnInterestRateDifference,
      moreExpensiveProperty,
      extendedMortgage,
      equityGrowth,
      affordMoreSpace,
      currentMortgage,
      purchasePrice: latestSale.value ?? null,
      valuation: i.valuation !== null ? utils.roundToNearestThousand(i.valuation) : null,
      valuationComponents,
    };
  };

  return {
    getLatestSale,
    getValueIncrease,
    getSavingsBasedOnInterestRateDifference,
    getMoreExpensiveProperty,
    getEquityGrowth,
    getExtendedMortgage,
    getAffordMoreSpace,
    getCombinedCalculations,
    getLatestSaleDate,
    getValuationComponents,
  };
};
