import { addDays, addYears } from 'date-fns';
import { estateSalesHistoryFixtureFactory } from '../../../framework/test/fixtures/misc/estateSalesHistoryFixtureFactory';
import { estateLatestSaleFixtureFactory } from '../../../framework/test/fixtures/misc/latestEstateSaleFixtureFactory';
import type { CombinedOutput, WealthManagementService } from './wealth-management.service';
import { wealthManagementServiceFactory } from './wealth-management.service';

describe('wealth management service', () => {
  let wealthManagementService: WealthManagementService;

  beforeEach(() => {
    wealthManagementService = wealthManagementServiceFactory();
  });

  describe('#getLatestSale', () => {
    describe('given no entries in the sales history', () => {
      it('should return null values', () => {
        expect(wealthManagementService.getLatestSale([])).toEqual({ value: null, date: null });
      });
    });

    describe('given an entry in the sales history without a date', () => {
      it('should return a null date', () => {
        expect(
          wealthManagementService.getLatestSale(estateSalesHistoryFixtureFactory((null as unknown) as Date, '0')),
        ).toEqual({
          value: 0,
          date: null,
        });
      });
    });

    describe('given an entry in the sales history with a malformed value', () => {
      it('should return a null value', () => {
        expect(
          wealthManagementService.getLatestSale(estateSalesHistoryFixtureFactory(new Date(0), '[fake value]')),
        ).toEqual({
          value: null,
          date: new Date(0),
        });
      });
    });

    describe('given some entries in the sales history', () => {
      const estateSalesHistory = [
        ...estateSalesHistoryFixtureFactory(addDays(new Date('2020.07.15'), -3), '300'),
        ...estateSalesHistoryFixtureFactory(addDays(new Date('2020.07.15'), -2), '200'),
        ...estateSalesHistoryFixtureFactory(addDays(new Date('2020.07.15'), -1), '100'),
      ];

      it('should return the latest sale price of the given estate', () => {
        expect(wealthManagementService.getLatestSale(estateSalesHistory)).toMatchObject({
          value: 100,
        });
      });
    });
  });

  describe('#getValueIncrease', () => {
    describe('given an increase', () => {
      it('should return the proper change', () => {
        const change = wealthManagementService.getValueIncrease({
          valuation: 120,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 100),
        });

        expect(change.increaseValue).toEqual(20);
        expect(change.increasePercentage).toEqual(0.2);
      });
    });

    describe('given a decrease', () => {
      it('should return the proper change', () => {
        const change = wealthManagementService.getValueIncrease({
          valuation: 100,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 120),
        });

        expect(change.increaseValue).toEqual(-20);
        expect(change.increasePercentage).toBeCloseTo(-0.1667, 2);
      });
    });

    describe('given a null valuation', () => {
      it('should return null', () => {
        const change = wealthManagementService.getValueIncrease({
          valuation: null,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 0),
        });

        expect(change.increaseValue).toBeNull();
        expect(change.increasePercentage).toBeNull();
      });
    });

    describe('given a null value in the last sales history entry', () => {
      it('should return null', () => {
        const change = wealthManagementService.getValueIncrease({
          valuation: 0,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), (null as unknown) as number),
        });

        expect(change.increaseValue).toBeNull();
        expect(change.increasePercentage).toBeNull();
      });
    });
  });

  describe('#getSavingsBasedOnInterestRateDifference', () => {
    describe('given no interest rate', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getSavingsBasedOnInterestRateDifference({
            interestRate: null,
            latestSale: estateLatestSaleFixtureFactory(),
            valuation: 1500,
            loanYears: 10,
            purchaseDate: new Date('2020.07.15'),
            originalLoan: 1000,
          }),
        ).toBeNull();
      });
    });

    describe('given no current loan', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getSavingsBasedOnInterestRateDifference({
            interestRate: 0.013,
            latestSale: estateLatestSaleFixtureFactory(),
            valuation: 1500,
            loanYears: 10,
            purchaseDate: new Date('2020.07.15'),
            originalLoan: 1000,
          }),
        ).toBeNull();
      });
    });

    describe('given no valuation', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getSavingsBasedOnInterestRateDifference({
            interestRate: 0.013,
            latestSale: estateLatestSaleFixtureFactory(),
            valuation: null,
            loanYears: 10,
            purchaseDate: new Date('2020.07.15'),
            originalLoan: 1000,
          }),
        ).toBeNull();
      });
    });

    test('acceptance', () => {
      expect(
        wealthManagementService.getSavingsBasedOnInterestRateDifference({
          valuation: 7690000,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 7400000),
          interestRate: 0.018,
          loanYears: 10,
          purchaseDate: new Date('2020.07.15'),
          originalLoan: 1000,
        }),
      ).toBeCloseTo(300, 1);

      expect(
        wealthManagementService.getSavingsBasedOnInterestRateDifference({
          valuation: 7690000,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 7400000),
          interestRate: 0.03,
          loanYears: 10,
          purchaseDate: new Date('2020.07.15'),
          originalLoan: 1000,
        }),
      ).toBeCloseTo(56400, 1);

      expect(
        wealthManagementService.getSavingsBasedOnInterestRateDifference({
          valuation: 7690000,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 7400000),
          interestRate: 0.02,
          loanYears: 10,
          purchaseDate: new Date('2020.07.15'),
          originalLoan: 1000,
        }),
      ).toBeCloseTo(20500, 1);

      expect(
        wealthManagementService.getSavingsBasedOnInterestRateDifference({
          valuation: 5.5e6,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 5.1e6),
          interestRate: 0.02,
          loanYears: 10,
          purchaseDate: new Date('2020.07.15'),
          originalLoan: 1000,
        }),
      ).toBeCloseTo(-22050, 1);

      expect(
        wealthManagementService.getSavingsBasedOnInterestRateDifference({
          valuation: 10e6,
          latestSale: estateLatestSaleFixtureFactory(new Date(0), 8e6),
          interestRate: 0.02,
          loanYears: 10,
          purchaseDate: new Date('2020.07.15'),
          originalLoan: 1000,
        }),
      ).toBeCloseTo(16400, 1);
    });
  });

  describe('#getExtendedMortgage', () => {
    describe('given no valuation', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getExtendedMortgage({
            otherDebt: 200,
            livingArea: 40,
            totalIncome: 1000,
            loanYears: 10,
            purchaseDate: new Date('2020.07.15'),
            originalLoan: 1000,
            valuation: null,
            interestRate: 0.1,
          }),
        ).toBeNull();
      });
    });

    describe('given a too big current loan', () => {
      it('should return 0', () => {
        expect(
          wealthManagementService.getExtendedMortgage({
            otherDebt: 200,
            livingArea: 40,
            totalIncome: 1000,
            valuation: 100,
            loanYears: 10,
            purchaseDate: new Date('2020.07.15'),
            originalLoan: 1000,
            interestRate: 0.1,
          }),
        ).toEqual(0);
      });
    });
  });

  describe('#getMoreExpensiveProperty', () => {
    describe('given no valuation', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getMoreExpensiveProperty({
            otherDebt: 200,
            livingArea: 40,
            totalIncome: 1000,
            valuation: null,
            loanYears: 10,
            purchaseDate: new Date('2020.07.15'),
            originalLoan: 1000,
            interestRate: 0.1,
          }),
        ).toBeNull();
      });
    });
  });

  describe('#getAffordMoreSpace', () => {
    describe('given no valuation', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getAffordMoreSpace({
            otherDebt: 200,
            livingArea: 40,
            totalIncome: 1000,
            valuation: null,
            loanYears: 10,
            purchaseDate: new Date('2020.07.15'),
            originalLoan: 1000,
            interestRate: 0.1,
          }),
        ).toBeNull();
      });
    });
  });

  describe('#getEquityGrowth', () => {
    describe('given no valuation', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getEquityGrowth({
            interestRate: 0.013,
            isOwnProperty: true,
            latestSale: estateLatestSaleFixtureFactory(new Date(0), 0),
            originalLoan: 1000,
            valuation: null,
            loanYears: 25,
            purchaseDate: new Date(),
          }),
        ).toMatchObject({
          increasePercentage: null,
          increaseValue: null,
        });
      });
    });

    describe('given no latest sale date', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getEquityGrowth({
            interestRate: 0.013,
            isOwnProperty: true,
            latestSale: estateLatestSaleFixtureFactory((null as unknown) as Date, 0),
            originalLoan: 1000,
            valuation: 800,
            loanYears: 25,
            purchaseDate: new Date(),
          }),
        ).toMatchObject({
          increasePercentage: null,
          increaseValue: null,
        });
      });
    });

    describe('given no latest sale value', () => {
      it('should return null', () => {
        expect(
          wealthManagementService.getEquityGrowth({
            interestRate: 0.013,
            isOwnProperty: true,
            latestSale: estateLatestSaleFixtureFactory(new Date(0), (null as unknown) as number),
            originalLoan: 1000,
            valuation: 800,
            loanYears: 25,
            purchaseDate: new Date(),
          }),
        ).toMatchObject({
          increasePercentage: null,
          increaseValue: null,
        });
      });
    });
  });

  describe('#getCombinedCalculations', () => {
    describe('acceptance (1st example)', () => {
      let calculations: CombinedOutput;

      beforeEach(() => {
        calculations = wealthManagementService.getCombinedCalculations({
          valuation: 6000000,
          salesHistory: estateSalesHistoryFixtureFactory(addYears(new Date('2020.07.15'), -5), '5100000'),
          originalLoan: 4000000,
          isOwnProperty: true,
          interestRate: 0.013,
          otherDebt: 200000,
          livingArea: 54,
          totalIncome: 800000,
          loanYears: 10,
        });
      });

      it('should return proper value increase (value)', () => {
        expect(calculations.valueIncrease.increaseValue).toEqual(900000);
      });

      it('should return proper value increase (percentage)', () => {
        expect(calculations.valueIncrease.increasePercentage).toBeCloseTo(0.1764, 2);
      });

      it('should return the proper extended mortgage', () => {
        expect(calculations.extendedMortgage).toEqual(1800000);
      });

      it('should return the proper more expensive property price', () => {
        expect(calculations.moreExpensiveProperty).toEqual(7800000);
      });

      it('should return proper equity growth (value)', () => {
        expect(calculations.equityGrowth.increaseValue).toBeCloseTo(640000, 1);
      });

      it('should return proper equity growth (percentage)', () => {
        expect(calculations.equityGrowth.increasePercentage).toBeCloseTo(0.52, 1);
      });

      it('should return proper afford more space value', () => {
        expect(calculations.affordMoreSpace).toBeCloseTo(16, 0);
      });

      it('should return proper savings based on interest rate difference value', () => {
        expect(calculations.savingsBasedOnInterestRateDifference).toEqual(-14700);
      });
    });
  });

  describe.only('acceptance (2nd example)', () => {
    let calculations: CombinedOutput;

    beforeEach(() => {
      calculations = wealthManagementService.getCombinedCalculations({
        valuation: 6000000,
        salesHistory: estateSalesHistoryFixtureFactory(new Date('2020.07.15'), '5100000'),
        originalLoan: 4800000,
        isOwnProperty: true,
        interestRate: 0.014,
        otherDebt: 200000,
        livingArea: 54,
        totalIncome: 1500000,
        loanYears: 25,
        now: new Date('2020.11.30'),
      });
    });

    it('should return proper value increase (value)', () => {
      expect(calculations.valueIncrease.increaseValue).toEqual(900000);
    });

    it('should return proper value increase (percentage)', () => {
      expect(calculations.valueIncrease.increasePercentage).toBeCloseTo(0.1764, 2);
    });

    it('should return the proper extended mortgage', () => {
      expect(calculations.extendedMortgage).toEqual(2553000);
    });

    it('should return the proper more expensive property price', () => {
      expect(calculations.moreExpensiveProperty).toEqual(8553000);
    });

    it('should return proper equity growth (value)', () => {
      expect(calculations.equityGrowth.increaseValue).toBeCloseTo(953488.95, 1);
    });

    it('should return proper equity growth (percentage)', () => {
      expect(calculations.equityGrowth.increasePercentage).toBeCloseTo(3.1783, 1);
    });

    it('should return proper afford more space value', () => {
      expect(calculations.affordMoreSpace).toBeCloseTo(23, 0);
    });

    it('should return proper savings based on interest rate difference value', () => {
      expect(calculations.savingsBasedOnInterestRateDifference).toEqual(-52000);
    });
  });
});
