import { differenceInMonths } from 'date-fns';
import type { LatestSaleParsed } from './wealth-management.service';

export const getMonthlyInterestRate = (
  interestRate: number | null,
  monthsPassedSincePurchase: number,
): number | null => {
  if (interestRate === null || !monthsPassedSincePurchase) {
    return null;
  }
  return (1 + interestRate / 12) ** monthsPassedSincePurchase - 1;
};

export const getEquityPurchase = (latestSale: LatestSaleParsed | null, originalLoan: number | null): number | null => {
  if (latestSale === null || latestSale.value === null || originalLoan === null) {
    return null;
  }
  return latestSale.value && latestSale.value - originalLoan;
};

export const getDocumentCost = (isOwnProperty: boolean, latestSale: LatestSaleParsed | null): number | null => {
  if (latestSale === null || latestSale.value === null) {
    return null;
  }
  if (isOwnProperty) {
    return latestSale.value && latestSale.value * 0.0225;
  }
  return 0;
};

export const getInterestPayment = (originalLoan: number | null, monthlyInterestRate: number | null): number | null => {
  if (!originalLoan || !monthlyInterestRate) {
    return null;
  }
  return originalLoan * monthlyInterestRate;
};

export const sortByDate = (
  a: {
    date: Date | null;
  },
  b: {
    date: Date | null;
  },
): number => {
  const useSecond = -1;
  const useFirst = 1;
  if (!a.date) {
    return useSecond;
  }
  if (!b.date) {
    return useFirst;
  }
  return a.date.getTime() - b.date.getTime();
};

export const getEquityRatio = (
  valuation: number | null,
  latestSale: LatestSaleParsed,
  loanOnTheProperty: number | null,
): number | null => {
  if (valuation === null || latestSale.value === null || loanOnTheProperty === null) {
    return null;
  }

  return (valuation - loanOnTheProperty) / latestSale.value;
};

export const getPartnerBankInterestRate = (
  equityRatio: number | null,
  loanOnTheProperty: number | null,
): number | null => {
  if (equityRatio === null || loanOnTheProperty === null) {
    return null;
  }

  if (equityRatio >= 0.3) {
    if (loanOnTheProperty >= 4e6) {
      return 0.0159;
    } else if (loanOnTheProperty >= 2e6) {
      return 0.0179;
    } else if (loanOnTheProperty >= 1e6) {
      return 0.0209;
    } else {
      return 0.0249;
    }
  } else if (equityRatio >= 0.15) {
    return 0.0249;
  }

  return null;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
function pmt(rate_per_period: number, number_of_payments: number, present_value: number): number {
  if (rate_per_period !== 0.0) {
    // Interest rate exists
    const q = Math.pow(1 + rate_per_period, number_of_payments);
    return (rate_per_period * (q * present_value)) / (-1 + q);
  } else if (number_of_payments !== 0.0) {
    // No interest rate, but number of payments exists
    return present_value / number_of_payments;
  }

  return 0;
}

export const getCurrentMortgage = (
  loanDate: Date | null,
  now: Date | null,
  originalLoan: number | null,
  p: number | null,
  loanYears: number | null,
): number | null => {
  if (loanDate === null || originalLoan === null || p === null || loanYears === null) {
    return null;
  }
  const noPayments = differenceInMonths(now || new Date(), loanDate);
  const monthlyp = getMonthlyInterestRate(p, 1);
  if (!monthlyp) {
    return null;
  }
  const monthlyPayment = pmt(monthlyp, loanYears * 12, originalLoan);
  const interestPayment = getInterestPayment(originalLoan, monthlyp);
  if (!interestPayment) {
    return null;
  }
  const monthlyPrincipalPayment = monthlyPayment - interestPayment;
  return originalLoan - noPayments * monthlyPrincipalPayment;
};

const roundToNearestThousand = (value: number): number => {
  return Math.round(value / 1000) * 1000;
};

export const utils = {
  getPartnerBankInterestRate,
  getEquityRatio,
  sortByDate,
  getInterestPayment,
  getDocumentCost,
  getEquityPurchase,
  getMonthlyInterestRate,
  getCurrentMortgage,
  roundToNearestThousand,
};
