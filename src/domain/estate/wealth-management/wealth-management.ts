export type WealthManagementSummaryEntry = {
  estateId?: string;
  streetAddress?: string;
  totalEstimatedValue: number | null;
  areFinancialDetailsFilled?: boolean;
  hasRecentBrokerValuation?: boolean;
  mortgage: {
    percentage: number | null;
    value: number | null;
  };
  equity: {
    percentage: number | null;
    value: number | null;
  };
  value: {
    initial: number | null;
    current: number | null;
    increasePercentage: number | null;
    increaseValue: number | null;
  };
  storebrandSaving: number | null;
  extendedMortgage: number | null;
  moreExpensiveProperty: number | null;
  affordMoreSpace: number | null;
};

export type WealthManagementSummary = {
  hasSentStorebrandLead: boolean;
  estates: WealthManagementSummaryEntry[];
  aggregation: WealthManagementSummaryEntry | null;
};
