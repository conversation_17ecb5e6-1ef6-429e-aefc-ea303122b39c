import { ResourceNotFound } from '../../framework/errors/resource.errors';
import type { EstatePG, EstatePGUpdates } from './estate';
import { EstateModel } from './estate.model';

export type EstateRepository = {
  getByID(id: EstatePG['id']): Promise<EstatePG | null>;
  getByIDAndUserID(id: EstatePG['id'], userID: EstatePG['userID']): Promise<EstatePG | null>;
  getByUserID(userID: EstatePG['userID']): Promise<EstatePG[]>;
  create(estate: Partial<EstatePG>): Promise<EstatePG>;
  updateByID(id: EstatePG['id'], userId: EstatePG['userID'], estate: Partial<EstatePG>): Promise<EstatePG>;
  createMany(estates: Partial<EstatePG>[]): Promise<EstatePG[]>;
};

export const estateTransformer = (estateModel: EstateModel): EstatePG => ({
  id: estateModel.id,
  createdAt: estateModel.createdAt,
  updatedAt: estateModel.updatedAt,
  userID: estateModel.userID,
  type: estateModel.type,
  address: estateModel.address,
  propertyType: estateModel.propertyType,
  numberOfBedrooms: estateModel.numberOfBedrooms,
  livingArea: estateModel.livingArea,
  buildYear: estateModel.buildYear,
  floor: estateModel.floor,
  sellPreference: estateModel.sellPreference,
  landIdentificationMatrix: estateModel.landIdentificationMatrix,
  connectToBroker: estateModel.connectToBroker,
  ownership: estateModel.ownership,
  EVEstateID: estateModel.EVEstateID, // Don't use this as REST API does not give back this
  EVAddressID: estateModel.EVAddressID,
  loanAmount: estateModel.loanAmount,
  originalMortgage: estateModel.originalMortgage,
  mortgageYears: estateModel.mortgageYears,
  interestRate: estateModel.interestRate,
  estateImage: estateModel.imageUrl,
  OrganizationNumber: estateModel.OrganizationNumber,
  ShareNumber: estateModel.ShareNumber,
  isArchived: estateModel.isArchived,
  estimationOffset: estateModel.estimationOffset,
});

export const estateRepositoryFactory = (): EstateRepository => {
  const getByID = async (id: EstatePG['id']): Promise<EstatePG | null> => {
    const selectedEstate = await EstateModel.findOne({
      where: {
        id,
        isArchived: false,
      },
    });
    if (!selectedEstate) {
      return null;
    }
    return estateTransformer(selectedEstate);
  };

  const getByIDAndUserID = async (id: EstatePG['id'], userID: EstatePG['userID']): Promise<EstatePG | null> => {
    const selectedEstate = await EstateModel.findOne({
      where: {
        id,
        userID,
        isArchived: false,
      },
    });
    if (!selectedEstate) {
      return null;
    }
    return estateTransformer(selectedEstate);
  };

  const getByUserID = async (userID: EstatePG['userID']): Promise<EstatePG[]> => {
    const selectedEstates = await EstateModel.findAll({
      where: {
        userID,
        isArchived: false,
      },
    });
    return selectedEstates.map((estate) => estateTransformer(estate));
  };

  const create = async (estate: Partial<EstatePG>): Promise<EstatePG> => {
    const createdEstate = await EstateModel.create(estate);
    return estateTransformer(createdEstate);
  };

  const updateByID = async (
    id: EstatePG['id'],
    userID: EstatePG['userID'],
    updates: Partial<EstatePGUpdates>,
  ): Promise<EstatePG> => {
    const updatableEstate = await EstateModel.findOne({
      where: {
        id,
        userID,
      },
    });
    if (!updatableEstate) {
      throw new ResourceNotFound('estate not found');
    }
    const updatedEstate = await updatableEstate.update({ ...updates, updatedAt: new Date() });
    return estateTransformer(updatedEstate);
  };

  const createMany = async (estates: Partial<EstatePG>[]): Promise<EstatePG[]> => {
    const created = await EstateModel.bulkCreate(estates);
    return created.map(estateTransformer);
  };

  return { getByID, getByIDAndUserID, getByUserID, create, updateByID, createMany };
};
