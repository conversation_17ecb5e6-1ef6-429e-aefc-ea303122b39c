import type { Resource } from '../../../framework/sequelize/resource';

export type VitecEstateExtension = Resource & {
  vitecID: string;
  loanAmount: number | null;
  interestRate: number | null;
  originalMortgage: number | null;
  mortgageYears: number | null;
  mapImageUrl: string | null;
  triedToDownloadImage: boolean | null;
};

export type VitecEstateExtensionUpdates = {
  loanAmount?: number | null;
  interestRate?: number | null;
  originalMortgage?: number | null;
  mortgageYears?: number | null;
  mapImageUrl?: string | null;
  triedToDownloadImage?: boolean | null;
};
