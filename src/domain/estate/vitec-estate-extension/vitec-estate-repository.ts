import type { VitecEstateExtension, VitecEstateExtensionUpdates } from './vitec-estate-extension';
import { VitecEstateExtensionModel } from './vitec-estate-extension.model';

export type VitecEstateExtensionRepository = {
  getEstateExtensionById(vitecID: string): Promise<VitecEstateExtension | null>;
  modifyEstateExtensionById(vitecID: string, newVal: VitecEstateExtensionUpdates): Promise<VitecEstateExtension>;
  getEstateExtensions(vitecIDs: string[]): Promise<VitecEstateExtension[]>;
};

export const estateTransformer = (estateModel: VitecEstateExtensionModel): VitecEstateExtension => ({
  id: estateModel.id,
  createdAt: estateModel.createdAt,
  updatedAt: estateModel.updatedAt,
  vitecID: estateModel.vitecID,
  loanAmount: estateModel.loanAmount,
  interestRate: estateModel.interestRate,
  originalMortgage: estateModel.originalMortgage,
  mortgageYears: estateModel.mortgageYears,
  mapImageUrl: estateModel.mapImageUrl,
  triedToDownloadImage: estateModel.triedToDownloadImage,
});

export const vitecEstateExtensionRepositoryFactory = (): VitecEstateExtensionRepository => {
  const getEstateExtensionById = async (vitecID: string): Promise<VitecEstateExtension | null> => {
    const selectedEstate = await VitecEstateExtensionModel.findOne({
      where: {
        vitecID,
      },
    });
    if (!selectedEstate) {
      return null;
    }
    return estateTransformer(selectedEstate);
  };

  const getEstateExtensions = async (vitecIDs: string[]): Promise<VitecEstateExtension[]> => {
    const selectedEstates = await VitecEstateExtensionModel.findAll({
      where: {
        vitecID: vitecIDs,
      },
    });
    return selectedEstates.map(estateTransformer);
  };

  const modifyEstateExtensionById = async (
    vitecID: string,
    newVal: VitecEstateExtensionUpdates,
  ): Promise<VitecEstateExtension> => {
    const selectedEstate = await VitecEstateExtensionModel.findOne({
      where: {
        vitecID,
      },
    });
    if (selectedEstate) {
      const updated = await selectedEstate.update({ ...newVal });
      return estateTransformer(updated);
    } else {
      const created = await VitecEstateExtensionModel.create({ ...newVal, vitecID });
      return estateTransformer(created);
    }
  };

  return { getEstateExtensionById, modifyEstateExtensionById, getEstateExtensions };
};
