import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../../framework/sequelize/resource';

export const VITEC_ESTATES_TABLE_NAME = 'VitecEstateExtensions';

export const vitecEstateExtensionAttributes: ModelAttributes = {
  ...baseModelAttributes,
  vitecID: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  loanAmount: {
    type: DataTypes.INTEGER,
  },
  interestRate: {
    type: DataTypes.FLOAT,
  },
  originalMortgage: {
    type: DataTypes.INTEGER,
  },
  mortgageYears: {
    type: DataTypes.INTEGER,
  },
  mapImageUrl: {
    type: DataTypes.STRING,
  },
  triedToDownloadImage: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  },
};

export class VitecEstateExtensionModel extends ResourceModel {
  public vitecID!: string;
  public loanAmount: number;
  public interestRate: number;
  public originalMortgage: number;
  public mortgageYears: number;
  public mapImageUrl: string;
  public triedToDownloadImage: boolean;
}

export const vitecEstateExtensionsModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  VitecEstateExtensionModel.init(vitecEstateExtensionAttributes, {
    sequelize,
    tableName: VITEC_ESTATES_TABLE_NAME,
    timestamps: true,
  });
};
