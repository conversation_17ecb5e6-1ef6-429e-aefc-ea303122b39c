import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../../framework/sequelize/resource';
import type { EstateEventID, EstateEventType } from './estate-event';

export const ESTATE_EVENT_ID_ENUM_NAME = 'estate_event_id_enum';
export const ESTATE_EVENT_TYPE_ENUM_NAME = 'estate_event_type_enum';
export const ESTATE_EVENT_TABLE = 'EstateEvents';
export const ESTATE_EVENT_CONNECTION = 'EstateEventConnection';

const estateEventAttributes: ModelAttributes = {
  ...baseModelAttributes,
  eventID: {
    type: ESTATE_EVENT_ID_ENUM_NAME,
    unique: true,
  },
  title: {
    type: DataTypes.TEXT,
  },
  type: {
    type: ESTATE_EVENT_TYPE_ENUM_NAME,
  },
  logName: {
    type: DataTypes.STRING,
  },
};

export class EstateEventModel extends ResourceModel {
  public eventID!: EstateEventID;
  public title: string;
  public type: EstateEventType;
  public logName: string;
}

export const estateEventModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstateEventModel.init(estateEventAttributes, {
    sequelize,
    tableName: ESTATE_EVENT_TABLE,
    timestamps: true,
  });
};
