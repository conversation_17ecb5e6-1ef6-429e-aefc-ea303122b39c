import type { EstateEvent } from './estate-event';
import { EstateEventModel } from './estate-event.model';

export type EstateEventRepository = {
  getEstateEvents(): Promise<EstateEvent[]>;
};

export const estateEventTransformer = (estateEventModel: EstateEventModel): EstateEvent => {
  return {
    eventID: estateEventModel.eventID,
    title: estateEventModel.title,
    type: estateEventModel.type,
    logName: estateEventModel.logName,
  };
};

export const estateEventRepositoryFactory = (): EstateEventRepository => {
  const getEstateEvents = async (): Promise<EstateEvent[]> => {
    const events = await EstateEventModel.findAll();
    return events.map((event) => estateEventTransformer(event));
  };

  return { getEstateEvents };
};
