import type { User } from '../../user/user';
import type { EstateEventConnectionRepository } from '../estate-event-connection/estate-event-connection.repository';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import type { EstateEvent } from './estate-event';
import { EstateEventType } from './estate-event';
import type { EstateEventRepository } from './estate-event.repository';

export type EstateEventWithDate = EstateEvent & {
  date: Date | null;
};

export type EstateEventService = {
  getEstateEvents(userID?: User['id'], estateID?: EstateMongoose['estateId']): Promise<EstateEventWithDate[]>;
};

export const estateEventServiceFactory = ({
  estateEventRepository,
  estateEventConnectionRepository,
}: {
  estateEventRepository: EstateEventRepository;
  estateEventConnectionRepository: EstateEventConnectionRepository;
}): EstateEventService => {
  const getEstateEvents = async (
    userID?: User['id'],
    estateID?: EstateMongoose['estateId'],
  ): Promise<EstateEventWithDate[]> => {
    const allEvents = await estateEventRepository.getEstateEvents();

    const userEvents = await Promise.all(
      allEvents
        .filter((event) => event.type === EstateEventType.USER)
        .map(async (event) => ({
          ...event,
          date:
            userID && estateID
              ? await estateEventConnectionRepository.getEventDate(event.eventID, userID, estateID)
              : null,
        })),
    );

    const vitecEvents = allEvents
      .filter((event) => event.type === EstateEventType.VITEC)
      .map((event) => ({ ...event, date: null }));

    return [...userEvents, ...vitecEvents];
  };
  return { getEstateEvents };
};
