export enum EstateEventID {
  INTERIOR_GUIDANCE = 'interiorGuidance',
  PHOTOGRAPHER = 'photographer',
  APPRAISER = 'appraiser',
  AD_LIVE_IN_CORE_CHANNELS = 'adLiveInCoreChannels',
  VIEWING = 'viewing',
  BIDDING_ROUND = 'biddingRound',
  CONTRACT_SIGNING = 'contractSigning',
  TAKE_OVER = 'takeOver',
}

export enum EstateEventType {
  VITEC = 'vitec',
  USER = 'user',
}

export type EstateEvent = {
  eventID: EstateEventID;
  title: string;
  type: EstateEventType;
  logName: string;
};
