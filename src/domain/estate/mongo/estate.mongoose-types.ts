import type { Schema } from 'mongoose';
import type { LandIdentificationMatrix } from '../../land-identification/LandIdentificationMatrix';
import type { EstateBaseType } from '../../otp/otp';
import type { EstateVitecStatus } from '../estate';
import type { EstateBid } from '../estate-bid/estate-bid';
import type { EmployeeMongoose } from './employee/employee';

export type EstateBaseMongoose = {
  _id: Schema.Types.ObjectId;
  contactId: string;
  contactType: number;
  companyName: string;
  firstName: string;
  lastName: string;
  mobilePhone: string;
};

export type EstateArea = {
  name: string;
  postalCodes: string[];
  areaId: string;
};

export type EstateBuyerMongoose = EstateBaseMongoose & {
  organisationNumber: string;
};

export type EstateSellerMongoose = EstateBaseMongoose & {
  email: string;
};

export type EstateProxyMongoose = EstateBaseMongoose & {
  proxyOf: string;
};

export type EstateAddress = {
  apartmentNumber: string;
  streetAdress: string;
  zipCode: string;
  city: string;
};

export type EstateDocument = {
  documentId: string;
  head: string;
  extension: string;
  docType: number;
  lastChanged: string; // ISO date
  signStatus: VitecDocumentSignStatus;
};

export type BrokerEmployee = {
  image: {
    large: string;
    medium: string;
    small: string;
  };
  slug: string;
  name: string;
  email: string;
  title: string;
  mobilePhone: string;
  workPhone: string;
};

export type BrokerIdWithRole = {
  employeeId: string;
  brokerRole: BrokerRole;
  employee: BrokerEmployee;
};

export type BrokerIdWithRoles = BrokerIdWithRole[];

export enum CheckListValue {
  NO = 0,
  YES = 1,
  NOT_RELEVANT = 2,
}

export type CheckListItem = {
  tags: string[];
  value: CheckListValue;
  changedBy: string;
  changedDate: Date;
};

export type CheckList = {
  checkListItems: CheckListItem[];
  lastChanged: Date;
};

export enum EstateMongooseAssignmentTypeGroups {
  NOTSET = 0,
  SALE = 1,
  RENT = 2,
  VALUATION = 3,
  SETTLEMENT = 4,
  FORECLOSURE = 5,
  COMMERCIALAREA = 6,
  PROJECTSALE = 7,
  COMMERCIALSALE = 8,
  COMMERCIALRENT = 9,
  ADMINISTRATION = 10,
}

export type EstateMongoose = {
  _id: Schema.Types.ObjectId;
  estateId: string;
  assignmentNum: string;
  assignmentType: number;
  assignmentTypeGroup: EstateMongooseAssignmentTypeGroups;
  ownAssignmentType: string;
  systemId: string;
  finnCode: string;
  finnPublishDate: Date;
  finnExpireDate: Date;
  takeOverDate: Date;
  contractMeetingDate: Date;
  changedDate: Date;
  address: EstateAddress;
  ownership: number;
  employeeId: string;
  brokersIdWithRoles: BrokerIdWithRoles;
  departmentId: number;
  status: number;
  defaultImageId: string;
  defaultImage: {
    large: string;
    medium: string;
    small: string;
  };
  heading: string;
  showings: { start: string; end: string; showingId: string }[];
  showingNote: string;
  noOfRooms: number;
  noOfBedRooms: number;
  textFields: Record<string, unknown>;
  floor: number;
  facilities: [];
  estatePreferences: [];
  constructionYear: number;
  energyLetter: number;
  energyColorCode: number;
  plot: Record<string, unknown>;
  partOwnership: Record<string, unknown>;
  estatePrice: {
    changedDate: Date;
    priceSuggestion: number;
    soldPrice: number;
    estimatedValue: number;
    communityTax: number;
    communityTaxYear: number;
    salesCostDescription: string;
  };
  estateSize: {
    /**
     * @deprecated The property is deprecated and will be removed in the future. Use the sum of BRA_i for the estate instead.
     */
    primaryRoomArea: number;
    primaryRoomAreaDescription: string;
    grossArea: number;
    /**
     * @deprecated The property is deprecated and will be removed in the future.
     */
    usableArea: number;
  };
  buildings: EstateBuilding[];
  valuationTax: Record<string, unknown>;
  links: string[];
  commissionAcceptedDate: Date;
  soldDate: Date;
  estateTypeId: string;
  estateType: string;
  estateBaseType: EstateBaseType;
  estateTypeExternal: number;
  createdDate: Date;
  expireDate: Date | null;
  location: {
    type: string;
    coordinates: number[];
  };
  matrikkel: LandIdentificationMatrix[];
  municipality: string;
  municipalityId: string;
  takeoverComment: string;
  appraiserContactId: string;
  appraiserContact: Record<string, unknown>;
  businessManagerContact: Record<string, unknown>;
  tag: string;
  projectId: string;
  projectRelation: number;
  projectUnits: string[];
  projectTextFields: Record<string, unknown>;
  projectName: string;
  activities: { start: Date; end: Date; type: number; name: string }[];
  statusChanges: { from: EstateVitecStatus; to: EstateVitecStatus; date: Date }[];
  url: string;
  sellers: EstateSellerMongoose[];
  employee: string | EmployeeMongoose;
  department: Record<string, unknown>;
  areaId: string;
  bids: EstateBid[];
  documents: EstateDocument[];
  contacts:
    | {
        contactId: string;
        comments: {
          comment: string;
          changedDate: Date;
        }[];
        proxyId: string;
        relationType: ContactRelationType;
      }[]
    | null;
  buyers: EstateBuyerMongoose[];
  ads: {
    id: string;
    preview: boolean;
    channel: number;
    finnAdType: number;
    adStatus: number;
    publishStart: Date;
    publishEnd: Date;
    lastChanged: Date;
  }[];
  checkList: CheckList;
  proxies: EstateProxyMongoose[] | null;
};

export enum ContactRelationType {
  VIEWING = 0,
  INTERESTED = 1,
}

export enum BrokerRole {
  UNKNOWN = -1,
  MAIN_BROKER = 1,
  RESPONSIBLE_BROKER = 2,
  ASSISTANT = 3,
  RESPONSIBLE_SETTLEMENT = 4,
}

export enum VitecDocumentSignStatus {
  NOT_SIGNED = 0,
  MANUALLY_SIGNED = 1,
  ELECTRONICALLY_SIGNED = 2,
}

export type EstateBuilding = {
  name: string;
  buildingArea: EstateBuildingArea[];
};

export type EstateBuildingArea = {
  areaType: AreaType;
  areaInformation: EstateAreaInformation[];
};

export type EstateAreaInformation = {
  floorNumber: number;
  areaSize: number;
  areaDescription: string;
};

export enum AreaType {
  UsageArea = 1,
  LivingArea = 2,
  GrossArea = 3,
  GrossAreaBta = 4,
  UsageAreaI = 10,
  UsageAreaE = 11,
  UsageAreaB = 12,
  OpenArea = 20,
}
