import type { Connection, Document, FilterQuery, Types } from 'mongoose';
import { flatten, isEmpty } from 'ramda';
import { withoutNorwayCountryCode } from '../../../utils/phone-number.utils';
import type { BuyerMongoose } from '../../customer/mongo/buyer/buyer';
import { buyerSchema } from '../../customer/mongo/buyer/buyer.schema';
import type { SellerMongoose } from '../../customer/mongo/seller/seller';
import { sellerSchema } from '../../customer/mongo/seller/seller.schema';
import type { EstateImage } from '../estate';
import { EstateVitecStatus, UserEstateRelation } from '../estate';
import type { EstateImageMongoose } from '../estate-image/estate-image';
import { imageSchema } from '../estate-image/estate-image.schema';
import type { EmployeeMongoose } from './employee/employee';
import { employeeSchema } from './employee/employee.schema';
import type { EstateArea, EstateMongoose } from './estate.mongoose-types';
import { estateSchema } from './estate.schema';

export type EstateMongooseResponse = EstateMongoose & { broker: { name: string; image: EstateImage } | null };

function mapToEstateMongooseResponse(
  estate: Omit<
    EstateMongoose &
      Document<any, any, any> & {
        _id: Types.ObjectId;
      },
    never
  >,
): EstateMongooseResponse {
  const employee = transformEstateEmployee(estate.employee as EmployeeMongoose);

  return {
    ...estate.toJSON(),
    broker: {
      name: employee?.name,
      image: employee?.image,
    },
  } as EstateMongooseResponse;
}

export function getPhoneNumberRegExp(phoneNumberWithoutCountryCode: string): RegExp {
  return new RegExp(`([0-9, +]{4})?${phoneNumberWithoutCountryCode}`);
}

export type EstateMongooseRepository = {
  findEstateByVitecEstateId(vitecID: EstateMongoose['estateId']): Promise<EstateMongooseResponse | null>;
  findEstatesBySellerPhoneOrEmail(opts: {
    phone: string | null;
    email: string | null;
    extraQuery?: FilterQuery<EstateMongoose & Document<any, any, any>>;
  }): Promise<EstateMongooseResponse[]>;
  getEstateImage(estateID: string): Promise<EstateImage | null>;
  findEstatesByPredicate(
    fields: (keyof EstateMongoose)[],
    conditions?: Record<string, unknown>,
    limit?: number,
    skip?: number,
    sortBy?: SortPredicate[],
  ): Promise<EstateMongoose[]>;
  countEstates(conditions?: Record<string, unknown>): Promise<number>;
  getImagesForEstates(estateIDs: string[]): Promise<EstateImageMongoose[]>;
  findEstatesByBuyerPhoneOrEmail(opts: {
    phone: string | null;
    email: string | null;
    extraQuery?: FilterQuery<EstateMongoose & Document<any, any, any>>;
  }): Promise<EstateMongooseResponse[]>;
  findEstatesByBrokerPhone(
    phone: string,
    extraQuery?: FilterQuery<EstateMongoose & Document<any, any, any>>,
  ): Promise<EstateMongooseResponse[]>;
  checkIfSellingProperty(userPhone: string | null, estate: EstateMongoose): boolean;
  checkIfBuyingProperty(userPhone: string | null, estate: EstateMongoose): boolean;
  checkIfBrokerProperty(userPhone: string | null, estate: EstateMongoose): boolean;
  checkUserEstateRelation(userPhone: string, estateID: EstateMongoose): Promise<UserEstateRelation | null>;
  getAllEstates(opts: {
    takeOverYearFrom: number; // like 2022
    takeOverMonthFrom: string; // like '01' for January
    takeOverYearTo: number;
  }): Promise<Pick<EstateMongoose, 'estateId' | 'takeOverDate' | 'address' | 'departmentId' | 'brokersIdWithRoles'>[]>;
};

export const estateImageTransformer = (estateImage: EstateImageMongoose): EstateImageMongoose['url'] | null =>
  estateImage.url || null;

export const estateMongooseRepositoryFactory = ({
  connection,
}: {
  connection: Connection;
}): EstateMongooseRepository => {
  const sellerModel = connection.model<SellerMongoose & Document>('seller', sellerSchema, 'sellers');
  const buyerModel = connection.model<BuyerMongoose & Document>('buyer', buyerSchema, 'buyers');
  const employeeModel = connection.model<EmployeeMongoose & Document>('employee', employeeSchema, 'employees');
  const estateModel = connection.model<EstateMongoose & Document>('estate', estateSchema, 'estates');
  const imagesModel = connection.model<EstateImageMongoose & Document>('EstateImage', imageSchema, 'estateimages');
  connection.model<EmployeeMongoose & Document>('employee', employeeSchema, 'employees');
  connection.model<EstateArea & Document>('EstateArea', sellerSchema, 'areas');

  const getEstateImage = async (estateID: string): Promise<EstateImage | null> => {
    const image = await imagesModel.findOne({
      estate: estateID,
    });
    if (!image) {
      return null;
    }
    return estateImageTransformer(image);
  };

  const findEstateByVitecEstateId = async (
    vitecID: EstateMongoose['estateId'],
  ): Promise<EstateMongooseResponse | null> => {
    const selectedEstate = await estateModel
      .findOne({
        estateId: vitecID,
      })
      .populate([{ path: 'employee', model: 'Employee' }]);
    if (!selectedEstate) {
      return null;
    }
    return mapToEstateMongooseResponse(selectedEstate);
  };

  const findEstatesByBuyerPhoneOrEmail = async ({
    phone,
    email,
    extraQuery = {},
  }: {
    phone: string | null;
    email: string | null;
    extraQuery?: FilterQuery<EstateMongoose & Document<any, any, any>>;
  }): Promise<EstateMongooseResponse[]> => {
    const phoneNumber = withoutNorwayCountryCode(phone);
    const phoneOrEmailQuery = phoneNumber
      ? {
          mobilePhone: { $regex: getPhoneNumberRegExp(phoneNumber) },
        }
      : { email };

    const buyersWithPhoneNumber = await buyerModel.find(phoneOrEmailQuery);

    if (!buyersWithPhoneNumber.length) {
      return [];
    }

    const estateObjectIds = flatten(buyersWithPhoneNumber.map((buyer) => buyer.estates));
    const estates = await estateModel
      .find({
        _id: {
          $in: estateObjectIds,
        },
        status: { $ne: EstateVitecStatus.OWN_ARCHIVED },
        ...extraQuery,
      })
      .populate([{ path: 'employee', model: 'Employee' }])
      .sort('-createdDate');

    return estates.map(mapToEstateMongooseResponse);
  };

  const findEstatesBySellerPhoneOrEmail = async ({
    phone,
    email,
    extraQuery = {},
  }: {
    phone: string | null;
    email: string | null;
    extraQuery: FilterQuery<EstateMongoose & Document<any, any, any>>;
  }): Promise<EstateMongooseResponse[]> => {
    const phoneNumber = withoutNorwayCountryCode(phone);
    const phoneOrEmailQuery = phoneNumber
      ? {
          mobilePhone: { $regex: getPhoneNumberRegExp(phoneNumber) },
        }
      : { email };

    const sellersWithPhoneNumber = await sellerModel.find(phoneOrEmailQuery);

    if (!sellersWithPhoneNumber.length) {
      return [];
    }

    const estateObjectIds = flatten(sellersWithPhoneNumber.map((seller) => seller.estates));

    const estates = await estateModel
      .find({
        _id: {
          $in: estateObjectIds,
        },
        status: { $ne: EstateVitecStatus.OWN_ARCHIVED },
        ...extraQuery,
      })
      .populate([{ path: 'employee', model: 'Employee' }])
      .sort('-createdDate');

    return estates.map(mapToEstateMongooseResponse);
  };

  const findEstatesByBrokerPhone = async (
    phone: string,
    extraQuery: FilterQuery<EstateMongoose & Document<any, any, any>> = {},
  ): Promise<EstateMongooseResponse[]> => {
    const phoneNumber = withoutNorwayCountryCode(phone);
    const brokersWithPhoneNumber = await employeeModel.find({
      mobilePhone: { $regex: getPhoneNumberRegExp(phoneNumber) },
    });

    if (!brokersWithPhoneNumber.length) {
      return [];
    }

    const brokerEmployeeIds = brokersWithPhoneNumber.map((b) => b.employeeId);

    const brokerEstates = await estateModel
      .find({
        'brokersIdWithRoles.employeeId': { $in: brokerEmployeeIds },
        status: { $ne: EstateVitecStatus.OWN_ARCHIVED },
        ...extraQuery,
      })
      .populate([{ path: 'employee', model: 'Employee' }])
      .sort('-createdDate');

    return brokerEstates.map(mapToEstateMongooseResponse);
  };

  const findEstatesByPredicate = async (
    fields: (keyof EstateMongoose)[],
    conditions: Record<string, unknown> = {},
    limit?: number,
    skip?: number,
    sortBy?: SortPredicate[],
  ): Promise<EstateMongoose[]> => {
    let estateQuery = estateModel
      .find(conditions, fields.join(' '))
      .sort(generateSorting(sortBy))
      .limit(limit ? limit : 0);
    if (skip) {
      estateQuery = estateQuery.skip(skip);
    }

    const populateFields: {
      path: keyof EstateMongoose;
      model: string;
    }[] = [{ path: 'employee', model: 'Employee' }];

    const populatedQuery = populateFields.reduce((acc, curr) => {
      if (fields.includes(curr.path)) {
        return acc.populate(curr);
      }
      return acc;
    }, estateQuery);

    const estates = await populatedQuery;
    return estates.map((estate) => estate.toJSON() as EstateMongoose);
  };

  const countEstates = async (conditions: Record<string, unknown> = {}): Promise<number> => {
    return estateModel.countDocuments({
      ...conditions,
    });
  };

  const getImagesForEstates = async (estateIDs: string[]): Promise<EstateImageMongoose[]> => {
    return imagesModel.find({ estate: { $in: estateIDs } }).lean();
  };

  const checkIfSellingProperty = (userPhone: string, estate: EstateMongoose): boolean => {
    const mobilePhoneRegex = getPhoneNumberRegExp(withoutNorwayCountryCode(userPhone));
    return estate.sellers.some((s) => mobilePhoneRegex.test(s.mobilePhone));
  };

  const checkIfBuyingProperty = (userPhone: string, estate: EstateMongoose): boolean => {
    const mobilePhoneRegex = getPhoneNumberRegExp(withoutNorwayCountryCode(userPhone));
    return estate.buyers.some((b) => mobilePhoneRegex.test(b.mobilePhone));
  };

  const checkIfBrokerProperty = (userPhone: string, estate: EstateMongoose): boolean => {
    const mobilePhoneRegex = getPhoneNumberRegExp(withoutNorwayCountryCode(userPhone));
    return estate.brokersIdWithRoles.some((b) => mobilePhoneRegex.test(b.employee.mobilePhone));
  };

  const checkUserEstateRelation = async (
    userPhone: string,
    estate: EstateMongoose,
  ): Promise<UserEstateRelation | null> => {
    const isSeller = checkIfSellingProperty(userPhone, estate);
    if (isSeller) {
      return UserEstateRelation.SELLER;
    }
    const isBuyer = checkIfBuyingProperty(userPhone, estate);
    if (isBuyer) {
      return UserEstateRelation.BUYER;
    }
    const isBroker = checkIfBrokerProperty(userPhone, estate);
    if (isBroker) {
      return UserEstateRelation.BROKER;
    }
    return null;
  };

  const getAllEstates = async (opts: {
    takeOverYearFrom: number; // like 2022
    takeOverMonthFrom: string; // like '01' for January
    takeOverYearTo: number;
  }): Promise<
    Pick<EstateMongoose, 'estateId' | 'takeOverDate' | 'address' | 'departmentId' | 'brokersIdWithRoles'>[]
  > => {
    return await estateModel.find(
      {
        takeOverDate: {
          $gte: new Date(`${opts.takeOverYearFrom}-${opts.takeOverMonthFrom}-01`),
          $lt: new Date(`${opts.takeOverYearTo}-12-31`),
        },
      },
      { estateId: 1, takeOverDate: 1, address: 1, departmentId: 1, brokersIdWithRoles: 1 },
    );
  };

  return {
    getEstateImage,
    findEstateByVitecEstateId,
    findEstatesBySellerPhoneOrEmail,
    findEstatesByPredicate,
    countEstates,
    getImagesForEstates,
    findEstatesByBuyerPhoneOrEmail,
    findEstatesByBrokerPhone,
    checkIfSellingProperty,
    checkIfBuyingProperty,
    checkIfBrokerProperty,
    checkUserEstateRelation,
    getAllEstates,
  };
};

export const transformEstateEmployee = (estateEmployee: EmployeeMongoose | undefined): EmployeeMongoose | null => {
  if (!estateEmployee) {
    return null;
  }
  return estateEmployee.toJSON() as EmployeeMongoose;
};

export const transformEstateImage = (estateImage: EstateImageMongoose | undefined): EstateImage | null => {
  if (!estateImage) {
    return null;
  }
  const estateImageJSON = estateImage.toJSON() as { url: { small: string; medium: string; large: string } };
  return { small: estateImageJSON.url.small, medium: estateImageJSON.url.medium, large: estateImageJSON.url.large };
};

export enum SortMode {
  ASCENDING = 1,
  DESCENDING = -1,
}

export enum SortField {
  PRICE = 'price',
  SIZE = 'size',
  DATE = 'date',
  STATUS = 'status',
}

export type SortPredicate = {
  sortMode: SortMode;
  sortField: SortField;
};

const generateSorting = (sortBy?: SortPredicate[]): { [key: string]: 1 | -1 } => {
  const getMongoField = (field: SortField): string | null => {
    switch (field) {
      case SortField.PRICE:
        return 'estatePrice.priceSuggestion';
      case SortField.SIZE:
        return 'estateSize.primaryRoomArea';
      case SortField.DATE:
        return 'createdDate';
      case SortField.STATUS:
        return 'status';
      default:
        return null;
    }
  };

  if (!sortBy || isEmpty(sortBy)) {
    return {
      createdDate: -1,
      estateId: 1,
    };
  }

  return {
    ...sortBy.reduce((cache, sort) => {
      const fieldName = getMongoField(sort.sortField);
      if (fieldName) {
        return {
          ...cache,
          [fieldName]: sort.sortMode,
        };
      }
      return {
        createdDate: -1,
      };
    }, {}),
    estateId: 1,
  };
};
