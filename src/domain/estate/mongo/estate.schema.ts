import { Schema, Types } from 'mongoose';

export const estateSchema = new Schema({
  _id: Types.ObjectId,
  estateId: String,
  assignmentNum: String,
  assignmentType: Number,
  assignmentTypeGroup: Number,
  ownAssignmentType: String,
  systemId: String,
  finnCode: String,
  finnPublishDate: Date,
  finnExpireDate: Date,
  takeOverDate: Date,
  contractMeetingDate: Date,
  changedDate: Date,
  address: Schema.Types.Mixed,
  ownership: Number,
  employeeId: String,
  brokersIdWithRoles: [
    {
      employeeId: String,
      brokerRole: Number,
      // employee: { type: Schema.Types.ObjectId, ref: 'Employee' }
      employee: {
        type: Schema.Types.Mixed,
      },
    },
  ],
  departmentId: Number,
  status: Number,
  defaultImageId: String,
  defaultImage: {
    large: String,
    medium: String,
    small: String,
  },
  heading: String,
  showings: [{ start: Schema.Types.Date, end: Schema.Types.Date, showingId: Schema.Types.String }],
  showingNote: String,
  noOfRooms: Number,
  noOfBedRooms: Number,
  textFields: Schema.Types.Mixed,
  floor: Number,
  facilities: [],
  estatePreferences: [],
  constructionYear: Number,
  energyLetter: Number,
  energyColorCode: Number,
  plot: {
    type: Schema.Types.Mixed,
  },
  partOwnership: {
    type: Schema.Types.Mixed,
  },
  estatePrice: {
    type: Schema.Types.Mixed,
  },
  estateSize: {
    type: Schema.Types.Mixed,
  },
  valuationTax: {
    type: Schema.Types.Mixed,
  },
  buildings: [
    {
      name: String,
      buildingArea: [
        {
          areaType: Number,
          areaInformation: [
            {
              floorNumber: Number,
              areaSize: Number,
              areaDescription: String,
            },
          ],
        },
      ],
    },
  ],
  links: [],
  commissionAcceptedDate: Date,
  soldDate: Date,
  estateTypeId: String,
  estateType: String,
  estateBaseType: Number,
  estateTypeExternal: Number,
  createdDate: Date,
  expireDate: Date,
  location: {
    type: { type: String },
    coordinates: [],
  },
  matrikkel: [],

  municipality: String,
  municipalityId: String,

  takeoverComment: String,
  appraiserContactId: String,
  appraiserContact: {
    type: Schema.Types.Mixed,
  },

  businessManagerContact: {
    type: Schema.Types.Mixed,
  },
  bids: [
    {
      bidId: Schema.Types.Number,
      type: Schema.Types.Mixed,
      time: Schema.Types.String,
      amount: Schema.Types.Number,
      accepted: Schema.Types.Boolean,
      reservations: Schema.Types.Boolean,
      rejectedDate: Schema.Types.String,
      expires: Schema.Types.String,
      partyId: Schema.Types.Number,
      changedDate: Schema.Types.String,
    },
  ],

  tag: String,
  projectId: String,
  projectRelation: Number,
  projectUnits: [],
  projectTextFields: Schema.Types.Mixed,
  projectName: String,
  activities: [Schema.Types.Mixed],
  url: String,

  sellers: [
    {
      firstName: String,
      lastName: String,
    },
  ],
  buyers: [
    {
      _id: { type: Schema.Types.ObjectId },
      contactId: String,
      contactType: Number,
      companyName: String,
      firstName: String,
      lastName: String,
      mobilePhone: String,
    },
  ],

  employee: { type: Schema.Types.ObjectId, ref: 'Employee' },
  department: { type: Schema.Types.ObjectId, ref: 'Department' },
  images: [{ type: Schema.Types.ObjectId, ref: 'EstateImage' }],
  area: { type: Schema.Types.ObjectId, ref: 'Area' },
  documents: [
    {
      documentId: String,
      head: String,
      extension: String,
      docType: Number,
      lastChanged: String,
      signStatus: Number,
    },
  ],
  checkList: {
    checkListItems: [
      {
        tags: [String],
        value: Number,
        changedBy: String,
        changedDate: Date,
      },
    ],
    lastChanged: Date,
  },
  ads: [
    {
      id: Number,
      preview: Boolean,
      channel: Number,
      finnAdType: Number,
      adStatus: Number,
      publishStart: Date,
      publishEnd: Date,
      lastChanged: Date,
    },
  ],
});
