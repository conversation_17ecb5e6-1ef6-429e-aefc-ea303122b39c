import type { Document, Schema } from 'mongoose';

export type EmployeeMongoose = Document & {
  _id: Schema.Types.ObjectId;
  image: {
    large: string;
    medium: string;
    small: string;
  };
  departmentId: number[];
  estates: string[];
  employeeId: string;
  title: string;
  name: string;
  mobilePhone: string;
  workPhone: string;
  email: string;
  employeeActive: boolean;
  changedDate: Date;
  slug: string;
  department: Schema.Types.ObjectId;
  aboutMe: string;
  student: boolean;
  webPublish: boolean;
  imageTimestamp: Date;
};
