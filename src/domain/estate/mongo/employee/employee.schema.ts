import { Schema, Types } from 'mongoose';

export const employeeSchema = new Schema({
  _id: Types.ObjectId,
  image: {
    large: String,
    medium: String,
    small: String,
  },
  departmentId: [Number],
  estates: [String],
  employeeId: String,
  title: String,
  name: String,
  mobilePhone: String,
  workPhone: String,
  email: String,
  employeeActive: Boolean,
  changedDate: Date,
  slug: String,
  department: Types.ObjectId,
  aboutMe: String,
  student: <PERSON><PERSON><PERSON>,
  webPublish: Boolean,
  imageTimestamp: Date,
});
