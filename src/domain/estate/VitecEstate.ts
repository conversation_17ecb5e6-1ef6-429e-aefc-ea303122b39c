import { pick } from 'lodash';
import { equals } from 'ramda';
import { cityToTitleCase } from '../../utils/address.utils';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import type { EstateImage, EstateSummary, SummarizableEstate, UserEstateRelation } from './estate';
import { EstateVitecStatus } from './estate';
import { EstateActivityType } from './estate-activity/estate-activity';
import { getEstatePropertyTypeString } from './estate-property-type';
import type { EstateTimelineSellerSection } from './estate-timeline/seller/estate-timeline-seller';
import { estateSummaryFactory } from './estate.factory';
import { getEstateAddressText, getEstateStatusText, getLivingArea } from './estate.service';
import type { EmployeeMongoose } from './mongo/employee/employee';
import type { EstateMongooseResponse } from './mongo/estate.mogoose-repository';
import type { EstateMongoose } from './mongo/estate.mongoose-types';
import { CheckListValue, EstateMongooseAssignmentTypeGroups } from './mongo/estate.mongoose-types';
import type { VitecEstateExtension } from './vitec-estate-extension/vitec-estate-extension';

export const getBaseLandIdentificationMatrixFromVitecEstate = (
  estate: EstateMongooseResponse | EstateMongoose | null,
): LandIdentificationMatrix | null => {
  if (!estate || !estate.matrikkel || !estate.matrikkel[0]) {
    return null;
  }

  const matrix = estate.matrikkel[0];
  return pick(matrix, ['bnr', 'fnr', 'gnr', 'knr', 'snr']);
};

export const createVitecEstate = (
  dto: EstateMongooseResponse & { extension?: VitecEstateExtension },
  timeline: EstateTimelineSellerSection[],
  userEstateRelation?: UserEstateRelation,
  shareNumber?: string | null,
  orgNumber?: string | null,
  pgEstateId?: string | null,
): SummarizableEstate => {
  const matchesLandIdentificationMatrix = (other: SummarizableEstate): boolean => {
    if (!dto.matrikkel || !dto.matrikkel[0]) {
      return false;
    }
    return equals(getBaseLandIdentificationMatrix(), other.getBaseLandIdentificationMatrix());
  };

  const toEstateSummary = (): EstateSummary => {
    let status = dto.status;

    // https://linear.app/nordvik/issue/NOR-2028/logic-change-for-valuation-vs-salesjourney-in-the-nordvik-app
    if (
      status === EstateVitecStatus.PREPARATION &&
      dto.assignmentTypeGroup === EstateMongooseAssignmentTypeGroups.VALUATION
    ) {
      status = EstateVitecStatus.REQUEST;
    }

    return estateSummaryFactory({
      id: dto.estateId,
      vitecID: dto.estateId,
      pgEstateId,
      propertyType: getEstatePropertyTypeString(dto.estateType),
      broker: dto.broker ? getEstateSummaryBroker() : null,
      soldDate: dto.soldDate ? dto.soldDate.toISOString() : null,
      soldPrice: dto.estatePrice?.soldPrice,
      takeOverDate: dto.takeOverDate ? dto.takeOverDate.toISOString() : null,
      status: getProcessStatus(),
      image: getImage(dto),
      address: cityToTitleCase(getEstateAddressText(dto.address)),
      streetAddress: dto.address.streetAdress,
      zipCode: dto.address.zipCode,
      statusText: getEstateStatusText(dto),
      vitecStatus: dto.assignmentTypeGroup === EstateMongooseAssignmentTypeGroups.VALUATION ? 0 : dto.status,
      floor: dto.floor,
      area: getLivingArea(dto),
      createdDate: dto.createdDate ? dto.createdDate.toString() : new Date(0).toISOString(),
      salesProcess: getSalesProcess(),
      numberOfBedrooms: dto.noOfBedRooms,
      buildYear: dto.constructionYear,
      bidValue: getBidValue(),
      userEstateRelation: userEstateRelation ? userEstateRelation : null,
      shareNumber,
      orgNumber,
      areEstateImagesShowableInPictureGallery:
        dto.status > EstateVitecStatus.PREPARATION ||
        (dto.status === EstateVitecStatus.PREPARATION &&
          !!dto.checkList.checkListItems.find(
            (c) => c.tags.includes('BILDER_I_APP') && c.value === CheckListValue.YES,
          )),
      departmentId: dto.departmentId,
    });
  };

  const getImage = (dto: EstateMongooseResponse & { extension?: VitecEstateExtension }): EstateImage | null => {
    if (dto.defaultImage) {
      return dto.defaultImage;
    }
    if (dto.extension?.mapImageUrl) {
      return {
        small: dto.extension.mapImageUrl,
        medium: dto.extension.mapImageUrl,
        large: dto.extension.mapImageUrl,
      };
    }
    return null;
  };

  const getBaseLandIdentificationMatrix = (): LandIdentificationMatrix | null => {
    return getBaseLandIdentificationMatrixFromVitecEstate(dto);
  };

  const getSalesProcess = (): EstateSummary['salesProcess'] => {
    const nextStep = timeline.find((e) => e.isActive);
    const nextEvent = nextStep?.items.find((e) => !e.isComplete);
    return { stepName: nextStep?.name, nextEvent: nextEvent?.name };
  };

  const getProcessStatus = (): EstateSummary['status'] => {
    const MAX_STEP_COUNT = 4;
    return { remainingSteps: MAX_STEP_COUNT - getCompletedProcessCount(), stepName: '' };
  };

  const getCompletedProcessCount = (): number => {
    const lastViewingElement = dto.activities
      .filter((a) => a.type === EstateActivityType.VIEWING)
      .slice(-1)
      .pop();
    return [
      lastViewingElement ? isDatePassed(lastViewingElement.end.toString()) : false,
      isDatePassed(dto.finnPublishDate),
      isDatePassed(dto.contractMeetingDate),
      isDatePassed(dto.takeOverDate?.toString()),
    ].filter(Boolean).length;
  };

  const isDatePassed = (date: Date | string | null): boolean => {
    return !!date && Date.now() >= new Date(date).getTime();
  };

  const getEstateSummaryBroker = (): EstateSummary['broker'] => {
    if (!dto.employee) {
      return null;
    }
    const { image, name, email, mobilePhone, workPhone } = dto.employee as EmployeeMongoose;
    return { name, image, email, mobilePhone: mobilePhone, workPhone: workPhone };
  };
  const getBidValue = (): number | null => {
    const acceptedBids = dto.bids.filter((bid) => bid.accepted === true);
    if (acceptedBids.length > 1) {
      return acceptedBids.sort((a, b) => new Date(b.changedDate).getTime() - new Date(a.changedDate).getTime())[0]
        .amount;
    }
    if (acceptedBids.length === 0) {
      return null;
    }
    return acceptedBids[0].amount;
  };

  return { matchesLandIdentificationMatrix, getBaseLandIdentificationMatrix, toEstateSummary };
};
