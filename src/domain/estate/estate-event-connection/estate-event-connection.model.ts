import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../../framework/sequelize/resource';
import { UserModel } from '../../user/user.model';
import { ESTATE_CHECKLIST_ID_ENUM_NAME } from '../estate-checklist/estate-checklist.model';
import { EstateEventModel } from '../estate-event/estate-event.model';

const ESTATE_EVENT_CONNECTION = 'EstateEventConnection';

const estateEventConnectionAttributes: ModelAttributes = {
  ...baseModelAttributes,
  vitecID: {
    type: DataTypes.TEXT,
  },
  userID: {
    type: DataTypes.UUID,
    references: {
      model: UserModel,
      key: 'id',
    },
    allowNull: false,
  },
  eventID: {
    type: ESTATE_CHECKLIST_ID_ENUM_NAME,
    references: {
      model: EstateEventModel,
      key: 'eventID',
    },
    allowNull: false,
  },
  date: {
    type: DataTypes.DATE,
    allowNull: false,
  },
};

export class EstateEventConnectionModel extends ResourceModel {
  public vitecID!: string;
  public userID!: UserModel['id'];
  public eventID!: EstateEventModel['eventID'];
  public date!: Date;
}

export const estateEventConnectionModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  EstateEventConnectionModel.init(estateEventConnectionAttributes, {
    sequelize,
    tableName: ESTATE_EVENT_CONNECTION,
    timestamps: true,
  });
};

export const setEstateEventConnectionModelReferences = (): void => {
  EstateEventConnectionModel.belongsTo(EstateEventModel, { foreignKey: 'eventID' });
  EstateEventConnectionModel.belongsTo(UserModel, {
    foreignKey: 'userID',
    as: 'user',
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  });
};
