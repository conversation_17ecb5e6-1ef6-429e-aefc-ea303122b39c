import type { User } from '../../user/user';
import type { EstateMongoose } from '../mongo/estate.mongoose-types';
import type { EstateEventModel } from '../estate-event/estate-event.model';
import { EstateEventConnectionModel } from './estate-event-connection.model';

export type EstateEventConnectionRepository = {
  getEventDate(
    eventID: EstateEventModel['eventID'],
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
  ): Promise<Date | null>;
  setEventDate(
    eventID: EstateEventModel['eventID'],
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
    date: Date,
  ): Promise<void>;
};

export const estateEventConnectionRepositoryFactory = (): EstateEventConnectionRepository => {
  const getEventDate = async (
    eventID: EstateEventModel['eventID'],
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
  ): Promise<Date | null> => {
    const eventConnection = await EstateEventConnectionModel.findOne({
      where: {
        vitecID,
        eventID,
        userID,
      },
    });

    if (!eventConnection) {
      return null;
    }

    return eventConnection.date;
  };

  const setEventDate = async (
    eventID: EstateEventModel['eventID'],
    userID: User['id'],
    vitecID: EstateMongoose['estateId'],
    date: Date,
  ): Promise<void> => {
    const eventConnection = await EstateEventConnectionModel.findOne({
      where: {
        vitecID,
        eventID,
        userID,
      },
    });

    if (!eventConnection) {
      await EstateEventConnectionModel.create({
        vitecID,
        eventID,
        userID,
        date,
      });
    } else {
      await EstateEventConnectionModel.update(
        {
          date,
        },
        {
          where: {
            vitecID,
            eventID,
            userID,
          },
        },
      );
    }
  };

  return { getEventDate, setEventDate };
};
