import { v4 } from 'uuid';
import type { Estate, EstateSummary } from './estate';
import { EstateType } from './estate';

export const estateFactory = ({
  id = v4(),
  vitecID = null,
  pgEstateId = null,
  address = '',
  matches = 0,
  primaryRoomArea = 0,
  numberOfBedrooms = 0,
  propertyType = '',
  type = EstateType.FOR_SALE,
  valuation = 0,
  purchasePrice = 0,
  statusText = '',
  brokers = [],
  bids = [],
  image = null,
  price = {
    label: '',
    value: 0,
    lastBidValue: 0,
  },
  statistics = null,
  floor = 0,
  ownership = '',
  constructionYear = 0,
  sellPreference = '',
  connectToBroker = false,
  status = 0,
  userEstateRelation = null,
  wentToViewingNo = null,
  interestedNo = null,
}: Partial<Estate> = {}): Estate => ({
  id,
  vitecID,
  pgEstateId,
  address,
  image,
  matches,
  primaryRoomArea,
  numberOfBedrooms,
  propertyType,
  valuation,
  purchasePrice,
  statusText,
  brokers,
  bids,
  statistics,
  type,
  price,
  floor,
  estimatedValue: null,
  ownership,
  constructionYear,
  sellPreference,
  connectToBroker,
  userEstateRelation,
  status,
  wentToViewingNo,
  interestedNo,
});

export const estateSummaryFactory = ({
  id = v4(),
  vitecID = null,
  pgEstateId = null,
  type = EstateType.FOR_SALE,
  address = '',
  streetAddress = '',
  image = null,
  status = { remainingSteps: 0, stepName: '' },
  broker = null,
  soldDate = null,
  soldPrice = null,
  takeOverDate = null,
  createdDate = new Date(0).toISOString(),
  statusText = '',
  vitecStatus = null,
  zipCode = '',
  propertyType = '',
  floor = 0,
  area = 0,
  numberOfBedrooms = 0,
  buildYear = 0,
  sellPreference = '',
  connectToBroker = false,
  ownerSince = null,
  estimatedValue = 0,
  valuationChange = { increasePercentage: null, increaseValue: null },
  bidValue = null,
  userEstateRelation = null,
  shareNumber = null,
  orgNumber = null,
  salesProcess = { stepName: '', nextEvent: '' },
  areEstateImagesShowableInPictureGallery = null,
  departmentId = null,
}: Partial<EstateSummary> & { createdDate: string }): EstateSummary => ({
  id,
  vitecID,
  pgEstateId,
  type,
  address,
  streetAddress,
  image,
  status,
  broker,
  soldDate,
  soldPrice,
  takeOverDate,
  createdDate,
  zipCode,
  statusText,
  vitecStatus,
  propertyType,
  floor,
  area,
  numberOfBedrooms,
  buildYear,
  sellPreference,
  connectToBroker,
  ownerSince,
  estimatedValue,
  valuationChange,
  salesProcess,
  bidValue,
  userEstateRelation,
  orgNumber,
  shareNumber,
  areEstateImagesShowableInPictureGallery,
  departmentId,
});
