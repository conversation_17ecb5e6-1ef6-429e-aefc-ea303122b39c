import { ObjectId } from 'mongodb';

export const estateEmployeeMock = {
  _id: new ObjectId('f3bec730001349ad94788dc5'),
  id: '9273512a-e8f6-41da-8453-eafb5ec6d81e',
  email: '<EMAIL>',
  password: 'sEcReT1234',
  name: 'Test Estate Employee',
  employeeId: 'b13054a4-91fa-4df1-ac87-052818db11d6',
  brokerRole: 'unk',
  image: {
    large: 'large.png',
    medium: 'medium.png',
    small: 'small.png',
  },
  slug: 'unk',
  title: 'unk',
  mobilePhone: '+36000000000',
  workPhone: '+36000000000',
  department: new ObjectId('4acddae0502e459da7c04a74'),
  departmentId: [1],
};
