import { Schema } from 'mongoose';
import { BrokerRole } from '../mongo/estate.mongoose-types';
import type { Employee } from './estate-employee';

export const employeeFactory = ({
  id = '',
  email = '',
  password = '',
  name = '',
  employeeId = '',
  brokerRole = BrokerRole.UNKNOWN,
  image = {
    large: '',
    medium: '',
    small: '',
  },
  slug = '',
  title = '',
  mobilePhone = '',
  workPhone = '',
  department = new Schema.Types.ObjectId(''),
  departmentId = [],
}: Partial<Employee> = {}): Employee => ({
  id,
  email,
  password,
  name,
  employeeId,
  brokerRole,
  image,
  slug,
  title,
  mobilePhone,
  workPhone,
  department,
  departmentId,
});
