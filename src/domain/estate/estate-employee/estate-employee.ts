import type { Schema } from 'mongoose';
import type { BrokerRole } from '../mongo/estate.mongoose-types';

export type Employee = {
  id: string;
  email: string;
  password: string;
  name: string;
  employeeId: string;
  brokerRole: BrokerRole;
  image: {
    large: string;
    medium: string;
    small: string;
  };
  slug: string;
  title: string;
  mobilePhone: string;
  workPhone: string;
  department: Schema.Types.ObjectId;
  departmentId: number[];
};
