export const estatePropertyType: { [key: string]: string } = {
  [0]: 'Unknown',
  [3]: 'Apartment',
  [1]: 'Detached',
  [4]: 'Townhouses',
  [2]: 'Semi Detached',
  [16]: 'Dorm',
  [12]: 'Cottage',
  [15]: 'Camping Out',
  [5]: 'Office',
  [21]: 'Commercial Lot',
  [17]: 'Room',
  [14]: 'Empty',
  [10]: 'Shop',
  [9]: 'Production',
  [7]: 'Storage',
  [19]: 'Combination Local',
  [11]: 'Farms',
  [20]: 'Apartment building',
  [22]: 'Shopping',
  [26]: 'Workshop',
  [6]: 'Garage',
  [8]: 'Hotel',
  [23]: 'Serving Local ',
  [28]: 'Instruction',
  [24]: 'Project',
  [13]: 'Other leisure',
  [18]: 'Second',
};

export const getEstatePropertyTypeString = (key: string | null): string => {
  if (!key) {
    return estatePropertyType[0];
  }
  return estatePropertyType[key] || estatePropertyType[0];
};

export const getEstatePropertyTypeKey = (value: string | undefined): string => {
  if (!value) {
    return estatePropertyType[0];
  }
  const item = Object.keys(estatePropertyType).find((key) => estatePropertyType[key] === value);
  if (!item) {
    return estatePropertyType[0];
  }
  return item;
};
