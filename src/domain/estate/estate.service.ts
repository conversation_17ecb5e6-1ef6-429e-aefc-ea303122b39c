import axios from 'axios';
import bluebird, { mapSeries } from 'bluebird';
import { addDays, addHours, differenceInCalendarDays, differenceInMilliseconds, subDays, subMonths } from 'date-fns';
import { flatten, flattenDeep, groupBy, isEmpty, isNil, map, orderBy, values } from 'lodash';
import { equals, last, uniqBy } from 'ramda';
import type { Unleash } from 'unleash-client';
import type { AwsS3Config, MapboxConfig } from '../../config';
import { FeatureFlag, config as envConfig } from '../../config';
import { ResourceNotFound } from '../../framework/errors/resource.errors';
import { logger } from '../../logger';
import type { LeadEstate } from '../../use-cases/broker/get-leads.use-case';
import type { Area } from '../area/area';
import type { AreaRepository } from '../area/area.repository';
import type { AWSS3Service, S3UploadInput } from '../aws/aws-s3.service';
import { S3ACLType, s3ImageKeyFactory } from '../aws/aws-s3.service';
import type { Broker } from '../broker/broker';
import type { ValuationObject } from '../eiendomsverdi/EiendomsverdiEstateValuationService';
import type { LandIdentificationMatrix } from '../land-identification/LandIdentificationMatrix';
import type { MapboxService } from '../mapbox/mapbox.service';
import { EstateBaseType, EstateTypeId } from '../otp/otp';
import {
  getFirstLastViewingDates,
  getRemainingPrivateViewingsCount,
  getRemainingViewingsCount,
  getStatusChangeDate,
} from '../recommendation-engine/utils';
import type { StorebrandAuditRepository } from '../storebrand/storebrand-audit.repository';
import type { RangeSelector } from '../user-options/user-options';
import type { User } from '../user/user';
import type { EstateSalesHistory } from '../valuation/EstateSalesHistoryEntry';
import type { ValuationService } from '../valuation/ValuationService';
import { createNordvikEstate } from './NordvikEstate';
import { createVitecEstate, getBaseLandIdentificationMatrixFromVitecEstate } from './VitecEstate';
import type {
  Estate,
  EstateImage,
  EstateImages,
  EstateImagesNordvikboligResponse,
  EstatePG,
  EstatePrice,
  EstateSummary,
  PremarketEstateSummary,
  SummarizableEstate,
} from './estate';
import { AdsAdStatus, AdsChannels, EstateType, EstateVitecStatus, UserEstateRelation } from './estate';
import type { EstateBid } from './estate-bid/estate-bid';
import type { EstateTimelineSellerService } from './estate-timeline/seller/estate-timeline-seller.service';
import type { EstateRepository } from './estate.repository';
import type {
  EstateMongooseRepository,
  EstateMongooseResponse,
  SortPredicate,
} from './mongo/estate.mogoose-repository';
import { SortField, SortMode } from './mongo/estate.mogoose-repository';
import type { BrokerIdWithRoles, EstateAddress, EstateBuilding, EstateMongoose } from './mongo/estate.mongoose-types';
import {
  AreaType,
  BrokerRole,
  CheckListValue,
  EstateMongooseAssignmentTypeGroups,
} from './mongo/estate.mongoose-types';
import type {
  VitecEstateExtension,
  VitecEstateExtensionUpdates,
} from './vitec-estate-extension/vitec-estate-extension';
import type { VitecEstateExtensionRepository } from './vitec-estate-extension/vitec-estate-repository';
import type { WealthManagementSummary, WealthManagementSummaryEntry } from './wealth-management/wealth-management';
import type { CombinedOutput, WealthManagementService } from './wealth-management/wealth-management.service';

export const estateTakeOverNotALongTimeAgoQuerySoEstateIsShowableToUserOnUI = {
  $or: [{ status: { $ne: EstateVitecStatus.OVERSOLD } }, { takeOverDate: { $not: { $lt: subDays(new Date(), 7) } } }],
};

export const extraBuyerQuerySoEstateIsShowableToUserOnUI = {
  ...estateTakeOverNotALongTimeAgoQuerySoEstateIsShowableToUserOnUI,
  status: {
    $in: [
      EstateVitecStatus.OVERSOLD,
      EstateVitecStatus.RESERVED,
      EstateVitecStatus.ARCHIVED,
      EstateVitecStatus.EXPIRED,
    ],
  },
};

export type EstateService = {
  getEstateFromVitec(vitecID: EstateMongoose['estateId']): Promise<EstateMongoose>;
  getActiveEstateFromVitecByLandIDMatrix(
    landIdentificationMatrixes: LandIdentificationMatrix,
    shareNumber?: string | null,
  ): Promise<EstateMongoose>;
  getUserEstates(opts: {
    userID: User['id'];
    phone: User['phoneNumber'];
    email: User['email'];
  }): Promise<EstateSummary[]>;
  getUserProspectEstates(opts: {
    userID: User['id'];
    phone: User['phoneNumber'];
    email: User['email'];
  }): Promise<EstateSummary[]>;
  getUserProspectEstate(opts: {
    estatePgOrVitecId: string;
    phoneNumber: User['phoneNumber'];
  }): Promise<EstateSummary | null>;
  getEstatePG(id: string): Promise<EstatePG>;
  getEstateStatusText(estate: EstateMongoose): string;
  getEstateImage(estate: EstateMongoose): Promise<EstateImage | null>;
  getEstateImages(estates: EstateMongoose[]): Promise<Map<string, EstateImage>>;
  getEstatePrice(estate: EstateMongoose, bids: EstateBid[]): EstatePrice;
  getEstatesByBrokerEmail(
    brokerEmail: string,
    fields: (keyof EstateMongoose)[],
    conditions?: Record<string, unknown>,
  ): Promise<EstateMongoose[]>;
  getEstatesByMainBrokerEmployeeId(
    employeeId: string,
    fields: (keyof EstateMongoose)[],
    estateVitecStatus?: number,
  ): Promise<EstateMongoose[]>;
  countEstates(conditions?: Record<string, unknown>): Promise<number>;
  getUserPGEstates(userID: User['id']): Promise<EstatePG[]>;
  getPremarketEstates(params: {
    priceRange: RangeSelector | null;
    livingAreaRange: RangeSelector | null;
    numberOfBedrooms: RangeSelector | null;
    postalCodes: string[];
    limit: number;
    statuses: number[];
    skip?: number;
    sortBy: SortPredicate[];
    propertyTypes: string[];
  }): Promise<PremarketEstateSummary[]>;
  getFavouritePremarketEstates(
    estateIDs: string[],
    statuses: number[],
    sortBy: SortPredicate[],
  ): Promise<PremarketEstateSummary[]>;
  createEstates(estatesToCreate: Partial<Estate>[]): Promise<EstatePG[]>;
  createInitialEstates(user: User, socialSecurityNumber: string): Promise<EstatePG[]>;
  getVitecEstateExtension(vitecID: string): Promise<VitecEstateExtension>;
  upsertVitecEstateExtension(vitecID: string, newVal: VitecEstateExtensionUpdates): Promise<VitecEstateExtension>;
  getVitecEstateExtensions(vitecIDs: string[]): Promise<VitecEstateExtension[]>;
  getUserEstateRelation(userPhone: string, estate: EstateMongoose): Promise<UserEstateRelation | null>;
  getVitecEstateValuation(
    estate: EstateMongoose,
  ): Promise<{ isBrokerValuation: boolean; valuation: number | null | undefined }>;
  getEiendomsverdiEstateWealthManagementSummaryEntry(
    user: User,
    estateSummary: EstateSummary,
  ): Promise<WealthManagementSummaryEntry | null>;
  getVitecEstateWealthManagementSummaryEntry(
    user: User,
    estateSummary: EstateSummary,
  ): Promise<WealthManagementSummaryEntry | null>;
  getWealthManagementSummary(user: User, estates: EstateSummary[]): Promise<WealthManagementSummary>;
  getEiendomsverdiEstatesByUserID(userID: EstatePG['userID']): Promise<EstatePG[]>;
  updateEiendomsverdiEstateByID(
    id: EstatePG['id'],
    userId: EstatePG['userID'],
    estate: Partial<EstatePG>,
  ): Promise<EstatePG>;
};

export const OFFMARKET = 'OFFMARKET';

const ownershipMap = new Map([
  [0, 'Eiet'],
  [1, 'Andel'],
  [2, 'Aksje'],
  [3, 'Obligasjon'],
  [4, 'Eierseksjon'],
]);
export const mapOwnershipToString = (ownershipIndicator: number): string => ownershipMap.get(ownershipIndicator) || '';

export const getEstateAddressText = (address: EstateAddress): string =>
  [address.streetAdress, address.zipCode, address.city].join(' ');

export const getEstateRelatedBrokerRoles = (estate: EstateMongoose | LeadEstate, broker: Broker): number[] =>
  estate.brokersIdWithRoles.filter((item) => item.employee?.email === broker.email).map((item) => item.brokerRole);

export function getUsableArea(buildings: EstateBuilding[]): number {
  // https://linear.app/nordvik/issue/NOR-8/changes-for-new-area-units-on-estates
  return getEstateArea(buildings, [AreaType.UsageAreaI, AreaType.UsageAreaE, AreaType.UsageAreaB]);
}

export function getBRA_i(buildings: EstateBuilding[]): number {
  // https://linear.app/nordvik/issue/NOR-8/changes-for-new-area-units-on-estates
  return getEstateArea(buildings, [AreaType.UsageAreaI]);
}

export function getEstateArea(buildings: EstateBuilding[], areaTypes: AreaType[]): number {
  return buildings.reduce((acc, building) => acc + getAreaForBuilding(building, areaTypes), 0);
}

export function getAreaForBuilding(building: EstateBuilding, areaTypes: AreaType[]): number {
  let sum = 0;
  building.buildingArea.forEach((area) => {
    if (areaTypes.includes(area.areaType)) {
      sum += area.areaInformation.reduce((acc, curr) => acc + (curr.areaSize ?? 0), 0);
    }
  });

  return sum;
}

export const estateServiceFactory = ({
  config,
  estateRepository,
  estateMongooseRepository,
  vitecEstateExtensionRepository,
  valuationService,
  areaRepository,
  awsS3Service,
  mapboxService,
  wealthManagementService,
  timelineService,
  storebrandAuditRepository,
  unleash,
}: {
  config: {
    awsS3Config: AwsS3Config;
    mapboxConfig: MapboxConfig;
  };
  estateRepository: EstateRepository;
  estateMongooseRepository: EstateMongooseRepository;
  vitecEstateExtensionRepository: VitecEstateExtensionRepository;
  valuationService: ValuationService;
  areaRepository: AreaRepository;
  mapboxService: MapboxService;
  wealthManagementService: WealthManagementService;
  timelineService: EstateTimelineSellerService;
  awsS3Service: AWSS3Service;
  storebrandAuditRepository: StorebrandAuditRepository;
  unleash: Unleash;
}): EstateService => {
  const getImageUrlByAddress = async (
    address: string,
    uploadParams: Omit<S3UploadInput, 'body'>,
  ): Promise<string | null> => {
    try {
      const geoCode = await mapboxService.getGeoCodeFromAddress(address);
      if (!geoCode) {
        return null;
      }
      const image = await mapboxService.getImagefromGeoCode({
        geoCode,
        styleName: config.mapboxConfig.styleID,
        zoom: config.mapboxConfig.zoom,
        userName: config.mapboxConfig.userName,
      });
      if (!image) {
        return null;
      }
      const imageUrl = await awsS3Service.uploadFile({
        key: uploadParams.key,
        body: image,
        bucket: uploadParams.bucket,
        acl: uploadParams.acl,
      });
      return imageUrl;
    } catch (e) {
      return null;
    }
  };

  const getVitecEstateExtension = async (vitecID: string): Promise<VitecEstateExtension> => {
    const extension = await vitecEstateExtensionRepository.getEstateExtensionById(vitecID);
    if (!extension) {
      throw new ResourceNotFound('errors.estateExtensionNotInSystem');
    }
    return extension;
  };

  const getVitecEstateExtensions = async (vitecIDs: string[]): Promise<VitecEstateExtension[]> =>
    vitecEstateExtensionRepository.getEstateExtensions(vitecIDs);

  const upsertVitecEstateExtension = async (
    vitecID: string,
    newVal: VitecEstateExtensionUpdates,
  ): Promise<VitecEstateExtension> => {
    const extension = await vitecEstateExtensionRepository.modifyEstateExtensionById(vitecID, newVal);
    return extension;
  };

  const getEstateFromVitec = async (vitecID: EstateMongoose['estateId']): Promise<EstateMongoose> => {
    const estate = await estateMongooseRepository.findEstateByVitecEstateId(vitecID);
    if (!estate) {
      throw new ResourceNotFound('errors.estateNotInSystem');
    }
    return estate;
  };

  const getActiveEstateFromVitecByLandIDMatrix = async (
    landIdentificationMatrix: LandIdentificationMatrix,
    shareNumber?: string | null,
  ): Promise<EstateMongoose> => {
    const fieldsToFilterOn: Record<string, unknown> = {
      matrikkel: { $elemMatch: landIdentificationMatrix },
      status: { $ne: EstateVitecStatus.OWN_ARCHIVED },
    };

    if (shareNumber) {
      fieldsToFilterOn['partOwnership.partNumber'] = Number(shareNumber);
    }

    const fields: (keyof EstateMongoose)[] = ['_id', 'estatePrice'];
    const estates = await estateMongooseRepository.findEstatesByPredicate(fields, fieldsToFilterOn, 1, undefined, [
      {
        sortField: SortField.STATUS,
        sortMode: SortMode.DESCENDING,
      },
    ]);
    return estates[0];
  };

  const getEstatePG = async (id: string): Promise<EstatePG> => {
    const localEstate = await estateRepository.getByID(id);
    if (!localEstate) {
      throw new ResourceNotFound('errors.estateNotInSystem');
    }
    return localEstate;
  };

  const getHistoryEvaluationMapImageForPGEstate = async (dto: EstatePG): Promise<SummarizableEstate> => {
    const history = await getEstateSalesHistory(dto.landIdentificationMatrix, dto.OrganizationNumber, dto.ShareNumber);
    const valuation = await getEstateEvaluation(dto.landIdentificationMatrix, dto.OrganizationNumber, dto.ShareNumber);
    const valuationChange = wealthManagementService.getValueIncrease({
      valuation: valuation?.valuation || null,
      latestSale: wealthManagementService.getLatestSale(history),
    });

    if (dto?.estateImage === null) {
      const img = await getImageUrlByAddress(dto.address, {
        key: s3ImageKeyFactory('eiendomsverdi-map-images', 'png'),
        bucket: config.awsS3Config.bucketName,
        acl: S3ACLType.PUBLIC_READ,
      });
      if (img) {
        await estateRepository.updateByID(dto.id, dto.userID, { estateImage: img });
        return createNordvikEstate(
          { ...dto, estateImage: img },
          history,
          valuation?.valuation || null,
          valuationChange,
        );
      }
    }
    return createNordvikEstate(dto, history, valuation?.valuation || null, valuationChange);
  };

  const mapEstateWithExtension = async (
    vEstate: EstateWithExtension,
    localEVEstates: SummarizableEstate[],
  ): Promise<SummarizableEstate> => {
    const estateInPg = localEVEstates.find((evEstate) =>
      equals(getBaseLandIdentificationMatrixFromVitecEstate(vEstate), evEstate.getBaseLandIdentificationMatrix()),
    );
    const timeline = timelineService.getTimeline(vEstate);
    if (!vEstate?.extension || (!vEstate.extension?.mapImageUrl && vEstate.extension?.triedToDownloadImage === false)) {
      const mapImageUrl = await getImageUrlByAddress(getEstateAddressText(vEstate.address), {
        key: s3ImageKeyFactory('vitec-map-images', 'png'),
        bucket: config.awsS3Config.bucketName,
        acl: S3ACLType.PUBLIC_READ,
      });
      if (mapImageUrl) {
        await upsertVitecEstateExtension(vEstate.estateId, { mapImageUrl, triedToDownloadImage: true });
        return createVitecEstate(
          {
            ...vEstate,
            defaultImage: {
              small: mapImageUrl,
              medium: mapImageUrl,
              large: mapImageUrl,
            },
          },
          timeline,
          vEstate.userEstateRelation,
          estateInPg?.toEstateSummary()?.shareNumber ?? null,
          estateInPg?.toEstateSummary()?.orgNumber ?? null,
          estateInPg?.toEstateSummary()?.id ?? null,
        );
      }
      await upsertVitecEstateExtension(vEstate.estateId, { mapImageUrl: null, triedToDownloadImage: true });
    }
    return createVitecEstate(
      vEstate,
      timeline,
      vEstate.userEstateRelation,
      estateInPg?.toEstateSummary()?.shareNumber ?? null,
      estateInPg?.toEstateSummary()?.orgNumber ?? null,
      estateInPg?.toEstateSummary()?.id ?? null,
    );
  };

  const getUserEstates = async ({
    userID,
    phone,
    email,
  }: {
    userID: User['id'];
    phone: User['phoneNumber'];
    email: User['email'];
  }): Promise<EstateSummary[]> => {
    const evEstatesInPgRaw = await estateRepository.getByUserID(userID);

    const [localEVEstates, sellingEstates, buyingEstates, brokerEstates] = await Promise.all([
      Promise.all(evEstatesInPgRaw.map(getHistoryEvaluationMapImageForPGEstate)),
      estateMongooseRepository.findEstatesBySellerPhoneOrEmail({
        email,
        phone,
        extraQuery: estateTakeOverNotALongTimeAgoQuerySoEstateIsShowableToUserOnUI,
      }),
      estateMongooseRepository.findEstatesByBuyerPhoneOrEmail({
        phone,
        email,
        extraQuery: extraBuyerQuerySoEstateIsShowableToUserOnUI,
      }),
      unleash.isEnabled(FeatureFlag.GET_BROKER_ESTATES_FOR_USER, {})
        ? estateMongooseRepository.findEstatesByBrokerPhone(
            phone,
            estateTakeOverNotALongTimeAgoQuerySoEstateIsShowableToUserOnUI,
          )
        : Promise.resolve([]),
    ]);

    const vitecEstates: (EstateMongooseResponse & {
      userEstateRelation?: UserEstateRelation;
    })[] = [
      ...sellingEstates.map((estate) => ({ ...estate, userEstateRelation: UserEstateRelation.SELLER })),
      ...buyingEstates.map((estate) => ({ ...estate, userEstateRelation: UserEstateRelation.BUYER })),
      ...brokerEstates.map((estate) => ({ ...estate, userEstateRelation: UserEstateRelation.SELLER })), // this is intended, we want to show the brokers the seller process
    ];

    const estateExtensions = await getVitecEstateExtensions(vitecEstates.map((e) => e.estateId));
    const estatesWithExtension: EstateWithExtension[] = vitecEstates.map((e) => ({
      ...e,
      extension: estateExtensions.find((ex) => ex.vitecID === e.estateId),
    }));

    const allVitecEstates = await bluebird.mapSeries(
      estatesWithExtension,
      async (vitecEstateWithExtension) => await mapEstateWithExtension(vitecEstateWithExtension, localEVEstates),
    );

    const uniqueLocalEVEstates = localEVEstates.filter(
      (localEVEstate) =>
        !allVitecEstates.some((vitecEstate) => vitecEstate.matchesLandIdentificationMatrix(localEVEstate)),
    );

    const deduplicatedVitecEstateSummaries = unleash.isEnabled(FeatureFlag.DEDUPLICATE_ESTATES, {})
      ? deduplicateEstates(allVitecEstates.map((e) => e.toEstateSummary()))
      : allVitecEstates.map((e) => e.toEstateSummary());

    const allEstateSummaries = [
      ...deduplicatedVitecEstateSummaries,
      ...uniqueLocalEVEstates.map((a) => a.toEstateSummary()),
    ];

    return orderBy(allEstateSummaries, ['streetAddress'], ['asc']);
  };

  const getUserProspectEstates = async (opts: {
    userID: User['id'];
    phone: User['phoneNumber'];
    email: User['email'];
  }): Promise<EstateSummary[]> => {
    const estates = await getUserEstates(opts);

    return estates.filter((estate) => {
      return !estate.vitecStatus; // null or EstateVitecStatus.REQUEST
    });
  };

  const getUserProspectEstate = async ({
    estatePgOrVitecId,
    phoneNumber,
  }: {
    estatePgOrVitecId: string;
    phoneNumber: string;
  }): Promise<EstateSummary | null> => {
    const evEstateInPgRaw = await estateRepository.getByID(estatePgOrVitecId);
    const localEVEstate = evEstateInPgRaw ? await getHistoryEvaluationMapImageForPGEstate(evEstateInPgRaw) : null;
    const vitecEstateRaw = await estateMongooseRepository.findEstateByVitecEstateId(estatePgOrVitecId);
    if (!vitecEstateRaw) {
      return localEVEstate?.toEstateSummary() || null;
    }

    const relation = await estateMongooseRepository.checkUserEstateRelation(phoneNumber, vitecEstateRaw);
    const vitecEstate: EstateMongooseResponse & {
      userEstateRelation: UserEstateRelation;
    } = {
      ...vitecEstateRaw,
      userEstateRelation: relation === UserEstateRelation.BUYER ? UserEstateRelation.BUYER : UserEstateRelation.SELLER, // this is intended, we want to show the brokers the seller process
    };
    const extension = await getVitecEstateExtension(vitecEstate.estateId);
    const estateWithExtension: EstateWithExtension = { ...vitecEstate, extension };
    const allVitecEstate = await mapEstateWithExtension(estateWithExtension, []);
    return allVitecEstate.toEstateSummary();
  };

  const getEstateSalesHistory = async (
    landIdentificationMatrix: LandIdentificationMatrix,
    organizationNumber: string | null,
    shareNumber: string | null,
  ): Promise<EstateSalesHistory> => {
    return await valuationService.getSaleHistory(landIdentificationMatrix, organizationNumber, shareNumber);
  };

  const getEstateEvaluation = async (
    landIdentificationMatrix: LandIdentificationMatrix,
    organizationNumber: EstatePG['OrganizationNumber'],
    shareNumber: EstatePG['ShareNumber'],
  ): Promise<ValuationObject | null> => {
    return await valuationService.getEstateEvaluation(landIdentificationMatrix, {
      organizationNumber,
      shareNumber,
    });
  };

  const getEstateImage = async (estate: EstateMongoose): Promise<EstateImage | null> => {
    if (!estate.defaultImage) {
      const image = await estateMongooseRepository.getEstateImage(estate._id.toString());
      if (!image) {
        return null;
      }
      return { small: image.small, medium: image.medium, large: image.large };
    }
    return { small: estate.defaultImage.small, medium: estate.defaultImage.medium, large: estate.defaultImage.large };
  };

  const getEstatePrice = (estate: EstateMongoose, bids: EstateBid[]): Estate['price'] => {
    const lastBid = last(bids);
    const lastBidValue = lastBid?.amount || 0;
    if (estate.status === EstateVitecStatus.REQUEST || estate.status === EstateVitecStatus.PREPARATION) {
      if (lastBid?.accepted) {
        return { lastBidValue, value: estate.estatePrice.priceSuggestion, label: 'Last accepted bid' };
      }
      return { lastBidValue, value: estate.estatePrice.priceSuggestion, label: 'Asking price' };
    }
    return { lastBidValue, value: estate.estatePrice.soldPrice, label: 'Sold for' };
  };

  const getEstatesByBrokerEmail = async (
    brokerEmail: string,
    fields: (keyof EstateMongoose)[],
    conditions: Record<string, unknown> = {},
  ): Promise<EstateMongoose[]> => {
    return estateMongooseRepository.findEstatesByPredicate(fields, {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'brokersIdWithRoles.employee.email': brokerEmail,
      ...conditions,
    });
  };

  const getEstatesByMainBrokerEmployeeId = async (
    employeeId: string,
    fields: (keyof EstateMongoose)[],
    estateVitecStatus?: number,
  ): Promise<EstateMongoose[]> => {
    let conditions: Record<string, unknown> = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'brokersIdWithRoles.employeeId': employeeId,
      brokerRole: BrokerRole.MAIN_BROKER,
    };
    if (estateVitecStatus) {
      conditions = { ...conditions, status: estateVitecStatus };
    }
    return estateMongooseRepository.findEstatesByPredicate(fields, conditions);
  };

  const countEstates = async (conditions: Record<string, unknown> = {}): Promise<number> => {
    return estateMongooseRepository.countEstates(conditions);
  };

  const getUserPGEstates = async (userID: User['id']): Promise<EstatePG[]> => estateRepository.getByUserID(userID);

  const getEstateImages = async (estates: EstateMongoose[]): Promise<Map<string, EstateImage>> => {
    const estateIDs = estates.map((estate) => estate._id.toString());
    const images = await estateMongooseRepository.getImagesForEstates(estateIDs);
    const uniqueImages = uniqBy(
      (image) => image.estate,
      images.sort((a, b) => a.imageSequence - b.imageSequence),
    );

    return new Map(
      uniqueImages.map((image) => [
        image.estate.toString(),
        {
          small: image.url.small,
          medium: image.url.medium,
          large: image.url.large,
        },
      ]),
    );
  };

  const getDataForPremarketEstates = async (estates: EstateMongoose[]): Promise<PremarketEstateSummary[]> => {
    const estatesWithAreas = await Promise.all(
      estates.map(async (estate) => {
        return {
          ...estate,
          area: await areaRepository.getAreaByPostalCode(estate.address.zipCode),
        };
      }),
    );

    const estateExtensions = await getVitecEstateExtensions(estates.map((e) => e.estateId));
    const estatesWithExtension: (EstateMongoose & {
      extension?: VitecEstateExtension;
      area: Area | null;
    })[] = estatesWithAreas.map((e) => ({ ...e, extension: estateExtensions.find((ex) => ex.vitecID === e.estateId) }));

    const estateImages = await getMultipleEstateImages(estatesWithExtension);

    const estatesWithImages = await bluebird.mapSeries(estatesWithExtension, async (estate) => {
      // TODO: investigate if status check is necessary
      if (estate.status === EstateVitecStatus.FOR_SALE) {
        const estateImage = estateImages
          .find((e) => e.estateId === estate.estateId)
          ?.images.find((i) => i.imageSequence === 1);
        if (estateImage) {
          return {
            ...estate,
            defaultImage: {
              large: estateImage.url.large,
              medium: estateImage.url.medium,
              small: estateImage.url.small,
            },
          };
        }
      }

      if (estate.extension?.mapImageUrl) {
        return {
          ...estate,
          defaultImage: {
            large: estate.extension.mapImageUrl,
            medium: estate.extension.mapImageUrl,
            small: estate.extension.mapImageUrl,
          },
        };
      }
      if (estate.extension?.triedToDownloadImage === true && estate.extension?.mapImageUrl === null) {
        return {
          ...estate,
          defaultImage: {
            large: '',
            medium: '',
            small: '',
          },
        };
      }
      const mapImageUrl = await getImageUrlByAddress(getEstateAddressText(estate.address), {
        key: s3ImageKeyFactory('vitec-map-images', 'png'),
        bucket: config.awsS3Config.bucketName,
        acl: S3ACLType.PUBLIC_READ,
      });
      if (!mapImageUrl) {
        await upsertVitecEstateExtension(estate.estateId, { mapImageUrl: null, triedToDownloadImage: true });
        return {
          ...estate,
          defaultImage: {
            large: '',
            medium: '',
            small: '',
          },
        };
      }
      await upsertVitecEstateExtension(estate.estateId, { mapImageUrl, triedToDownloadImage: true });
      return {
        ...estate,
        defaultImage: {
          large: mapImageUrl,
          medium: mapImageUrl,
          small: mapImageUrl,
        },
      };
    });

    return estatesWithImages.map(transformToPremarketEstateSummaryData);
  };

  const getPremarketEstates = async (params: {
    priceRange: RangeSelector | null;
    livingAreaRange: RangeSelector | null;
    numberOfBedrooms: RangeSelector | null;
    postalCodes: string[];
    limit: number;
    statuses: number[];
    skip?: number;
    sortBy: SortPredicate[];
    propertyTypes: string[];
  }): Promise<PremarketEstateSummary[]> => {
    const fields: (keyof EstateMongoose)[] = [
      '_id',
      'estateId',
      'estateType',
      'address',
      'status',
      'heading',
      'soldDate',
      'brokersIdWithRoles',
      'ownership',
      'estatePrice',
      'estateSize',
      'buildings',
      'employeeId',
      'noOfBedRooms',
      'constructionYear',
      'expireDate',
      'ads',
      'assignmentNum',
    ];

    const filterConditions = generateConditions([
      // filter out estates where no price defined
      {
        checkIf: true, // always
        mongoField: 'estatePrice',
        mongoCond: { $ne: null },
      },
      {
        checkIf: true,
        mongoField: 'checkList.checkListItems',
        mongoCond: {
          $not: {
            $elemMatch: {
              tags: { $in: [OFFMARKET] },
              value: CheckListValue.YES,
            },
          },
        },
      },
      {
        checkIf: true,
        mongoField: 'expireDate',
        mongoCond: {
          $gt: addHours(new Date(), 2),
        },
      },
      {
        checkIf: !isEmpty(params.postalCodes),
        mongoField: 'address.zipCode',
        mongoCond: { $in: params.postalCodes },
      },
      {
        checkIf: !isEmpty(params.propertyTypes),
        mongoField: 'estateTypeId',
        mongoCond: { $in: flattenDeep(params.propertyTypes.map(mapPropertyTypeStringFromUIToTypeId)) },
      },
      {
        checkIf: !isNil(params.priceRange),
        mongoField: 'estatePrice.priceSuggestion',
        mongoCond: { $gte: params.priceRange?.from, $lte: params.priceRange?.to },
      },
      {
        checkIf: !isNil(params.livingAreaRange),
        mongoField: 'estateSize.primaryRoomArea',
        mongoCond: {
          $gte: params.livingAreaRange?.from,
          $lte: params.livingAreaRange?.to,
        },
        orSecondaryMongoField: 'estateBaseType',
        orSecondaryMongoCond: {
          $eq: EstateBaseType.PLOT,
        },
      },
      {
        checkIf: !isNil(params.numberOfBedrooms),
        mongoField: 'noOfBedRooms',
        mongoCond: { $gte: params.numberOfBedrooms?.from },
      },
      {
        checkIf: true,
        mongoField: 'assignmentTypeGroup',
        mongoCond: {
          $nin: [EstateMongooseAssignmentTypeGroups.SETTLEMENT, EstateMongooseAssignmentTypeGroups.VALUATION],
        },
      },
      {
        checkIf: true,
        mongoField: 'ownAssignmentType',
        mongoCond: { $ne: 'Konsulentoppdrag' },
      },
    ]);

    const conditions: { [id: string]: Record<string, unknown> | Record<string, unknown>[] } = {
      $and: [
        {
          $or: params.statuses.map((status) =>
            status === EstateVitecStatus.FOR_SALE
              ? {
                  $and: [
                    { status: EstateVitecStatus.FOR_SALE },
                    {
                      ads: {
                        $elemMatch: {
                          channel: AdsChannels.PUBLISH,
                          adStatus: AdsAdStatus.LIVE,
                        },
                      },
                    },
                  ],
                }
              : { status },
          ),
        },
        filterConditions,
      ],
    };

    const estates = await estateMongooseRepository.findEstatesByPredicate(
      fields,
      conditions,
      params.limit,
      params.skip,
      params.sortBy,
    );

    return getDataForPremarketEstates(estates);
  };

  const getFavouritePremarketEstates = async (
    estateIDs: string[],
    statuses: number[],
    sortBy: SortPredicate[],
  ): Promise<PremarketEstateSummary[]> => {
    const fields: (keyof EstateMongoose)[] = [
      '_id',
      'estateId',
      'estateType',
      'address',
      'status',
      'heading',
      'soldDate',
      'brokersIdWithRoles',
      'ownership',
      'estatePrice',
      'estateSize',
      'buildings',
      'employeeId',
      'noOfBedRooms',
      'constructionYear',
      'expireDate',
      'assignmentNum',
    ];

    const favoriteCondition = generateConditions([
      // filter out estates where no price defined
      {
        checkIf: true, // always
        mongoField: 'estatePrice',
        mongoCond: { $ne: null },
      },
      {
        checkIf: true,
        mongoField: 'estateId',
        mongoCond: { $in: estateIDs },
      },
      {
        checkIf: true,
        mongoField: 'status',
        mongoCond: { $in: [1, 2] },
      },
      {
        checkIf: true,
        mongoField: 'expireDate',
        mongoCond: {
          $gt: addHours(new Date(), 2),
        },
      },
    ]);

    const conditions: Record<string, unknown> = {
      $and: [
        {
          $or: statuses.map((status) => ({
            status,
          })),
        },
        favoriteCondition,
      ],
    };

    const favEstates = await estateMongooseRepository.findEstatesByPredicate(fields, conditions, 0, 0, sortBy);

    return getDataForPremarketEstates(favEstates);
  };

  const createInitialEstates = async (user: User, socialSecurityNumber: string): Promise<EstatePG[]> => {
    const currentlyOwnedEstates = await valuationService.getCreatableEstates(user.id, socialSecurityNumber);
    const estatesToCreate = currentlyOwnedEstates.map((estate) => estate.toEstateCreatePayload(user));
    return createEstates(estatesToCreate);
  };

  const createEstates = async (estatesToCreate: Partial<EstatePG>[]): Promise<EstatePG[]> => {
    const estatesWithImages = await mapSeries(estatesToCreate, async (estate) => {
      const imageUrl = await getImageUrlByAddress(estate.address || '', {
        key: s3ImageKeyFactory('eiendomsverdi-map-images', 'png'),
        bucket: config.awsS3Config.bucketName,
        acl: S3ACLType.PUBLIC_READ,
      });
      if (!imageUrl) {
        return {
          ...estate,
          imageUrl: null,
        };
      }
      return {
        ...estate,
        imageUrl,
      };
    });
    return estateRepository.createMany(estatesWithImages);
  };

  const getUserEstateRelation = async (
    userPhone: string,
    estate: EstateMongoose,
  ): Promise<UserEstateRelation | null> => {
    return estateMongooseRepository.checkUserEstateRelation(userPhone, estate);
  };

  const getEiendomsverdiEstateWealthManagementSummaryEntry = async (
    user: User,
    estateSummary: EstateSummary,
  ): Promise<WealthManagementSummaryEntry | null> => {
    const estate = await getEstatePG(estateSummary.pgEstateId ?? estateSummary.id);

    const eiendomsverdiValuation = await valuationService.getEstateEvaluation(estate.landIdentificationMatrix, {
      organizationNumber: estate.OrganizationNumber,
      shareNumber: estate.ShareNumber,
    });

    const eiendomsverdiSalesHistory = await valuationService.getSaleHistory(
      estate.landIdentificationMatrix,
      estate.OrganizationNumber,
      estate.ShareNumber,
    );

    const combinedOutput = wealthManagementService.getCombinedCalculations({
      valuation: eiendomsverdiValuation?.valuation ?? null,
      salesHistory: eiendomsverdiSalesHistory,
      totalIncome: user?.annualIncome ?? null,
      otherDebt: user?.sumOfOtherLoans ?? null,
      interestRate: estate.interestRate ?? null,
      originalLoan: estate.originalMortgage ?? null,
      livingArea: estate.livingArea ?? 0,
      isOwnProperty: estate.ownership === 'Eiet' || estate.ownership === 'Selveier',
      loanYears: estate.mortgageYears,
    });

    return transformToWealthManagementSummaryEntry(
      estate.id,
      estateSummary.streetAddress,
      eiendomsverdiValuation?.valuation ?? null,
      false,
      combinedOutput,
      user.annualIncome,
      user.sumOfOtherLoans,
      estate.interestRate,
      estate.originalMortgage,
      estate.mortgageYears,
    );
  };

  const getVitecEstateValuation = async (
    estate: EstateMongoose,
    shareNumber?: string | null,
    orgNumber?: string | null,
  ): Promise<{ isBrokerValuation: boolean; valuation: number | null | undefined }> => {
    const vitecValuation = estate.estatePrice
      ? estate.estatePrice.priceSuggestion || estate.estatePrice.estimatedValue
      : 0;

    if (!vitecValuation || estate.estatePrice.changedDate < subMonths(new Date(), 7)) {
      const eiendomsverdiValuation = await valuationService.getEstateEvaluation(estate.matrikkel[0], {
        organizationNumber: orgNumber ?? null,
        shareNumber: shareNumber ?? null,
      });

      const useEiendomsverdiValuation = eiendomsverdiValuation?.valuation !== null;

      return {
        isBrokerValuation: !useEiendomsverdiValuation,
        valuation: useEiendomsverdiValuation ? eiendomsverdiValuation?.valuation : vitecValuation,
      };
    } else {
      return {
        isBrokerValuation: true,
        valuation: vitecValuation,
      };
    }
  };

  const getVitecEstateWealthManagementSummaryEntry = async (
    user: User,
    estateSummary: EstateSummary,
  ): Promise<WealthManagementSummaryEntry | null> => {
    const estate = await estateMongooseRepository.findEstateByVitecEstateId(estateSummary.id);

    if (!estate) {
      return null;
    }

    const estateExtension = await getVitecEstateExtension(estate.estateId).catch(() => null);

    const eiendomsverdiOrVitecValuation = await getVitecEstateValuation(
      estate,
      estateSummary.shareNumber,
      estateSummary.orgNumber,
    );
    const eiendomsverdiSalesHistory = await valuationService.getSaleHistory(
      estate.matrikkel[0],
      estateSummary.orgNumber,
      estateSummary.shareNumber,
    );

    const combinedOutput = wealthManagementService.getCombinedCalculations({
      valuation: eiendomsverdiOrVitecValuation.valuation ? eiendomsverdiOrVitecValuation.valuation : null,
      salesHistory: eiendomsverdiSalesHistory,
      totalIncome: user?.annualIncome ?? null,
      otherDebt: user?.sumOfOtherLoans ?? null,
      interestRate: estateExtension?.interestRate ?? null,
      originalLoan: estateExtension?.originalMortgage ?? null,
      livingArea: getLivingArea(estate),
      isOwnProperty: estate.ownership === 0,
      loanYears: estateExtension?.mortgageYears ?? null,
    });

    return transformToWealthManagementSummaryEntry(
      estate.estateId,
      estateSummary.streetAddress,
      eiendomsverdiOrVitecValuation.valuation ?? null,
      eiendomsverdiOrVitecValuation.isBrokerValuation,
      combinedOutput,
      user.annualIncome,
      user.sumOfOtherLoans,
      estateExtension?.interestRate ?? null,
      estateExtension?.originalMortgage ?? null,
      estateExtension?.mortgageYears ?? null,
    );
  };

  const getWealthManagementSummary = async (user: User, estates: EstateSummary[]): Promise<WealthManagementSummary> => {
    const wealthManagementSummaryEntries = (
      await Promise.all(
        estates.map(async (summary) => {
          if (summary.type === EstateType.OWNED) {
            return getEiendomsverdiEstateWealthManagementSummaryEntry(user, summary);
          } else {
            return getVitecEstateWealthManagementSummaryEntry(user, summary);
          }
        }),
      )
    ).filter((entry) => entry !== null) as WealthManagementSummaryEntry[];

    const aggregation =
      wealthManagementSummaryEntries.length > 0
        ? aggregateWealthManagementSummaryEntries(wealthManagementSummaryEntries)
        : null;

    const numberOfStorebrandLeads = await storebrandAuditRepository.getNumberOfSentLeadsByUser(
      user.id,
      subMonths(new Date(), 3),
    );

    const hasSentStorebrandLead = numberOfStorebrandLeads > 0;

    return {
      hasSentStorebrandLead,
      estates: wealthManagementSummaryEntries,
      aggregation,
    };
  };

  const getEiendomsverdiEstatesByUserID = async (userID: EstatePG['userID']): Promise<EstatePG[]> => {
    return estateRepository.getByUserID(userID);
  };

  const updateEiendomsverdiEstateByID = async (
    id: EstatePG['id'],
    userId: EstatePG['userID'],
    estate: Partial<EstatePG>,
  ): Promise<EstatePG> => {
    return estateRepository.updateByID(id, userId, estate);
  };

  return {
    getEstateFromVitec,
    getActiveEstateFromVitecByLandIDMatrix,
    getUserEstates,
    getUserProspectEstates,
    getUserProspectEstate,
    getEstatePG,
    getEstateStatusText,
    getEstateImage,
    getEstatePrice,
    getEstatesByBrokerEmail,
    getEstatesByMainBrokerEmployeeId,
    countEstates,
    getUserPGEstates,
    getEstateImages,
    getPremarketEstates,
    createEstates,
    createInitialEstates,
    getVitecEstateExtension,
    upsertVitecEstateExtension,
    getVitecEstateExtensions,
    getFavouritePremarketEstates,
    getUserEstateRelation,
    getVitecEstateValuation,
    getEiendomsverdiEstateWealthManagementSummaryEntry,
    getVitecEstateWealthManagementSummaryEntry,
    getWealthManagementSummary,
    getEiendomsverdiEstatesByUserID,
    updateEiendomsverdiEstateByID,
  };
};

const isMainBroker = ({ brokerRole }: { brokerRole: BrokerRole }): boolean => brokerRole === BrokerRole.MAIN_BROKER;

const mapToPreMarketBrokerData = (
  broker: BrokerIdWithRoles[0],
): {
  name: string;
  image: EstateImage;
  email: string;
  mobilePhone: string;
  workPhone: string;
} => ({
  name: broker.employee?.name ? broker.employee.name : '',
  image: broker.employee?.image ? broker.employee.image : { small: '', medium: '', large: '' },
  email: broker.employee?.email ? broker.employee.email : '',
  mobilePhone: broker.employee?.mobilePhone ? broker.employee.mobilePhone : '',
  workPhone: broker.employee?.workPhone ? broker.employee.workPhone : '',
});

const transformToPremarketEstateSummaryData = (
  estate: EstateMongoose & { area: Area | null },
): PremarketEstateSummary => ({
  id: estate.estateId,
  status: estate.status,
  heading: estate.status === 2 ? estate.heading : '',
  address: estate.status === 2 ? [estate.address.apartmentNumber, estate.address.streetAdress].join(' ').trim() : '',
  propertyType: estate.estateType,
  postalCode: [estate.address.zipCode, estate.area?.name].join(' ').trim(),
  ownership: mapOwnershipToString(estate.ownership),
  estimatedPrice: estate.estatePrice ? estate.estatePrice.estimatedValue || estate.estatePrice.priceSuggestion : 0,
  numberOfBedrooms: estate.noOfBedRooms,
  livingArea: getLivingArea(estate),
  broker: estate.brokersIdWithRoles.filter(isMainBroker).map(mapToPreMarketBrokerData)[0] || null,
  image: estate.defaultImage.large ? estate.defaultImage : null,
  builtYear: estate.constructionYear,
  expireDate: estate.expireDate,
  assignmentNum: estate.assignmentNum,
});

export function getLivingArea(estate: EstateMongoose): number {
  return getBRA_i(estate.buildings ?? []) ?? estate.estateSize.primaryRoomArea;
}

export function getEstateStatusText(estate: EstateMongoose, now: number = Date.now()): string {
  const soldStatuses = [EstateVitecStatus.OVERSOLD, EstateVitecStatus.RESERVED, EstateVitecStatus.ARCHIVED];
  if (soldStatuses.includes(estate.status)) {
    return 'Selges';
  }

  if (EstateVitecStatus.PREPARATION === estate.status) {
    return 'Som forberedelse til å bli markedsført';
  }

  if (estate.status === EstateVitecStatus.FOR_SALE) {
    const { firstViewingDate, lastViewingDate } = getFirstLastViewingDates(estate.showings);
    const isFirstViewingFuture = firstViewingDate && differenceInMilliseconds(firstViewingDate, now) > 0;
    if (!firstViewingDate || !lastViewingDate || isFirstViewingFuture) {
      return getMarketingStatusText(estate, now);
    }

    const isFirstViewingPast = differenceInMilliseconds(firstViewingDate, now) < 0;
    const isLastViewingFuture = differenceInMilliseconds(lastViewingDate, now) > 0;
    if (isFirstViewingPast && isLastViewingFuture) {
      return getShowingCountStatusText(estate, now);
    }

    const isLastViewingPast = differenceInMilliseconds(lastViewingDate, now) < 0;
    if (isLastViewingPast) {
      return getSimpleMarketingOrBettingRoundStatusText(lastViewingDate, now);
    }

    return '';
  }

  return '';
}

function getMarketingStatusText(estate: EstateMongoose, now: number): string {
  const dateOfMarketingPhaseStart =
    getStatusChangeDate(estate.statusChanges, EstateVitecStatus.FOR_SALE) || estate.finnPublishDate;

  if (dateOfMarketingPhaseStart) {
    const daysSinceMarketingPhaseStart = differenceInCalendarDays(now, dateOfMarketingPhaseStart);
    return `Dag ${daysSinceMarketingPhaseStart} av markedsføringsfasen`;
  }

  return '';
}

function getShowingCountStatusText(estate: EstateMongoose, now: number): string {
  const remainingViewingsCount = getRemainingViewingsCount(estate.showings, new Date(now));
  const remainingPrivateViewingsCount = getRemainingPrivateViewingsCount(estate.activities, new Date(now));
  return `${remainingViewingsCount + remainingPrivateViewingsCount} flere visninger planlagt`;
}

function getSimpleMarketingOrBettingRoundStatusText(lastViewingDate: Date, now: number): string {
  const isLastViewingLessThanOneWeekPast = differenceInMilliseconds(addDays(lastViewingDate, 7), now) > 0;
  if (isLastViewingLessThanOneWeekPast) {
    return 'Budrunde';
  }

  return 'Markedsføring';
}

const generateConditions = (
  conditions: {
    checkIf: boolean;
    mongoField: string;
    mongoCond: Record<string, unknown>;
    orSecondaryMongoField?: string;
    orSecondaryMongoCond?: Record<string, unknown>;
  }[],
): Record<string, unknown> => {
  const allConditionsArray = conditions.reduce<Record<string, unknown>[]>((cache, cond) => {
    if (cond.checkIf && cond.mongoField && cond.mongoCond && cond.orSecondaryMongoCond && cond.orSecondaryMongoField) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      return [
        ...cache,
        { $or: [{ [cond.mongoField]: cond.mongoCond }, { [cond.orSecondaryMongoField]: cond.orSecondaryMongoCond }] },
      ];
    }
    if (cond.checkIf && cond.mongoField && cond.mongoCond) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      return [...cache, { [cond.mongoField]: cond.mongoCond }];
    }
    return cache;
  }, []);
  return { $and: allConditionsArray };
};

export const transformToWealthManagementSummaryEntry = (
  estateId: string,
  streetAddress: string,
  valuation: number | null,
  isRecentBrokerValuation: boolean,
  combinedOutput: CombinedOutput,
  annualIncome: number | null,
  sumOfOtherLoans: number | null,
  interestRate: number | null,
  originalMortgage: number | null,
  mortgageYears: number | null,
): WealthManagementSummaryEntry => ({
  estateId,
  streetAddress,
  totalEstimatedValue: valuation,
  areFinancialDetailsFilled:
    annualIncome !== null &&
    sumOfOtherLoans !== null &&
    interestRate !== null &&
    originalMortgage !== null &&
    mortgageYears !== null,
  hasRecentBrokerValuation: isRecentBrokerValuation,
  mortgage: {
    percentage:
      combinedOutput.valuationComponents !== null ? combinedOutput.valuationComponents.mortgage.percentage : null,
    value: combinedOutput.valuationComponents !== null ? combinedOutput.valuationComponents.mortgage.value : null,
  },
  equity: {
    percentage:
      combinedOutput.valuationComponents !== null ? combinedOutput.valuationComponents.equity.percentage : null,
    value: combinedOutput.valuationComponents !== null ? combinedOutput.valuationComponents.equity.value : null,
  },
  value: {
    initial: combinedOutput.purchasePrice,
    current: valuation,
    increasePercentage: combinedOutput.valueIncrease.increasePercentage,
    increaseValue: combinedOutput.valueIncrease.increaseValue,
  },
  storebrandSaving: combinedOutput.savingsBasedOnInterestRateDifference,
  extendedMortgage: combinedOutput.extendedMortgage,
  moreExpensiveProperty: combinedOutput.moreExpensiveProperty,
  affordMoreSpace: combinedOutput.affordMoreSpace,
});

export const aggregateWealthManagementSummaryEntries = (
  wealthManagementSummaryEntries: WealthManagementSummaryEntry[],
): WealthManagementSummaryEntry => {
  return wealthManagementSummaryEntries.reduce((acc, entry) => {
    const totalEstimatedValue = addWealthManagementField(acc.totalEstimatedValue, entry.totalEstimatedValue);
    const initialValue = addWealthManagementField(acc.value.initial, entry.value.initial);
    const mortgageValue = addWealthManagementField(acc.mortgage.value, entry.mortgage.value);
    const equityValue = addWealthManagementField(acc.equity.value, entry.equity.value);
    const increaseValue = addWealthManagementField(acc.value.increaseValue, entry.value.increaseValue);

    return {
      totalEstimatedValue,
      mortgage: {
        value: mortgageValue,
        percentage: totalEstimatedValue !== 0 ? mortgageValue / totalEstimatedValue : null,
      },
      equity: {
        value: equityValue,
        percentage: totalEstimatedValue !== 0 ? equityValue / totalEstimatedValue : null,
      },
      value: {
        initial: initialValue,
        current: addWealthManagementField(acc.value.current, entry.value.current),
        increasePercentage: initialValue !== 0 ? increaseValue / initialValue : null,
        increaseValue,
      },
      storebrandSaving: addWealthManagementField(acc.storebrandSaving, entry.storebrandSaving),
      extendedMortgage: addWealthManagementField(acc.extendedMortgage, entry.extendedMortgage),
      moreExpensiveProperty: addWealthManagementField(acc.moreExpensiveProperty, entry.moreExpensiveProperty),
      affordMoreSpace: addWealthManagementField(acc.affordMoreSpace, entry.affordMoreSpace),
    };
  });
};

const addWealthManagementField = (lhs: number | null, rhs: number | null): number => {
  return (lhs || 0) + (rhs || 0);
};

type EstateWithExtension = EstateMongooseResponse & {
  extension?: VitecEstateExtension;
  userEstateRelation?: UserEstateRelation;
};

export const deduplicateEstates = (inputEstates: EstateSummary[]): EstateSummary[] => {
  const groupedEstates = groupBy(inputEstates, 'userEstateRelation');
  return flatten(map(groupedEstates, (estates) => deduplicateEstatesByAddress(estates)));
};

const deduplicateEstatesByAddress = (inputEstates: EstateSummary[]): EstateSummary[] => {
  const groupedEstates = groupBy(inputEstates, 'address');
  const groupedSortedEstates = map(groupedEstates, (estates) =>
    orderBy(estates, ['vitecStatus', (estate) => new Date(estate.createdDate || '1900-01-01')], ['desc', 'desc']),
  );
  const filteredEstates = map(groupedSortedEstates, (e) => e[0]);
  return filteredEstates;
};

const getMultipleEstateImages = async (estates: EstateMongoose[]): Promise<EstateImages[]> => {
  const queryStrings = estates
    .map((e, index) => `estate${index}: estate(id: "${e.estateId}") { estateId, images { url imageSequence } }`)
    .join(' ');
  const query = encodeURIComponent(`{ ${queryStrings} }`);
  const estateImagesResponse = await axios.get<EstateImagesNordvikboligResponse>(
    `${envConfig.nordvikboligApiUrl}/graphql?query=${query}`,
    {
      headers: {
        'Content-Type': 'application/json',
      },
      validateStatus: () => true,
    },
  );

  if (estateImagesResponse.status !== 200) {
    logger.error('Failed to get estate images from nordvikbolig api.');
    return [];
  }

  return values(estateImagesResponse.data.data).filter(Boolean);
};

export const mapPropertyTypeStringFromUIToTypeId = (propTypeName: string): EstateTypeId[] => {
  switch (propTypeName) {
    case 'Enebolig':
      return [EstateTypeId.ENEBOLIG];
    case 'Rekkehus':
      return [EstateTypeId.REKKEHUS];
    case 'Tomannsbolig':
      return [EstateTypeId.TOMMANSBOLIG];
    case 'Tomt':
      return [EstateTypeId.TOMT, EstateTypeId.NAERINGSTOMT];
    case 'Fritidsbolig':
      return [EstateTypeId.FRITIDSEIENDOM];
    case 'Andelsleilighet':
    case 'Aksjeleilighet':
    case 'Eierseksjon':
    case 'Selveierleilighet':
      return [
        EstateTypeId.SELVEIERLEILIGHET,
        EstateTypeId.ANDELSLEILIGHET,
        EstateTypeId.AKSJELEILIGHET,
        EstateTypeId.OBLIGASJONSLEILIGHET,
      ];
  }
  return [];
};
