import { estatePGFixtureFactory } from '../../framework/test/fixtures/entities/estatePGFixtureFactory';
import { userFixtureFactory } from '../../framework/test/fixtures/entities/userFixtureFactory';
import { estateSalesHistoryFixtureFactory } from '../../framework/test/fixtures/misc/estateSalesHistoryFixtureFactory';
import type { User } from '../user/user';
import type { EstateSalesHistory } from '../valuation/EstateSalesHistoryEntry';
import type { EstatePG, SummarizableEstate } from './estate';
import { createNordvikEstate } from './NordvikEstate';

describe('NordvikEstate', () => {
  describe('given a PG estate and a sales history', () => {
    let expectedUser: User;
    let expectedEstatePG: EstatePG;
    let expectedEstateSalesHistory: EstateSalesHistory;
    let expectedNordvikEstate: SummarizableEstate;
    let expectedEstateValuation: 100000;
    let expectedValuationChange: { increasePercentage: 21; increaseValue: 122 };

    beforeEach(() => {
      expectedUser = userFixtureFactory();
      expectedEstatePG = estatePGFixtureFactory(expectedUser);
      expectedEstateSalesHistory = estateSalesHistoryFixtureFactory();
      expectedNordvikEstate = createNordvikEstate(
        expectedEstatePG,
        expectedEstateSalesHistory,
        expectedEstateValuation,
        expectedValuationChange,
      );
    });

    describe('#getBaseLandIdentificationMatrix', () => {
      describe('when called', () => {
        it('should return the proper base land identification matrix', () => {
          expect(expectedNordvikEstate.getBaseLandIdentificationMatrix()).toMatchObject({
            knr: 1,
            gnr: 2,
            bnr: 3,
            fnr: 4,
            snr: 5,
          });
        });
      });
    });

    describe('#toEstateSummary', () => {
      describe('when called', () => {
        it('should put createdAt into createdDate prop as it is very important in deduplicating estates', () => {
          expect(expectedNordvikEstate.toEstateSummary()).toMatchObject({
            createdDate: new Date(0).toISOString(),
          });
        });
      });
    });
  });
});
