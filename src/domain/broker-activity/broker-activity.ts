import type { Resource } from '../../framework/sequelize/resource';
import type { EstateMongoose } from '../estate/mongo/estate.mongoose-types';
import type { BrokerActivityModel } from './broker-activity.model';

export enum BrokerActivityType {
  SIGNUP = 'signup',
  LOGIN = 'login',
  GET_STATISTICS = 'getStatistics',
  GET_SELLERS = 'getSellers',
  GET_ESTATES = 'getEstates',
}

export type SignupActivityData = Record<string, unknown>;

export type LoginActivityData = Record<string, unknown>;

export type GetStatisticsActivityData = Record<string, unknown>;

export type GetSellersActivityData = Record<string, unknown>;

export type GetEstatesActivityData = {
  vitecIDs: EstateMongoose['estateId'][];
};

export type BrokerActivity = Resource & {
  brokerID: BrokerActivityModel['id'];
  type: BrokerActivityModel['type'];
  data: BrokerActivityModel['data'];
};
