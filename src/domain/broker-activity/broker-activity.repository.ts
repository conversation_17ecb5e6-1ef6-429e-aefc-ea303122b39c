import type { <PERSON>roke<PERSON> } from '../broker/broker';
import type { BrokerActivity, BrokerActivityType } from './broker-activity';
import { BrokerActivityModel } from './broker-activity.model';

export type BrokerActivityRepository = {
  create(
    brokerID: BrokerActivity['id'],
    type: BrokerActivity['type'],
    data: BrokerActivity['data'],
  ): Promise<BrokerActivity>;
  getLatestActivityByType(brokerID: Broker['id'], type: BrokerActivityType): Promise<BrokerActivity | null>;
  getNumberOfActivitiesByType(brokerID: Broker['id'], type: BrokerActivityType): Promise<number>;
};

export const brokerActivityTransformer = (brokerActivityModel: BrokerActivityModel): BrokerActivity => ({
  id: brokerActivityModel.id,
  createdAt: brokerActivityModel.createdAt,
  updatedAt: brokerActivityModel.updatedAt,
  brokerID: brokerActivityModel.brokerID,
  type: brokerActivityModel.type,
  data: brokerActivityModel.data,
});

export const brokerActivityRepositoryFactory = (): BrokerActivityRepository => {
  const create = async (
    brokerID: BrokerActivity['id'],
    type: BrokerActivity['type'],
    data: BrokerActivity['data'],
  ): Promise<BrokerActivity> => {
    return BrokerActivityModel.create({ brokerID, type, data });
  };

  const getLatestActivityByType = async (
    brokerID: Broker['id'],
    type: BrokerActivityType,
  ): Promise<BrokerActivity | null> => {
    const entry = await BrokerActivityModel.findOne({
      where: {
        brokerID,
        type,
      },
      order: [['createdAt', 'DESC']],
      limit: 1,
    });

    if (!entry) {
      return null;
    }

    return entry;
  };

  const getNumberOfActivitiesByType = async (brokerID: Broker['id'], type: BrokerActivityType): Promise<number> => {
    return BrokerActivityModel.count({
      where: {
        brokerID,
        type,
      },
    });
  };

  return {
    create,
    getLatestActivityByType,
    getNumberOfActivitiesByType,
  };
};
