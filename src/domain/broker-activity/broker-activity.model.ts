import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import type { Broker } from '../broker/broker';
import { BrokerModel } from '../broker/broker.model';
import type {
  BrokerActivityType,
  GetEstatesActivityData,
  GetSellersActivityData,
  GetStatisticsActivityData,
  LoginActivityData,
  SignupActivityData,
} from './broker-activity';

export const BROKER_ACTIVITY_TABLE_NAME = 'BrokerActivity';
export const BROKER_ACTIVITY_TYPE_ENUM_NAME = 'broker_activity_type';

const brokerActivityTableAttributes: ModelAttributes = {
  ...baseModelAttributes,
  brokerID: {
    type: DataTypes.UUID,
    references: {
      model: BrokerModel,
      key: 'id',
    },
    allowNull: false,
  },
  type: {
    type: BROKER_ACTIVITY_TYPE_ENUM_NAME,
  },
  data: {
    type: DataTypes.JSONB,
  },
};

export class BrokerActivityModel extends ResourceModel {
  brokerID: Broker['id'];
  type: BrokerActivityType;
  data:
    | SignupActivityData
    | LoginActivityData
    | GetStatisticsActivityData
    | GetSellersActivityData
    | GetEstatesActivityData;
}

export const brokerActivityModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  BrokerActivityModel.init(brokerActivityTableAttributes, {
    sequelize,
    tableName: BROKER_ACTIVITY_TABLE_NAME,
    timestamps: true,
    indexes: [
      {
        fields: ['brokerID'],
      },
    ],
  });
};

export const setBrokerActivityModelReferences = (): void => {
  BrokerActivityModel.belongsTo(BrokerModel, {
    foreignKey: 'id',
    as: 'broker',
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  });
};
