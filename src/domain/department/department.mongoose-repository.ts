import type { Connection, Document } from 'mongoose';
import type { DepartmentMongoose } from './department';
import { departmentSchema } from './department.schema';

export type DepartmentMongooseRepository = {
  getDepartmentById(departmentId: number): Promise<DepartmentMongoose | null>;
  getDepartments(): Promise<DepartmentMongoose[]>;
};

export const departmentMongooseRepositoryFactory = ({
  connection,
}: {
  connection: Connection;
}): DepartmentMongooseRepository => {
  const departmentModel = connection.model<DepartmentMongoose & Document>(
    'department',
    departmentSchema,
    'departments',
  );

  const getDepartmentById = async (departmentId: number): Promise<DepartmentMongoose | null> => {
    return departmentModel.findOne({ departmentId });
  };

  const getDepartments = async (): Promise<DepartmentMongoose[]> => {
    return departmentModel.find();
  };

  return { getDepartmentById, getDepartments };
};
