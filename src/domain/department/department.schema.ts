import { Schema } from 'mongoose';

const ArticleSummary = {
  _id: Schema.Types.ObjectId,
  articleId: String,
  amount: Number,
};

const Commission = {
  _id: Schema.Types.ObjectId,
  employeeId: String,
  departmentId: Number,
  period: String,
  articleSummaries: [ArticleSummary],
};

export const departmentSchema = new Schema({
  _id: Schema.Types.ObjectId,
  employees: [Schema.Types.ObjectId],
  estates: [Schema.Types.ObjectId],
  commissions: [Commission],
  departmentId: Number,
  name: String,
  organisationNumber: String,
  legalName: String,
  phone: String,
  email: String,
  streetAddress: String,
  postalAddress: String,
  postalCode: String,
  city: String,
  slug: String,
  __v: Number,
});
