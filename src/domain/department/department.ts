import type { Schema } from 'mongoose';

export type DepartmentMongoose = {
  _id: Schema.Types.ObjectId;
  employees: Schema.Types.ObjectId[];
  estates: Schema.Types.ObjectId[];
  commissions: Commission[];
  departmentId: number;
  name: string;
  organisationNumber: string;
  legalName: string;
  phone: string;
  email: string;
  streetAddress: string;
  postalAddress: string;
  postalCode: string;
  city: string;
  slug: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  __v: number;
};

export type Commission = {
  _id: Schema.Types.ObjectId;
  employeeId: string;
  departmentId: number;
  period: string;
  articleSummaries: ArticleSummary[];
};

export type ArticleSummary = {
  _id: Schema.Types.ObjectId;
  articleId: string;
  amount: number;
};
