import type { Favorite } from './favorite';
import { FavoriteModel } from './favorite.model';

export type FavoritesRepository = {
  createFavorite: (userID: string, estateID: string) => Promise<void>;
  deleteFavorite: (userID: string, estateID: string) => Promise<void>;
  getUserFavorites: (userID: string) => Promise<Favorite[]>;
};

export const favoriteTransformer = (favoritesModel: FavoriteModel): Favorite => ({
  id: favoritesModel.id,
  createdAt: favoritesModel.createdAt,
  updatedAt: favoritesModel.updatedAt,
  estateID: favoritesModel.estateID,
  userID: favoritesModel.userID,
});

export const favoriteRepositoryFactory = (): FavoritesRepository => {
  const createFavorite = async (userID: string, estateID: string): Promise<void> => {
    await FavoriteModel.create({ userID, estateID });
  };

  const deleteFavorite = async (userID: string, estateID: string): Promise<void> => {
    const favorite = await FavoriteModel.findOne({ where: { userID, estateID } });
    return favorite?.destroy();
  };

  const getUserFavorites = async (userID: string): Promise<Favorite[]> => {
    const favorites = await FavoriteModel.findAll({ where: { userID } });
    return favorites.map(favoriteTransformer);
  };

  return { createFavorite, deleteFavorite, getUserFavorites };
};
