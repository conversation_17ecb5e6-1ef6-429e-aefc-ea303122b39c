import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import { UserModel } from '../user/user.model';

export const FAVORITES_TABLE_NAME = 'Favorites';

const favoriteAttributes: ModelAttributes = {
  ...baseModelAttributes,
  userID: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  estateID: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
};

export class FavoriteModel extends ResourceModel {
  public userID: string;
  public estateID: string;
}

export const favoriteModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  FavoriteModel.init(favoriteAttributes, { sequelize, tableName: FAVORITES_TABLE_NAME, timestamps: true });
};

export const setFavoriteModelReferences = (): void => {
  FavoriteModel.belongsTo(UserModel, { foreignKey: 'id', as: 'user', onUpdate: 'CASCADE', onDelete: 'CASCADE' });
};
