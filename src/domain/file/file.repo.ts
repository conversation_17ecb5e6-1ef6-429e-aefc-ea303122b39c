import { omit, pick } from 'lodash';
import type { FileDetails, FileResource, FileWritable } from './file';
import { FileModel } from './file.model';

export type FileRepo = {
  create: (file: Omit<FileWritable, 'binaryContent'>) => Promise<FileResource>;
  get: (id: string) => Promise<FileResource | null>;
  getDetails: (id: string) => Promise<FileDetails | null>;
  getAllDetailsByIds: (ids: string[]) => Promise<FileDetails[]>;
  update: (id: string, file: Partial<FileWritable>) => Promise<FileResource | null>;
  delete: (id: string) => Promise<void>;
};

type FileRepoFactory = () => FileRepo;
export const fileRepoFactory: FileRepoFactory = () => ({
  create: async (file) => {
    const fileToCreate = omit(file, 'id', 'createdAt', 'updatedAt');
    const rawFile = await FileModel.create(fileToCreate);
    return rawFile.toJSON();
  },
  get: async (id) => {
    const rawFile = await FileModel.findOne({ where: { id } });
    if (!rawFile) {
      return null;
    }
    return rawFile.toJSON();
  },
  getDetails: async (id) => {
    const rawFileDetails = await FileModel.findOne({
      attributes: Object.keys(pick(FileModel.rawAttributes, 'id', 'type', 'fileName', 'mimeType')),
      where: { id },
      order: [['createdAt', 'ASC']],
    });
    if (!rawFileDetails) {
      return null;
    }

    return rawFileDetails.toJSON();
  },
  getAllDetailsByIds: async (ids) => {
    const rawFileDetails = await FileModel.findAll({
      attributes: Object.keys(pick(FileModel.rawAttributes, 'id', 'type', 'fileName', 'mimeType')),
      where: { id: ids },
      order: [['createdAt', 'ASC']],
    });

    return rawFileDetails.map<FileDetails>((detail) => detail.toJSON());
  },
  update: async (id, file) => {
    const existingRawFile = await FileModel.findOne({ where: { id } });
    if (!existingRawFile) {
      return null;
    }

    const fileToUpdate = omit(file, 'id', 'createdAt', 'updatedAt');
    const updatedRawFile = await existingRawFile.update(fileToUpdate);
    return updatedRawFile.toJSON();
  },

  delete: async (id) => {
    const existingFile = await FileModel.findOne({ where: { id } });
    if (!existingFile) {
      return;
    }

    await FileModel.destroy({ where: { id } });
  },
});
