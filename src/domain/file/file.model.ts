import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { ResourceModel, baseModelAttributes } from '../../framework/sequelize/resource';
import type { FileType } from './file';

export const FILE_TABLE_NAME = 'File';

export const fileTableOptions: ModelAttributes = {
  ...baseModelAttributes,
  type: {
    type: DataTypes.ENUM,
    values: ['rawDocument', 'participantImage', 'meterImage'],
    allowNull: false,
  },
  binaryContent: {
    type: DataTypes.BLOB,
    allowNull: true,
  },
  fileName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  mimeType: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  s3FileUrl: {
    type: DataTypes.STRING,
    allowNull: true,
  },
};

export class FileModel extends ResourceModel {
  public type: FileType;
  public binaryContent: Buffer;
  public fileName: string;
  public mimeType: string;
  public s3FileUrl: string;
}

export const fileModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  FileModel.init(fileTableOptions, {
    sequelize,
    tableName: FILE_TABLE_NAME,
    timestamps: true,
  });
};
