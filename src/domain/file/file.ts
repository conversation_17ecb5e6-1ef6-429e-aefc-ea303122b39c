import type { Resource } from '../../framework/sequelize/resource';

export enum FileType {
  SUPPORT_DOCUMENT = 'supportDocument',
  RAW_DOCUMENT = 'rawDocument',
  PARTICIPANT_IMAGE = 'participantImage',
  METER_IMAGE = 'meterImage',
  SIGNED_DOCUMENT = 'signedDocument',
}

export type FileWritable = {
  type: FileType;
  binaryContent: Buffer;
  fileName: string;
  mimeType: string;
  s3FileUrl: string | null;
};

export type FileResource = Resource & FileWritable;

export type FileResourceWithoutBinary = Omit<FileResource, 'binaryContent'>;

export type FileInput = Omit<FileWritable, 'binaryContent' | 's3FileUrl'> & { content: Buffer | string };

export type FileDetails = Pick<FileResource, 'id' | 'type' | 'fileName' | 'mimeType'>;
