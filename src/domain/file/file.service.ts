import { config } from '../../config';
import type { AWSS3Service } from '../aws/aws-s3.service';
import { S3ACLType, s3ImageKeyFactory } from '../aws/aws-s3.service';
import type { FileInput, FileResource as Myfile } from './file';
import { FileType } from './file';
import type { FileRepo } from './file.repo';

const folders = {
  [FileType.METER_IMAGE]: config.awsS3DocAndMeterConfig.meterFolder,
  [FileType.PARTICIPANT_IMAGE]: config.awsS3DocAndMeterConfig.docFolder,
  [FileType.RAW_DOCUMENT]: config.awsS3DocAndMeterConfig.docFolder,
  [FileType.SIGNED_DOCUMENT]: config.awsS3DocAndMeterConfig.docFolder,
  [FileType.SUPPORT_DOCUMENT]: config.awsS3DocAndMeterConfig.docFolder,
};

export type FileService = {
  get: (id: string) => Promise<Myfile | null>;
  create: (file: Omit<FileInput, 'content'> & { content: Buffer | string }) => Promise<Myfile>;
  createOrUpdate(params: {
    id: string | null;
    file: Omit<FileInput, 'content'> & { content: Buffer | string };
  }): Promise<Myfile>;
  remove(id: string): Promise<void>;
};

type FileServiceFactory = (params: { fileRepo: FileRepo; awsS3Service: AWSS3Service }) => FileService;
export const fileServiceFactory: FileServiceFactory = ({ fileRepo, awsS3Service }) => {
  const get: FileService['get'] = async (id) => {
    const file = await fileRepo.get(id);
    if (!file) {
      return null;
    }
    if (file.s3FileUrl) {
      const binaryContent = await awsS3Service.getFile({
        bucket: config.awsS3DocAndMeterConfig.bucket,
        key: getAwsFileNameWithFolder(file.s3FileUrl),
      });
      return { ...file, binaryContent };
    }
    return file;
  };
  const create: FileService['create'] = async ({ content, ...file }) => {
    const folder = folders[file.type];
    const s3FileUrl = await awsS3Service.uploadFile({
      acl: S3ACLType.PUBLIC_READ,
      body: Buffer.isBuffer(content) ? content : Buffer.from(content, 'base64'),
      bucket: config.awsS3DocAndMeterConfig.bucket,
      key: s3ImageKeyFactory(folder, file.mimeType.split('/')[1]),
    });
    return fileRepo.create({ ...file, s3FileUrl });
  };
  const createOrUpdate: FileService['createOrUpdate'] = async ({ id, file }) => {
    if (id) {
      await remove(id);
      return create(file);
    }
    return create(file);
  };
  const remove: FileService['remove'] = async (id) => {
    const file = await fileRepo.get(id);
    if (!file) {
      return;
    }
    if (file.s3FileUrl) {
      await awsS3Service.deleteFile({
        bucket: config.awsS3DocAndMeterConfig.bucket,
        key: getAwsFileNameWithFolder(file.s3FileUrl),
      });
    }
    await fileRepo.delete(id);
  };
  return { get, create, createOrUpdate, remove };
};

const getAwsFileNameWithFolder = (fileUrl: string): string => {
  const path: string[] = fileUrl.split('/');
  const folder = path[path.length - 2];
  const fileName = path[path.length - 1];
  return `${folder}/${fileName}`;
};
