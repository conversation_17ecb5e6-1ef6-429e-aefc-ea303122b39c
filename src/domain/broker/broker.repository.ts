import { ResourceNotFound } from '../../framework/errors/resource.errors';
import type { PasswordHelper } from '../password/password.helper';
import type { Broker, BrokerUpdates } from './broker';
import { BrokerModel } from './broker.model';

export type BrokerRepository = {
  createBroker(email: Broker['email'], password: Broker['password'], vitecID: Broker['vitecID']): Promise<Broker>;
  getBrokerByEmail(email: Broker['email']): Promise<Broker | null>;
  getBrokerByID(id: Broker['id']): Promise<Broker | null>;
  updateBroker(id: Broker['id'], updates: BrokerUpdates): Promise<Broker | null>;
  getBrokerByReferralID(referralID: string): Promise<Broker | null>;
};

export const brokerTransformer = (brokerModel: BrokerModel): Broker => {
  return {
    id: brokerModel.id,
    vitecID: brokerModel.vitecID,
    createdAt: brokerModel.createdAt,
    updatedAt: brokerModel.updatedAt,
    email: brokerModel.email.toLowerCase(),
    password: brokerModel.password,
    passwordCode: brokerModel.passwordCode,
    verifyPasswordCode: brokerModel.verifyPasswordCode,
    isVerified: brokerModel.isVerified,
    verifyEmailCode: brokerModel.verifyEmailCode,
    referralCode: brokerModel.referralCode,
  };
};

export const brokerRepositoryFactory = ({ passwordHelper }: { passwordHelper: PasswordHelper }): BrokerRepository => {
  const createBroker = async (email: string, password: string, vitecID: Broker['vitecID']): Promise<Broker> => {
    const hashedPassword = await passwordHelper.hash(password);
    const broker = await BrokerModel.create({
      email: email.toLowerCase(),
      password: hashedPassword,
      vitecID,
    });

    return brokerTransformer(broker);
  };
  const getBrokerByEmail = async (email: Broker['email']): Promise<Broker | null> => {
    const selectedBroker = await BrokerModel.findOne({
      where: {
        email: email.toLowerCase(),
      },
    });

    if (!selectedBroker) {
      return null;
    }

    return brokerTransformer(selectedBroker);
  };

  const getBrokerByID = async (id: Broker['id']): Promise<Broker | null> => {
    const selectedBroker = await BrokerModel.findOne({
      where: {
        id,
      },
    });
    if (!selectedBroker) {
      return null;
    }
    return brokerTransformer(selectedBroker);
  };

  const updateBroker = async (id: Broker['id'], updates: BrokerUpdates): Promise<Broker> => {
    const selectedBroker = await BrokerModel.findOne({ where: { id } });
    if (!selectedBroker) {
      throw new ResourceNotFound('broker not found');
    }
    const updatedBroker = await selectedBroker.update({
      ...updates,
      updatedAt: new Date(),
      email: updates.email?.toLowerCase(),
    });
    return brokerTransformer(updatedBroker);
  };

  const getBrokerByReferralID = async (referralID: string): Promise<Broker | null> => {
    const selectedBroker = await BrokerModel.findOne({
      where: { referralCode: referralID },
    });
    if (!selectedBroker) {
      return null;
    }
    return brokerTransformer(selectedBroker);
  };

  return {
    createBroker,
    getBrokerByEmail,
    getBrokerByID,
    updateBroker,
    getBrokerByReferralID,
  };
};
