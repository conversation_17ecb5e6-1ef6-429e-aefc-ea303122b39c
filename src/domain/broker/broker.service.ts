import type { EmployeeMongooseRepository } from './mongo/employee.mongoose-repository';

export type BrokerService = {
  getBrokerExistsFromVITECbyEmail(vitecEmail: string): Promise<string | undefined>;
};

export const brokerServiceFactory = ({
  employeeMongooseRepository,
}: {
  employeeMongooseRepository: EmployeeMongooseRepository;
}): BrokerService => {
  const getBrokerExistsFromVITECbyEmail = async (vitecEmail: string): Promise<string | undefined> => {
    const broker = await employeeMongooseRepository.getEmployeeByEmail(vitecEmail);

    if (!broker) {
      return undefined;
    }

    return broker.id;
  };

  return {
    getBrokerExistsFromVITECbyEmail,
  };
};
