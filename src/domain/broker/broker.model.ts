import type { ModelAttributes } from 'sequelize';
import { DataTypes, Sequelize } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';

export const BROKERS_TABLE_NAME = 'Brokers';

const brokerAttributes: ModelAttributes = {
  ...baseModelAttributes,
  email: {
    type: DataTypes.TEXT,
    unique: true,
  },
  password: {
    type: DataTypes.TEXT,
  },
  vitecID: {
    type: DataTypes.TEXT,
  },
  // TODO: Convert this field to UUID data type
  passwordCode: {
    allowNull: false,
    // TODO: Security issue, this does not necessarily use a CSPRNG.
    //       Use node's v4() function from the uuid package instead of relying on the database!
    defaultValue: Sequelize.literal('uuid_generate_v4()'),
    type: DataTypes.TEXT,
  },
  // TODO: Convert this field to UUID data type
  verifyPasswordCode: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  isVerified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  verifyEmailCode: {
    type: DataTypes.UUID,
    allowNull: true,
  },
  referralCode: {
    type: DataTypes.TEXT,
  },
};

export class BrokerModel extends ResourceModel {
  public email!: string;
  public vitecID?: string;
  public password: string;
  public passwordCode!: string;
  public verifyPasswordCode: string;
  public isVerified: boolean;
  public verifyEmailCode: string;
  public referralCode: string;
}

export const brokerModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  BrokerModel.init(brokerAttributes, { sequelize, tableName: BROKERS_TABLE_NAME, timestamps: true });
};
