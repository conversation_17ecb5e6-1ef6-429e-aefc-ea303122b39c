import { v4 } from 'uuid';
import type { MailService } from '../mail/mail-service';
import { emailVerificationEmailTemplateFactory } from '../templates/email-templates/email-verification.email-template';
import type { TokenService } from '../token/token.service';
import type { BrokerRepository } from './broker.repository';
import type { Broker } from './broker';
import type { BrokerService } from './broker.service';

export type BrokerEmailVerificationService = {
  sendVerificationEmail(brokerId: Broker['id']): Promise<boolean>;
};

export type BrokerEmailVerificationServiceFactoryArgs = {
  mailService: MailService;
  brokerRepository: BrokerRepository;
  brokerService: BrokerService;
  tokenService: TokenService<string, { brokerId: string }>;
  mail: {
    domain: string;
    sender: string;
  };
};

export const brokerEmailVerificationServiceFactory = (
  factoryArgs: BrokerEmailVerificationServiceFactoryArgs,
): BrokerEmailVerificationService => {
  const sendVerificationEmail = async (brokerId: Broker['id']): Promise<boolean> => {
    const broker = await factoryArgs.brokerRepository.updateBroker(brokerId, {
      verifyEmailCode: v4(),
    });

    if (!broker || !broker.verifyEmailCode) {
      return false;
    }

    const token = factoryArgs.tokenService.sign({ brokerId: brokerId }, broker.verifyEmailCode);

    const emailTemplate = await emailVerificationEmailTemplateFactory({
      name: broker.name || 'Broker',
      token,
      frontendDomain: factoryArgs.mail.domain,
    });

    const { isSuccess } = await factoryArgs.mailService.sendMail({
      subject: 'Email verification',
      to: broker.email,
      html: emailTemplate,
    });
    return isSuccess;
  };

  return { sendVerificationEmail };
};
