/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import type { Connection, Document } from 'mongoose';
import { withoutNorwayCountryCode } from '../../../utils/phone-number.utils';
import type { Employee } from '../../estate/estate-employee/estate-employee';
import type { EmployeeMongoose } from '../../estate/mongo/employee/employee';
import { employeeSchema } from '../../estate/mongo/employee/employee.schema';
import { BrokerRole } from '../../estate/mongo/estate.mongoose-types';

export type EmployeeMongooseRepository = {
  getEmployeeByEmail(email: string): Promise<Employee | null>;
  getEmployeeByPhoneNumber(phoneNumber: string): Promise<EmployeeMongoose | null>;
};

const employeeTransformer = (employeeDocument: EmployeeMongoose): Employee => {
  const employeeJSON = employeeDocument.toJSON() as Employee<PERSON>ongoose;
  return {
    id: employeeJSON._id,
    title: employeeJ<PERSON><PERSON>.title,
    email: employeeJSO<PERSON>.email,
    password: '',
    name: employeeJSO<PERSON>.name,
    employeeId: employeeJ<PERSON><PERSON>.employeeId,
    brokerRole: BrokerRole.UNKNOWN,
    image: employeeJSON.image,
    slug: employeeJSON.slug,
    mobilePhone: employeeJSON.mobilePhone,
    workPhone: employeeJSON.workPhone,
    department: employeeJSON.department,
    departmentId: employeeJSON.departmentId,
  };
};

export const employeeMongooseRepositoryFactory = ({
  connection,
}: {
  connection: Connection;
}): EmployeeMongooseRepository => {
  const employeeModel = connection.model<EmployeeMongoose & Document>('Employee', employeeSchema, 'employees');

  const getEmployeeByEmail = async (email: string): Promise<Employee | null> => {
    const selectedEmployee = await employeeModel.findOne({ email: new RegExp(`^${email}$`, 'i') });
    if (!selectedEmployee) {
      return null;
    }
    return employeeTransformer(selectedEmployee);
  };

  const getEmployeeByPhoneNumber = async (phoneNumber: string): Promise<EmployeeMongoose | null> => {
    const phoneNumberWithoutCountryCode = withoutNorwayCountryCode(phoneNumber);
    const selectedEmployee = await employeeModel.findOne({
      mobilePhone: new RegExp(`^${phoneNumberWithoutCountryCode}$`, 'i'),
      employeeActive: true,
    });
    if (!selectedEmployee) {
      return null;
    }
    return selectedEmployee;
  };

  return { getEmployeeByEmail, getEmployeeByPhoneNumber };
};
