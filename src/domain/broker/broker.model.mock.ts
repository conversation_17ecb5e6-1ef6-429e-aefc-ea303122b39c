import bcrypt from 'bcrypt';
import { v4 } from 'uuid';
import { config } from '../../config';

export const brokerModelMock = {
  id: '9273512a-e8f6-41da-8453-eafb5ec6d81e',
  email: '<EMAIL>',
  name: '[fake broker name]',
  plainTextPassword: 'sEcReT1234',
  password: bcrypt.hashSync('sEcReT1234', config.saltRounds),
  vitecID: 'fac315cf-ff82-4cc2-8e30-190b959c027a',
  passwordCode: v4(),
  verifyPasswordCode: null,
  isVerified: false,
  verifyEmailCode: 'e2a0a0f0-ee04-483c-8510-e12d630ab047',
};
