import type { Resource } from '../../framework/sequelize/resource';
import type { RecommendedAction } from '../recommendation-engine/recommendation-engine';

export type Broker = Resource & {
  email: string;
  password: string;
  vitecID?: string;
  name?: string;
  passwordCode: string;
  verifyPasswordCode: string | null;
  isVerified: boolean;
  verifyEmailCode: string | null;
  referralCode: string | null;
};

export type BrokerLeadItem = {
  id: string;
  estateID: string;
  title: string;
  address: string;
  reminders: RecommendedAction[];
  relatedBrokerRoles: number[];
};
export type BrokerLead = {
  name: string;
  items: BrokerLeadItem[];
};

export type BrokerUpdates = Partial<Omit<Broker, 'id' | 'createdAt'>>;
