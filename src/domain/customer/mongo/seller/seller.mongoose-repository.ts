import type { Connection, Document } from 'mongoose';
import type { SellerMongoose } from './seller';
import { sellerSchema } from './seller.schema';

export type SellerMongooseRepository = {
  getSellerByContactId(id: string): Promise<SellerMongoose | null>;
  getSellersByContactIds(ids: string[]): Promise<SellerMongoose[]>;
};

export const sellerMongooseRepositoryFactory = ({
  connection,
}: {
  connection: Connection;
}): SellerMongooseRepository => {
  const sellerModel = connection.model<SellerMongoose & Document>('seller', sellerSchema, 'sellers');

  const getSellerByContactId = async (id: string): Promise<SellerMongoose | null> => {
    const selectedBuyer = await sellerModel.findOne({ contactId: id });
    if (!selectedBuyer) {
      return null;
    }
    return selectedBuyer.toJSON() as SellerMongoose;
  };

  const getSellersByContactIds = async (ids: string[]): Promise<SellerMongoose[]> => {
    const selectedSellers = await sellerModel.find({ contactId: { $in: ids } });
    return selectedSellers.map((s) => s.toJSON() as SellerMongoose);
  };

  return { getSellerByContactId, getSellersByContactIds };
};
