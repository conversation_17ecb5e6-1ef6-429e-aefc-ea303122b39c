import type { Connection, Document, Schema } from 'mongoose';
import type { BuyerMongoose } from './buyer';
import { buyerSchema } from './buyer.schema';

export type BuyerMongooseRepository = {
  getBuyerByContactId(id: string): Promise<BuyerMongoose | null>;
  getBuyerByEmail(email: string): Promise<BuyerMongoose | null>;
  getBuyersByEstates(estateIds: Schema.Types.ObjectId[]): Promise<BuyerMongoose[]>;
  getBuyersByContactIds(ids: string[]): Promise<BuyerMongoose[]>;
};

export const buyerMongooseRepositoryFactory = ({ connection }: { connection: Connection }): BuyerMongooseRepository => {
  const buyerModel = connection.model<BuyerMongoose & Document>('Buyer', buyerSchema, 'buyers');

  const getBuyerByContactId = async (id: string): Promise<BuyerMongoose | null> => {
    const selectedBuyer = await buyerModel.findOne({ contactId: id });
    if (!selectedBuyer) {
      return null;
    }
    return selectedBuyer.toJSON() as BuyerMongoose;
  };

  const getBuyerByEmail = async (email: string): Promise<BuyerMongoose | null> => {
    const selectedBuyer = await buyerModel.findOne({ email });
    if (!selectedBuyer) {
      return null;
    }
    return selectedBuyer.toJSON() as BuyerMongoose;
  };

  const getBuyersByEstates = async (estateIds: Schema.Types.ObjectId[]): Promise<BuyerMongoose[]> => {
    const selectedBuyers = await buyerModel.find({ estates: { $in: estateIds } });
    return selectedBuyers.map((b) => b.toJSON() as BuyerMongoose);
  };

  const getBuyersByContactIds = async (ids: string[]): Promise<BuyerMongoose[]> => {
    const selectedBuyers = await buyerModel.find({ contactId: { $in: ids } });
    return selectedBuyers.map((b) => b.toJSON() as BuyerMongoose);
  };

  return { getBuyerByEmail, getBuyersByEstates, getBuyerByContactId, getBuyersByContactIds };
};
