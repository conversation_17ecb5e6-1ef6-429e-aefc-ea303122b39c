import { getFullNameString } from '../../utils/name.utils';
import type { Customer } from './customer';
import { CustomerType } from './customer';
import type { BuyerMongoose } from './mongo/buyer/buyer';
import type { BuyerMongooseRepository } from './mongo/buyer/buyer.mogoose-repository';
import type { SellerMongoose } from './mongo/seller/seller';
import type { SellerMongooseRepository } from './mongo/seller/seller.mongoose-repository';

const customerTransformer = (customer: SellerMongoose | BuyerMongoose, type: CustomerType): Customer => ({
  id: customer._id.toString(),
  contactId: customer.contactId,
  type,
  email: customer.email,
  phone: customer.mobilePhone,
  name: getFullNameString(customer),
});

export type CustomerServiceForBrokers = {
  getCustomerByContactID(customerID: string, estateMongoID?: string): Promise<Customer | null>;
  getSellersByIds(ids: string[]): Promise<Customer[]>;
  getBuyersByIds(ids: string[]): Promise<Customer[]>;
};
export const customerServiceFactoryForBrokers = ({
  sellerMongooseRepository,
  buyerMongooseRepository,
}: {
  sellerMongooseRepository: SellerMongooseRepository;
  buyerMongooseRepository: BuyerMongooseRepository;
}): CustomerServiceForBrokers => {
  const getCustomerByContactID = async (customerID: string, estateMongoID?: string): Promise<Customer | null> => {
    const selectedSeller = await sellerMongooseRepository.getSellerByContactId(customerID);
    if (estateMongoID && !selectedSeller?.estates.map((e) => e.toString()).includes(estateMongoID)) {
      const selectedBuyer = await buyerMongooseRepository.getBuyerByContactId(customerID);
      if (!selectedBuyer) {
        return null;
      }
      return customerTransformer(selectedBuyer, CustomerType.BUYER);
    }
    if (!selectedSeller) {
      return null;
    }
    return customerTransformer(selectedSeller, CustomerType.SELLER);
  };

  const getSellersByIds = async (ids: string[]): Promise<Customer[]> => {
    const sellers = await sellerMongooseRepository.getSellersByContactIds(ids);
    return sellers.map((s) => customerTransformer(s, CustomerType.SELLER));
  };

  const getBuyersByIds = async (ids: string[]): Promise<Customer[]> => {
    const buyers = await buyerMongooseRepository.getBuyersByContactIds(ids);
    return buyers.map((b) => customerTransformer(b, CustomerType.BUYER));
  };

  return { getCustomerByContactID, getSellersByIds, getBuyersByIds };
};
