import type { LeadAudit } from './lead-audit';
import { LeadAuditModel } from './lead-audit.model';

export type LeadAuditRepository = {
  create(userID: LeadAudit['userID'], data: LeadAudit['data'], response: LeadAudit['response']): Promise<LeadAudit>;
};

export const leadAuditTransformer = (leadAuditModel: LeadAuditModel): LeadAudit => {
  return {
    id: leadAuditModel.id,
    createdAt: leadAuditModel.createdAt,
    updatedAt: leadAuditModel.updatedAt,
    userID: leadAuditModel.userID,
    data: leadAuditModel.data,
    response: leadAuditModel.response,
  };
};

export const leadAuditRepositoryFactory = (): LeadAuditRepository => {
  const create = async (
    userID: LeadAudit['userID'],
    data: LeadAudit['data'],
    response: LeadAudit['response'],
  ): Promise<LeadAudit> => {
    const audit = await LeadAuditModel.create({
      userID,
      data,
      response,
    });

    return leadAuditTransformer(audit);
  };

  return {
    create,
  };
};
