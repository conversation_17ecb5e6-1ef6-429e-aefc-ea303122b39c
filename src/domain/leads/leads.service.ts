import { promises as fs } from 'fs';
import { join } from 'path';
import { isEmpty } from 'lodash';
import type { DepartmentMongooseRepository } from '../department/department.mongoose-repository';
import type { EstateMongooseRepository } from '../estate/mongo/estate.mogoose-repository';
import type { EstateMongoose } from '../estate/mongo/estate.mongoose-types';
import { BrokerRole } from '../estate/mongo/estate.mongoose-types';
import type { LeadAuditModel } from './lead-audit.model';
import type { LeadAuditRepository } from './lead-audit.repository';

export const DEFAULT_OFFICE_ID = '0001';

type BrokerDetailsOutput = Promise<{
  name: string | null;
  employeeId: string | null;
  departmentName: string | null;
  departmentPostalCode: string | null;
}>;
export type LeadsService = {
  assignToOffice: (zipCode: string) => string;
  saveLeadAudit: (userID: string, lead: LeadAuditModel['data'], response: LeadAuditModel['response']) => Promise<void>;
  getMostRecentBrokerNameIdAndDepartmentName: (estates: EstateMongoose[]) => BrokerDetailsOutput;
  getBrokerDetailsFromUserPhoneOrEmail: (opts: { phone: string | null; email: string | null }) => BrokerDetailsOutput;
};

export const leadsServiceFactory = async ({
  rootDir,
  leadAuditRepository,
  estateMongooseRepository,
  departmentMongooseRepository,
}: {
  rootDir: string;
  leadAuditRepository: LeadAuditRepository;
  estateMongooseRepository: EstateMongooseRepository;
  departmentMongooseRepository: DepartmentMongooseRepository;
}): Promise<LeadsService> => {
  const offices = JSON.parse(await fs.readFile(join(rootDir, 'data/offices.json'), 'utf-8')) as {
    name: string;
    zip: string;
  }[];
  const zipGroups = JSON.parse(await fs.readFile(join(rootDir, 'data/zip-groups.json'), 'utf-8')) as {
    name: string;
    offices: string[];
    zips: string[];
  }[];
  const assignToOffice = (zipCode: string): string => {
    const group = zipGroups.find((group: { zips: string[] }) => group.zips.includes(zipCode));
    if (!group) {
      return DEFAULT_OFFICE_ID;
    }
    const randomIndex = Math.floor(Math.random() * group.offices.length);
    const selectedOffice = group.offices[randomIndex];

    const officeId = offices.find((o: { name: string }) => o.name === selectedOffice);
    return officeId?.zip || DEFAULT_OFFICE_ID;
  };

  const saveLeadAudit = async (
    userID: string,
    lead: LeadAuditModel['data'],
    response: LeadAuditModel['response'],
  ): Promise<void> => {
    await leadAuditRepository.create(userID, lead, response);
  };

  const getMostRecentBrokerNameIdAndDepartmentName = async (
    estates: EstateMongoose[],
  ): Promise<{
    name: string | null;
    employeeId: string | null;
    departmentName: string | null;
    departmentPostalCode: string | null;
  }> => {
    if (isEmpty(estates)) {
      return { name: null, employeeId: null, departmentName: null, departmentPostalCode: null };
    }
    const recentEstate = estates.sort((a, b) => b.createdDate.getDate() - a.createdDate.getDate())[0];
    const recentBroker =
      recentEstate.brokersIdWithRoles.filter((b) => b.brokerRole === BrokerRole.MAIN_BROKER)[0] ||
      recentEstate.brokersIdWithRoles[0];
    const departmentOfEstate = await departmentMongooseRepository.getDepartmentById(recentEstate.departmentId);

    return {
      name: recentBroker?.employee.name,
      employeeId: recentBroker?.employeeId,
      departmentName: departmentOfEstate?.name || null,
      departmentPostalCode: departmentOfEstate?.postalCode || null,
    };
  };

  const getBrokerDetailsFromUserPhoneOrEmail = async ({
    phone,
    email,
  }: {
    phone: string | null;
    email: string | null;
  }): Promise<{
    name: string | null;
    employeeId: string | null;
    departmentName: string | null;
    departmentPostalCode: string | null;
  }> => {
    const buyerEstates = await estateMongooseRepository.findEstatesByBuyerPhoneOrEmail({ phone, email });
    const sellerEstates = await estateMongooseRepository.findEstatesBySellerPhoneOrEmail({ phone, email });
    return await getMostRecentBrokerNameIdAndDepartmentName([...buyerEstates, ...sellerEstates]);
  };
  return {
    assignToOffice,
    saveLeadAudit,
    getBrokerDetailsFromUserPhoneOrEmail,
    getMostRecentBrokerNameIdAndDepartmentName,
  };
};
