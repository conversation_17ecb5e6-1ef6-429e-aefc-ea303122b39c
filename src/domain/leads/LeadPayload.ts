import type { EstatePG } from '../estate/estate';
import type { User } from '../user/user';
import type { CreateLeadInput } from './lead-audit';

export const toLeadsPayload = (user: User, dto: EstatePG): CreateLeadInput => {
  const zipCode = addressToZipCode(dto.address);
  return {
    firstName: user.name,
    lastName: user.name,
    mobilePhone: user.phoneNumber,
    email: user.email,
    postalCode: zipCode,
    streetAdress: dto.address,
    comment: 'Sent from Nordvik APP',
  };
};

const addressToZipCode = (address: EstatePG['address']): string => {
  const match = /(.+), (\d+) (.+)/.exec(address);

  if (match) {
    return match[2];
  } else {
    return '';
  }
};
