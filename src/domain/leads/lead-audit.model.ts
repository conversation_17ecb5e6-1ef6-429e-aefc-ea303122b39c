import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import { UserModel } from '../user/user.model';
import type { LeadAudit } from './lead-audit';

export const LEAD_AUDIT_TABLE_NAME = 'LeadAudit';

export const leadAuditAttributes: ModelAttributes = {
  ...baseModelAttributes,
  userID: {
    type: DataTypes.UUID,
    references: {
      model: UserModel,
      key: 'id',
    },
    allowNull: false,
  },
  data: {
    type: DataTypes.JSONB,
  },
  response: {
    type: DataTypes.JSONB,
  },
};

export class LeadAuditModel extends ResourceModel {
  public userID: UserModel['id'];
  public data: LeadAudit['data'];
  public response: LeadAudit['response'];
}

export const leadAuditModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  LeadAuditModel.init(leadAuditAttributes, {
    sequelize,
    tableName: LEAD_AUDIT_TABLE_NAME,
    timestamps: true,
  });
};

export const setLeadAuditModelReferences = (): void => {
  LeadAuditModel.belongsTo(UserModel, { foreignKey: 'id', as: 'user', onUpdate: 'CASCADE', onDelete: 'CASCADE' });
};
