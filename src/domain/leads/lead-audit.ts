import type { Resource } from '../../framework/sequelize/resource';
import type { User } from '../user/user';

export type CreateLeadInput = {
  firstName: string;
  lastName: string;
  mobilePhone: string;
  email: string;
  postalCode: string;
  streetAdress: string;
  comment: string;
};

export type LeadAudit = Resource & {
  userID: User['id'];
  data: CreateLeadInput;
  response: { status: number; payload: unknown };
};
