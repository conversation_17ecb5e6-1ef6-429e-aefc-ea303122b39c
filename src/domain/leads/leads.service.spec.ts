import { config } from '../../config';
import { departmentMongooseRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/departmentMongooseRepositoryFixtureFactory';
import { estateMongooseRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/estateMongooseRepositoryFixtureFactory';
import { DEFAULT_OFFICE_ID, leadsServiceFactory } from './leads.service';

describe('leads service', () => {
  const leadsService = leadsServiceFactory({
    rootDir: config.rootFolder,
    leadAuditRepository: { create: jest.fn() },
    estateMongooseRepository: estateMongooseRepositoryFixtureFactory(),
    departmentMongooseRepository: departmentMongooseRepositoryFixtureFactory(),
  });
  describe('assignToOffice', () => {
    describe('given an existing postal code that belongs to one office only', () => {
      const input = '0168';
      const expectedOutput = '0166';

      it('should return the corresponding offices postal code', async () => {
        return expect(leadsService.then((s) => s.assignToOffice(input))).resolves.toEqual(expectedOutput);
      });
    });
    describe('given an existing postal code that belongs to multiple offices', () => {
      const input = '0010';
      const expectedOutput = '0139';

      it('should return the corresponding offices postal code', async () => {
        // will select the first office from the list
        jest.spyOn(global.Math, 'random').mockReturnValue(0);

        return expect(leadsService.then((s) => s.assignToOffice(input))).resolves.toEqual(expectedOutput);
      });
    });
    describe('given a non-existing postal code', () => {
      const input = '696969';

      it('should return the default postal code', async () => {
        return expect(leadsService.then((s) => s.assignToOffice(input))).resolves.toEqual(DEFAULT_OFFICE_ID);
      });
    });
  });
});
