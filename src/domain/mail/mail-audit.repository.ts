import type { MailAudit } from './mail-audit';
import { MailAuditModel } from './mail-audit.model';

export type MailAuditRepository = {
  create(
    type: MailAudit['type'],
    from: MailAudit['from'],
    to: MailAudit['to'],
    data: MailAudit['data'],
  ): Promise<MailAudit>;
};

export const mailAuditTransformer = (mailAuditModel: MailAuditModel): MailAudit => {
  return {
    id: mailAuditModel.id,
    createdAt: mailAuditModel.createdAt,
    updatedAt: mailAuditModel.updatedAt,
    type: mailAuditModel.type,
    from: mailAuditModel.from,
    to: mailAuditModel.to,
    data: mailAuditModel.data,
  };
};

export const mailAuditRepositoryFactory = (): MailAuditRepository => {
  const create = async (
    type: MailAudit['type'],
    from: MailAudit['from'],
    to: MailAudit['to'],
    data: MailAudit['data'],
  ): Promise<MailAudit> => {
    const audit = await MailAuditModel.create({
      type,
      from,
      to,
      data,
    });

    return mailAuditTransformer(audit);
  };

  return {
    create,
  };
};
