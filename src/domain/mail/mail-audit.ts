import type { Resource } from '../../framework/sequelize/resource';

export enum MailAuditType {
  SEND_MAIL = 'sendMail',
  SEND_TEMPLATE = 'sendTemplate',
}

export type SendMailData = {
  subject: string;
  html: string;
};

export type SendTemplateData = {
  templateID: string;
  data: Record<string, string>;
};

export type MailAudit = Resource & {
  type: MailAuditType;
  from: string;
  to: string;
  data: SendMailData | SendTemplateData;
};
