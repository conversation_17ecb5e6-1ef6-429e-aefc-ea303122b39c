import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import type { MailAuditType, SendMailData, SendTemplateData } from './mail-audit';

export const MAIL_AUDIT_TABLE_NAME = 'MailAudit';
export const MAIL_AUDIT_TYPE_ENUM_NAME = 'mail_audit_type';

export const mailAuditAttributes: ModelAttributes = {
  ...baseModelAttributes,
  type: {
    type: MAIL_AUDIT_TYPE_ENUM_NAME,
  },
  from: {
    type: DataTypes.STRING,
  },
  to: {
    type: DataTypes.STRING,
  },
  data: {
    type: DataTypes.JSONB,
  },
};

export class MailAuditModel extends ResourceModel {
  public type: MailAuditType;
  public from: string;
  public to: string;
  public data: SendMailData | SendTemplateData;
}

export const mailAuditModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  MailAuditModel.init(mailAuditAttributes, {
    sequelize,
    tableName: MAIL_AUDIT_TABLE_NAME,
    timestamps: true,
  });
};
