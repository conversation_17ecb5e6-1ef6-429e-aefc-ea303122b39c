import sgMail from '@sendgrid/mail';
import { logger } from '../../logger';
import type { MailAuditRepository } from './mail-audit.repository';
import { MailAuditType } from './mail-audit';

export type Attachment = {
  content: string;
  filename: string;
  filetype: string;
};

export type MailService = {
  sendMail(data: {
    to: string;
    html: string;
    subject: string;
    from?: string;
    attachment?: Attachment;
  }): Promise<{ sentText: string; isSuccess: boolean; error?: Error }>;
  sendTemplate(mailData: { templateID: string; to: string; data: Record<string, string> }): Promise<boolean>;
};

export const mailServiceFactory = ({
  mailAuditRepository,
  apikey,
  senderAddress,
  silentFail,
}: {
  mailAuditRepository: MailAuditRepository;
  apikey: string;
  senderAddress: string;
  silentFail: boolean;
}): MailService => {
  sgMail.setApiKey(apikey);

  const sendMail = async (mailData: {
    to: string;
    html: string;
    subject: string;
    from?: string;
    attachment?: Attachment;
  }): Promise<{ sentText: string; isSuccess: boolean; error?: Error }> => {
    const from = mailData.from || senderAddress;

    await mailAuditRepository.create(MailAuditType.SEND_MAIL, from, mailData.to, {
      subject: mailData.subject,
      html: mailData.html,
    });

    try {
      await sgMail.send({ ...mailData, from, attachments: mailData.attachment ? [mailData.attachment] : undefined });
    } catch (e) {
      logger.error(e as Error, 'Mail sending failed because of Sendgrid');
      return { sentText: mailData.html, isSuccess: false, error: e as Error };
    }

    return { sentText: mailData.html, isSuccess: true };
  };

  const sendTemplate = async (mailData: {
    templateID: string;
    to: string;
    data: Record<string, string>;
  }): Promise<boolean> => {
    await mailAuditRepository.create(MailAuditType.SEND_TEMPLATE, senderAddress, mailData.to, {
      templateID: mailData.templateID,
      data: mailData.data,
    });

    try {
      await sgMail.send({
        templateId: mailData.templateID,
        dynamicTemplateData: mailData.data,
        to: mailData.to,
        from: senderAddress,
      });
    } catch (e) {
      return silentFail;
    }
    return true;
  };
  return {
    sendMail,
    sendTemplate,
  };
};
