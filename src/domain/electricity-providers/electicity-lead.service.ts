import axios from 'axios';
import { format } from 'date-fns';
import { isEmpty } from 'ramda';
import type { FjordkraftConfig, FortumConfig, TrondelagkraftConfig } from '../../config';
import { logger } from '../../logger';
import { LightAxiosError } from '../../utils/axios-logger.utils';
import { getFullNameString } from '../../utils/name.utils';
import { dateFromBankidFormat } from '../bankid/BankIdDate';
import { ContactType } from '../contact/mongo/contact-mongoose';
import type { EstateAddress, EstateBuyerMongoose, EstateMongoose } from '../estate/mongo/estate.mongoose-types';
import { generateExternalLeadHumanReadableId } from '../external-lead-audit/external-lead-audit';
import type { ExternalLeadAuditRepository } from '../external-lead-audit/external-lead-audit.repository';
import type { LeadsService } from '../leads/leads.service';
import { NotUnifiedServiceOfferViaApiType } from '../offers/service-offer';
import type { Otp } from '../otp/otp';
import { ElectricityProvider } from '../otp/otp';
import type { OtpMeterResponse } from '../otp/otp-meter/otp-meter';
import type { OtpParticipantResponse } from '../otp/otp-participant/otp-participant';

type CreateSaleInput = CreateMultiMeterSaleInput | CreateSingleMeterSaleInput;

type CreateMultiMeterSaleInput = CreateSaleInputBase & {
  type: 'multi';
  meterNumbers: string[];
};

type CreateSingleMeterSaleInput = CreateSaleInputBase & {
  type: 'single';
  meterNumber?: string;
};

type CreateSaleInputBase = {
  firstName: string;
  lastName: string;
  companyName: string;
  birthDate: Date;
  otpId: string;
  provider: ElectricityProvider;
  email: string;
  phoneNumber: string;
  address: EstateAddress;
  billingAddress: EstateAddress;
  contactType: ContactType;
  contactId: string;
  ssn: string;
  brokerName: string | null;
  brokerEmployeeId: string | null;
  estateOrBrokerDepartmentName: string | null;
  organizationNo?: string;
};

export type ElectricityLeadService = {
  createSale(input: CreateSaleInput): Promise<void>;
  createSalesForOtp(
    otp: Otp & {
      electricityMeters: OtpMeterResponse[];
      participants: OtpParticipantResponse[];
    },
    estate: EstateMongoose,
    billingBuyerInfo: EstateBuyerMongoose & {
      socialSecurity: string;
      email: string;
      organisationNumber: string;
    },
  ): Promise<void>;
};

const isRejected = <T>(p: PromiseSettledResult<T>): p is PromiseRejectedResult => p.status === 'rejected';

type TokenResponse = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  access_token: string;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  expires_in: number;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  token_type: string;
};

class ElectricityLeadError extends Error {
  constructor(msg: string, public electricityMeterNumbers: string[]) {
    super(msg);

    // Set the prototype explicitly.
    Object.setPrototypeOf(this, ElectricityLeadError.prototype);
  }
}
export const createElectricityLeadService = ({
  norgesConfig,
  fortumConfig,
  fjordkraftConfig,
  trondelagSpotConfig,
  trondelagTobbSpotConfig,
  externalLeadAuditRepository,
  leadService,
}: {
  norgesConfig: FortumConfig;
  fortumConfig: FortumConfig;
  fjordkraftConfig: FjordkraftConfig;
  trondelagSpotConfig: TrondelagkraftConfig;
  trondelagTobbSpotConfig: TrondelagkraftConfig;
  externalLeadAuditRepository: ExternalLeadAuditRepository;
  leadService: LeadsService;
}): ElectricityLeadService => {
  const createSale = async (input: CreateSaleInput): Promise<void> => {
    switch (input.provider) {
      case ElectricityProvider.FORTUM: {
        await createFortumSale({ input });
        break;
      }
      case ElectricityProvider.NORGES_ENERGY: {
        await createFortumSale({
          input,
          config: norgesConfig,
          leadType: NotUnifiedServiceOfferViaApiType.NORGES_ENERGI,
        });
        break;
      }
      case ElectricityProvider.FJORDKRAFT: {
        await createFjordkraftSale(input);
        break;
      }
      case ElectricityProvider.TRONDELAGSPOT: {
        await createTrondelagKraftSpotSale(input);
        break;
      }
      case ElectricityProvider.TRONDELAGTOBBSPOT: {
        await createTrondelagKraftTobbSpotSale(input);
        break;
      }
      case ElectricityProvider.NONE: {
        return;
      }
      default:
        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
        throw new Error(`Provider ${input.provider} is not implemented`);
    }
  };

  const createSalesForOtp = async (
    otp: Otp & {
      electricityMeters: OtpMeterResponse[];
      participants: OtpParticipantResponse[];
    },
    estate: EstateMongoose,
    billingBuyerInfo: EstateBuyerMongoose & {
      socialSecurity: string;
      email: string;
      organisationNumber: string;
    },
  ): Promise<void> => {
    const getBrokerDetails = async (): Promise<{
      brokerName: string | null;
      brokerEmployeeId: string | null;
      departmentNameOfEstate: string | null;
    }> => {
      const {
        name: brokerName,
        employeeId: brokerEmployeeId,
        departmentName: departmentNameOfEstate,
      } = await leadService.getMostRecentBrokerNameIdAndDepartmentName([estate]);
      let fallbackBrokerName: string | null = null;
      let fallbackBrokerEmployeeId: string | null = null;
      let fallbackDepartmentOfEstate: string | null = null;
      if (!brokerEmployeeId || !departmentNameOfEstate) {
        const d = await leadService.getBrokerDetailsFromUserPhoneOrEmail({
          phone: billingBuyerInfo.mobilePhone,
          email: billingBuyerInfo.email,
        });
        fallbackBrokerName = d.name;
        fallbackBrokerEmployeeId = d.employeeId;
        fallbackDepartmentOfEstate = d.departmentName;
      }
      return {
        brokerName: brokerName || fallbackBrokerName,
        brokerEmployeeId: brokerEmployeeId || fallbackBrokerEmployeeId,
        departmentNameOfEstate: departmentNameOfEstate || fallbackDepartmentOfEstate,
      };
    };
    const sendLead = async (electricityMeterNumbers: string[]): Promise<void> => {
      const { brokerEmployeeId, brokerName, departmentNameOfEstate } = await getBrokerDetails();

      const genericLeadBase: Omit<CreateSaleInputBase, 'provider'> = {
        otpId: otp.id,
        firstName: billingBuyerInfo.firstName,
        lastName: billingBuyerInfo.lastName,
        companyName: billingBuyerInfo.companyName,
        birthDate: dateFromBankidFormat(billingBuyerInfo.socialSecurity),
        address: estate.address,
        billingAddress:
          otp.city && otp.postCode && otp.address
            ? {
                city: otp.city,
                zipCode: otp.postCode,
                streetAdress: otp.address,
                apartmentNumber: '',
              }
            : {
                city: estate.address.city,
                zipCode: estate.address.zipCode,
                streetAdress: estate.address.streetAdress,
                apartmentNumber: '',
              },
        ssn: billingBuyerInfo.socialSecurity,
        organizationNo: billingBuyerInfo.organisationNumber,
        phoneNumber: billingBuyerInfo.mobilePhone,
        email: billingBuyerInfo.email,
        contactType: billingBuyerInfo.contactType,
        contactId: billingBuyerInfo.contactId,
        brokerName,
        brokerEmployeeId,
        estateOrBrokerDepartmentName: departmentNameOfEstate,
      };

      if (otp.electricityProviderSelected) {
        try {
          if (electricityMeterNumbers.length === 1) {
            await createSale({
              type: 'single',
              provider: otp.electricityProviderSelected,
              meterNumber: electricityMeterNumbers[0],
              ...genericLeadBase,
            });
          } else {
            await createSale({
              type: 'multi',
              provider: otp.electricityProviderSelected,
              meterNumbers: electricityMeterNumbers,
              ...genericLeadBase,
            });
          }
        } catch (error) {
          logger.error(
            error as Error,
            `Lead sending failed for meter${
              electricityMeterNumbers.length > 1 ? 's' : ''
            }: ${electricityMeterNumbers.join(', ')} at estate: ${estate.estateId} with provider: ${
              otp.electricityProviderSelected
            }. Electricity service responded with error.`,
          );
          const errorMessage = 'Lead sending failed. Details: electricityProvider responded with error';
          throw new ElectricityLeadError(errorMessage, electricityMeterNumbers);
        }
      }

      logger.info(
        `Lead sending finished for meter${
          electricityMeterNumbers.length > 1 ? 's' : ''
        }: ${electricityMeterNumbers.join(', ')} at estate: ${estate.estateId}`,
      );
      return;
    };

    if (
      !otp.electricityInfoProvided ||
      !otp.electricityProviderSelected ||
      otp.electricityProviderSelected === ElectricityProvider.NONE
    ) {
      logger.info(`Lead sending finished for estate: ${estate.estateId}, no electricity provider selected`);
      return;
    }

    const meterNumbers = isEmpty(otp.electricityMeters)
      ? ['']
      : otp.electricityMeters.map((meter) => meter.meterNumber);

    const sendLeadPromises =
      otp.electricityProviderSelected === ElectricityProvider.FJORDKRAFT
        ? [sendLead(meterNumbers)]
        : meterNumbers.map(async (meter: string) => sendLead([meter]));

    const sendLeadResults = await Promise.allSettled(sendLeadPromises);

    const failedLeads = sendLeadResults.filter(isRejected).map((e) => e.reason as ElectricityLeadError);
    if (!isEmpty(failedLeads)) {
      const metersWithErrors = failedLeads
        .map((m) => `Meter: ${m.electricityMeterNumbers.join(', ')} Error: ${m.message}`)
        .join('\n');
      const numOfAllMeters = otp.electricityMeters.length;
      const numOfFailedMeters = failedLeads.reduce((acc, l) => acc + l.electricityMeterNumbers.length, 0);
      logger.error(
        `Lead sending finished for estate: ${estate.estateId}. Lead sending was unsuccessful (${numOfFailedMeters} of ${numOfAllMeters}) for meters: ${metersWithErrors}`,
      );

      throw new Error(`Lead sending failed (${numOfFailedMeters} of ${numOfAllMeters}) for:\n${metersWithErrors}`);
    }
    logger.info(`Lead sending finished for estate: ${estate.estateId}`);
  };

  const getFortumToken = async (postgresId: string): Promise<string> => {
    const body = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      grant_type: 'password',
      username: fortumConfig.username,
      password: fortumConfig.password,
    };
    return getToken({ body, tokenUrl: fortumConfig.tokenUrl, postgresId });
  };

  const createFortumSale = async ({
    input,
    config = fortumConfig,
    leadType = NotUnifiedServiceOfferViaApiType.FORTUM,
  }: {
    input: CreateSaleInput;
    config?: FortumConfig;
    leadType?: NotUnifiedServiceOfferViaApiType;
  }): Promise<void> => {
    if (input.type === 'multi') {
      throw new Error('Fortum does not support sending multiple electricity meters in one lead');
    }

    const { id: postgresId, createdAt } = await externalLeadAuditRepository.create({
      email: input.email,
      name: getFullNameString(input),
      phone: input.phoneNumber,
      address: input.address.streetAdress || null,
      postalCode: input.address.zipCode || null,
      brokerId: input.brokerEmployeeId,
      departmentOfBroker: input.estateOrBrokerDepartmentName,
      externalLeadId: null,
      leadType,
    });

    const fortumApiBody = {
      CustomerFirstName: input.firstName,
      CustomerLastName: input.lastName,
      CustomerName: getFullNameString(input),
      CustomerPersonalNumber: input.ssn,
      OrganisationNumber: input.organizationNo,
      DateOfBirth: format(input.birthDate, 'yyyy-MM-dd'),
      CustomerMobile: input.phoneNumber,
      CustomerPhone: input.phoneNumber,
      CustomerEmail: input.email,
      DeliveryStreet: input.address.streetAdress.split(' ').slice(0, -1).join(' '),
      DeliveryStreetFull: input.address.streetAdress,
      DeliveryStreetNumber: input.address.streetAdress.split(' ').pop(),
      DeliveryLetter: input.address.apartmentNumber,
      DeliveryZipCode: input.address.zipCode,
      DeliveryCity: input.address.city,
      DeliveryMeterNumber: input.meterNumber,
      DeliveryStartDate: format(new Date(), 'yyyy-MM-dd'),
      DeliveryCustomerLastReadDate: format(new Date(), 'yyyy-MM-dd'),
      CreatedDate: format(new Date(), 'yyyy-MM-dd') /*date customer registered for Hafslund_Fortum strøm in your app*/,
      ProductPackage: {
        ExternalPackageId: config.packageId,
      },
      Project: {
        ProjectId: config.projectId,
      },
      SellerEmployee: input.brokerEmployeeId || 'Nordvik',
      CustomerVerifiedTerms: true,
      CustomerVerifiedContract: true,
      InvoiceStreet: input.billingAddress.streetAdress.replace(/[0-9]+/, ''),
      InvoiceStreetFull: input.billingAddress.streetAdress,
      InvoiceStreetNumber: input.billingAddress.streetAdress.replace(/\D+/g, ''),
      InvoiceLetter: input.billingAddress.apartmentNumber,
      InvoiceCo: '',
      InvoiceZip: input.billingAddress.zipCode,
      InvoiceCity: input.billingAddress.city,
      SellerComment: generateExternalLeadHumanReadableId({
        postgresId,
        leadType,
        createdAt,
      }),
    };

    const token = await getFortumToken(postgresId);

    const headers = { Authorization: `Bearer ${token}` };
    const response = await axios.post<{ Id: number }>(config.saleUrl, fortumApiBody, {
      headers,
      validateStatus: () => true,
    });

    const dataToAudit = {
      request: { method: 'POST', url: config.saleUrl, headers, body: fortumApiBody },
      response: {
        statusCode: response.status,
        body: response.data,
      },
      notes: { participantID: input.contactId, otpId: input.otpId },
    };

    if (!response?.status?.toString().startsWith('2')) {
      await externalLeadAuditRepository.update(postgresId, {
        isSuccessful: false,
        data: dataToAudit,
      });
      throw new Error((response.data as unknown) as string);
    }

    await externalLeadAuditRepository.update(postgresId, {
      isSuccessful: true,
      data: dataToAudit,
      externalLeadId: response.data.Id?.toString(),
    });
  };

  const getFjordkraftToken = async (postgresId: string): Promise<string> => {
    const body = `grant_type=client_credentials&client_id=${fjordkraftConfig.client_id}&client_secret=${fjordkraftConfig.client_secret}&scope=${fjordkraftConfig.scope}`;
    return getToken({ postgresId, tokenUrl: fjordkraftConfig.tokenUrl, body });
  };

  const createFjordkraftSale = async (input: CreateSaleInput): Promise<void> => {
    const { id: postgresId, createdAt } = await externalLeadAuditRepository.create({
      email: input.email,
      name: getFullNameString(input),
      phone: input.phoneNumber,
      address: input.address.streetAdress || null,
      postalCode: input.address.zipCode || null,
      brokerId: input.brokerEmployeeId,
      departmentOfBroker: input.estateOrBrokerDepartmentName,
      externalLeadId: null,
      leadType: NotUnifiedServiceOfferViaApiType.FJORDKRAFT,
    });
    const fjordkraftApiBody = {
      productHubId:
        input.contactType === ContactType.COMPANY
          ? fjordkraftConfig.companyProductId
          : fjordkraftConfig.privateProductId,
      extraProducts: [],
      distributionMethodId: 3,
      salesTypeId: input.contactType === ContactType.COMPANY ? 2 : 1,
      store: {
        productHubId: fjordkraftConfig.productHubId,
        name: 'Nordvik Eiendomsmegler',
      },
      brandId: 1,
      overrideAccept: true,
      overrideCredit: true,
      contractMail: false,
      customer: {
        firstname: input.firstName || input.lastName || input.companyName,
        lastname: input.lastName || input.companyName,
        ssn: input.ssn?.replace(/\D/g, '').replace(/\s/g, ''),
        consentOffersNewsletter: false,
        companyName: input.contactType === ContactType.COMPANY ? input.companyName || input.lastName : '',
        // remove all spaces from the organization number
        companyNo: input.organizationNo?.replace(/\s/g, '') || '',
        phone: input.phoneNumber,
        email: input.email,
        invoiceAddress: {
          streetAddress: input.billingAddress.streetAdress,
          postalCode: input.billingAddress.zipCode,
        },
      },
      installations: (input.type === 'single' ? [input.meterNumber] : input.meterNumbers).map((meterNumber) => ({
        meterId: '',
        meterNo: meterNumber,
        agreedStartupDate: format(new Date(), 'yyyy-MM-dd'),
        installationAddress: {
          streetAddress: input.address.streetAdress,
          postalCode: input.address.zipCode,
        },
        movingDate: '',
      })),
      partnerInfo: {
        partnerSellerName: input.brokerName,
        partnerSellerCode: input.brokerEmployeeId,
        partnerOrderId: generateExternalLeadHumanReadableId({
          postgresId,
          leadType: NotUnifiedServiceOfferViaApiType.FJORDKRAFT,
          createdAt,
        }),
      },
    };

    const token = await getFjordkraftToken(postgresId);
    const headers = { Authorization: `Bearer ${token}` };
    const response = await axios.post<{ id: number; store: { id: number } }[]>(
      fjordkraftConfig.saleUrl,
      fjordkraftApiBody,
      {
        headers,
        validateStatus: () => true,
      },
    );
    const dataToAudit = {
      request: { method: response.config.method, url: response.config.url, headers, body: fjordkraftApiBody },
      response: {
        statusCode: response.status,
        body: response.data,
      },
      notes: {
        participantID: input.contactId,
        otpId: input.otpId,
        storeId: response.data[0]?.store?.id,
      },
    };

    if (!response?.status?.toString().startsWith('2')) {
      await externalLeadAuditRepository.update(postgresId, {
        isSuccessful: false,
        data: dataToAudit,
      });
      throw new Error((response.data as unknown) as string);
    }

    await externalLeadAuditRepository.update(postgresId, {
      isSuccessful: true,
      data: dataToAudit,
      externalLeadId: response.data[0]?.id.toString(),
    });
  };

  const createTrondelagKraftSpotSale = async (input: CreateSaleInput): Promise<void> => {
    const { id: postgresId, createdAt } = await externalLeadAuditRepository.create({
      email: input.email,
      name: getFullNameString(input),
      phone: input.phoneNumber,
      address: input.address.streetAdress || null,
      postalCode: input.address.zipCode || null,
      brokerId: input.brokerEmployeeId,
      departmentOfBroker: input.estateOrBrokerDepartmentName,
      externalLeadId: null,
      leadType: NotUnifiedServiceOfferViaApiType.FJORDKRAFT,
    });

    const fjordkraftApiBody = {
      productHubId:
        input.contactType === ContactType.COMPANY
          ? trondelagSpotConfig.companyProductId
          : trondelagSpotConfig.productId,
      extraProducts: [],
      distributionMethodId: 3,
      salesTypeId: 1,
      store: {
        productHubId: trondelagSpotConfig.productHubId,
        name: 'Nordvik Eiendomsmegler',
      },
      brandId: 2,
      overrideAccept: true,
      overrideCredit: true,
      contractMail: false,
      customer: {
        firstname: input.firstName || input.lastName || input.companyName,
        lastname: input.lastName || input.companyName,
        ssn: input.ssn?.replace(/\D/g, ''),
        consentOffersNewsletter: false,
        companyName: input.lastName ?? '',
        companyNo: input.organizationNo || '',
        phone: input.phoneNumber,
        email: input.email,
        invoiceAddress: {
          streetAddress: input.billingAddress.streetAdress,
          postalCode: input.billingAddress.zipCode,
        },
      },
      installations: (input.type === 'single' ? [input.meterNumber] : input.meterNumbers).map((meterNumber) => ({
        meterId: '',
        meterNo: meterNumber,
        agreedStartupDate: format(new Date(), 'yyyy-MM-dd'),
        installationAddress: {
          streetAddress: input.address.streetAdress,
          postalCode: input.address.zipCode,
        },
        movingDate: '',
      })),
      partnerInfo: {
        partnerSellerName: input.brokerName,
        partnerSellerCode: input.brokerEmployeeId,
        partnerOrderId: generateExternalLeadHumanReadableId({
          postgresId,
          leadType: NotUnifiedServiceOfferViaApiType.FJORDKRAFT,
          createdAt,
        }),
      },
    };

    const token = await getFjordkraftToken(postgresId);
    const headers = { Authorization: `Bearer ${token}` };
    const response = await axios.post<{ id: number; store: { id: number } }[]>(
      fjordkraftConfig.saleUrl,
      fjordkraftApiBody,
      {
        headers,
        validateStatus: () => true,
      },
    );
    const dataToAudit = {
      request: { method: response.config.method, url: response.config.url, headers, body: fjordkraftApiBody },
      response: {
        statusCode: response.status,
        body: response.data,
      },
      notes: {
        participantID: input.contactId,
        otpId: input.otpId,
        storeId: response.data[0]?.store.id,
      },
    };

    if (!response?.status?.toString().startsWith('2')) {
      await externalLeadAuditRepository.update(postgresId, {
        isSuccessful: false,
        data: dataToAudit,
      });
      throw new Error((response.data as unknown) as string);
    }

    await externalLeadAuditRepository.update(postgresId, {
      isSuccessful: true,
      data: dataToAudit,
      externalLeadId: response.data[0]?.id.toString(),
    });
  };

  const createTrondelagKraftTobbSpotSale = async (input: CreateSaleInput): Promise<void> => {
    const { id: postgresId, createdAt } = await externalLeadAuditRepository.create({
      email: input.email,
      name: getFullNameString(input),
      phone: input.phoneNumber,
      address: input.address.streetAdress || null,
      postalCode: input.address.zipCode || null,
      brokerId: input.brokerEmployeeId,
      departmentOfBroker: input.estateOrBrokerDepartmentName,
      externalLeadId: null,
      leadType: NotUnifiedServiceOfferViaApiType.FJORDKRAFT,
    });

    const fjordkraftApiBody = {
      productHubId: trondelagTobbSpotConfig.productId,
      extraProducts: [],
      distributionMethodId: 3,
      salesTypeId: 1,
      store: {
        productHubId: trondelagTobbSpotConfig.productHubId,
        name: 'Nordvik Eiendomsmegler',
      },
      brandId: 2,
      overrideAccept: true,
      overrideCredit: true,
      contractMail: false,
      customer: {
        firstname: input.firstName || input.lastName || input.companyName,
        lastname: input.lastName || input.companyName,
        ssn: input.ssn?.replace(/\D/g, ''),
        consentOffersNewsletter: false,
        companyName: input.lastName ?? '',
        companyNo: input.organizationNo || '',
        phone: input.phoneNumber,
        email: input.email,
        invoiceAddress: {
          streetAddress: input.billingAddress.streetAdress,
          postalCode: input.billingAddress.zipCode,
        },
      },
      installations: (input.type === 'single' ? [input.meterNumber] : input.meterNumbers).map((meterNumber) => ({
        meterId: '',
        meterNo: meterNumber,
        agreedStartupDate: format(new Date(), 'yyyy-MM-dd'),
        installationAddress: {
          streetAddress: input.address.streetAdress,
          postalCode: input.address.zipCode,
        },
        movingDate: '',
      })),
      partnerInfo: {
        partnerSellerName: input.brokerName,
        partnerSellerCode: input.brokerEmployeeId,
        partnerOrderId: generateExternalLeadHumanReadableId({
          postgresId,
          leadType: NotUnifiedServiceOfferViaApiType.FJORDKRAFT,
          createdAt,
        }),
      },
    };

    const token = await getFjordkraftToken(postgresId);
    const headers = { Authorization: `Bearer ${token}` };
    const response = await axios.post<{ id: number; store: { id: number } }[]>(
      fjordkraftConfig.saleUrl,
      fjordkraftApiBody,
      {
        headers,
        validateStatus: () => true,
      },
    );
    const dataToAudit = {
      request: { method: response.config.method, url: response.config.url, headers, body: fjordkraftApiBody },
      response: {
        statusCode: response.status,
        body: response.data,
      },
      notes: {
        participantID: input.contactId,
        otpId: input.otpId,
        storeId: response.data[0]?.store.id,
      },
    };

    if (!response?.status?.toString().startsWith('2')) {
      await externalLeadAuditRepository.update(postgresId, {
        isSuccessful: false,
        data: dataToAudit,
      });
      throw new Error((response.data as unknown) as string);
    }

    await externalLeadAuditRepository.update(postgresId, {
      isSuccessful: true,
      data: dataToAudit,
      externalLeadId: response.data[0]?.id.toString(),
    });
  };

  const getToken = async (input: {
    postgresId: string;
    tokenUrl: string;
    body: string | Record<string, any>;
  }): Promise<string> => {
    try {
      const response = await axios.post<TokenResponse>(input.tokenUrl, input.body);
      return response.data.access_token;
    } catch (error) {
      if (error instanceof LightAxiosError) {
        await externalLeadAuditRepository.update(input.postgresId, {
          isSuccessful: false,
          data: {
            request: {
              url: input.tokenUrl,
              body: input.body,
            },
            response: {
              statusCode: error.response.status,
              body: error.response.data,
            },
            notes: { type: 'tokenError' },
          },
        });
      }
      throw error;
    }
  };

  return { createSale, createSalesForOtp };
};
