import axios from 'axios';
import * as cache from 'cache-decorator';
import type { FjordkraftConfig, FortumConfig, TrondelagkraftConfig } from '../../config';
import { externalLeadAuditRepositoryFixtureFactory } from '../../framework/test/fixtures/repositories/externalLeadAuditRepositoryFixtureFactory';
import { LeadsServiceFixtureFactory } from '../../framework/test/fixtures/services/leadsServiceFixtureFactory';
import { ContactType } from '../contact/mongo/contact-mongoose';
import type { EstateAddress } from '../estate/mongo/estate.mongoose-types';
import { ElectricityProvider } from '../otp/otp';
import { createElectricityLeadService } from './electicity-lead.service';

// const createNorgesConfig = (): NorgesConfig => ({
//   activityId: 'norgesActivity',
//   // eslint-disable-next-line @typescript-eslint/naming-convention
//   client_id: 'norgesClientId',
//   // eslint-disable-next-line @typescript-eslint/naming-convention
//   client_secret: 'norgesClientSecret',
//   password: 'norgesPassword',
//   saleUrl: 'norgesSaleUrl',
//   tokenUrl: 'norgesTokenUrl',
//   username: 'norgesUsername',
//   customerGroup: 'norgesCustomerGroup',
//   productId: 'norgesProductId',
// });

const createFortumConfig = (): FortumConfig => ({
  password: 'fortumPassword',
  saleUrl: 'fortumSaleUrl',
  tokenUrl: 'fortumTokenUrl',
  username: 'fortumUsername',
  packageId: 'packageId',
  projectId: 'projectId',
});

const createFjordkraftConfig = (): FjordkraftConfig => ({
  client_id: '',
  client_secret: '',
  saleUrl: '',
  tokenUrl: '',
  scope: '',
  privateProductId: 0,
  companyProductId: 0,
  productHubId: 0,
});

const createtrondelagSpotConfig = (): TrondelagkraftConfig => ({
  client_id: '',
  client_secret: '',
  saleUrl: '',
  tokenUrl: '',
  scope: '',
  productId: 0,
  productHubId: 0,
  companyProductId: 0,
});

const createTondelTobbSpotConfig = (): TrondelagkraftConfig => ({
  client_id: '',
  client_secret: '',
  saleUrl: '',
  tokenUrl: '',
  scope: '',
  productId: 0,
  productHubId: 0,
  companyProductId: 0,
});

const createSaleInput = ({
  provider,
  otpId,
  firstName = 'first',
  lastName = 'last',
  companyName,
  contactType,
  brokerName,
  brokerEmployeeId,
  estateOrBrokerDepartmentName,
  address,
}: {
  otpId: string;
  provider: ElectricityProvider;
  brokerName: string;
  brokerEmployeeId: string;
  estateOrBrokerDepartmentName: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  contactType?: ContactType;
  address?: EstateAddress;
}): {
  type: 'single';
  firstName: string;
  lastName: string;
  companyName: string;
  contactType: ContactType;
  contactId: string;
  birthDate: Date;
  otpId: string;
  provider: ElectricityProvider;
  email: string;
  phoneNumber: string;
  address: EstateAddress;
  billingAddress: EstateAddress;
  meterNumber?: string;
  ssn: string;
  organizationNo?: string;
  brokerName: string;
  brokerEmployeeId: string;
  estateOrBrokerDepartmentName: string;
} => ({
  type: 'single',
  firstName,
  lastName,
  companyName: companyName || '',
  contactType: contactType || ContactType.PERSON,
  contactId: 'mockContactId',
  birthDate: new Date(),
  otpId,
  provider,
  email: 'email',
  phoneNumber: 'phone',
  address: address
    ? { ...address }
    : { apartmentNumber: 'num', city: 'city', streetAdress: 'address', zipCode: '1111' },
  billingAddress: { apartmentNumber: 'num', city: 'city', streetAdress: 'address', zipCode: '1111' },
  meterNumber: '10',
  ssn: '*********',
  organizationNo: '11',
  brokerName,
  brokerEmployeeId,
  estateOrBrokerDepartmentName,
});

describe('electricity lead service', () => {
  describe('createSale', () => {
    it('should call Fortum api with Fortum ids in case of Fortum sale', async () => {
      const externalLeadAuditRepository = externalLeadAuditRepositoryFixtureFactory();
      const leadService = LeadsServiceFixtureFactory();
      const createLeadSpy = spyOn(externalLeadAuditRepository, 'create').and.returnValue(
        Promise.resolve({ id: 'pgId', createdAt: new Date() }),
      );
      const updateLeadSpy = spyOn(externalLeadAuditRepository, 'update').and.returnValue(Promise.resolve());
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      jest.spyOn(cache, 'fcache').mockImplementation((callback, _) => () => callback());
      const axiosSpy = jest.spyOn(axios, 'post');
      axiosSpy.mockImplementation(async (url: string) => {
        if (url === 'fortumSaleUrl') {
          return Promise.resolve({
            status: 201,
            config: { method: 'POST', url: 'fortumSaleUrl' },
            data: { Id: 1234 },
          });
        }
        // eslint-disable-next-line @typescript-eslint/naming-convention
        return Promise.resolve({ data: { access_token: 'accessToken' } });
      });
      const service = createElectricityLeadService({
        norgesConfig: createFortumConfig(),
        fortumConfig: createFortumConfig(),
        fjordkraftConfig: createFjordkraftConfig(),
        trondelagSpotConfig: createtrondelagSpotConfig(),
        trondelagTobbSpotConfig: createTondelTobbSpotConfig(),
        leadService,
        externalLeadAuditRepository,
      });

      await service.createSale(
        createSaleInput({
          provider: ElectricityProvider.FORTUM,
          otpId: 'forId',
          brokerName: 'brokerName',
          brokerEmployeeId: 'ANSO',
          estateOrBrokerDepartmentName: 'dep',
        }),
      );

      expect(axiosSpy).toHaveBeenCalledTimes(2);
      expect(axiosSpy).toHaveBeenCalledWith('fortumTokenUrl', {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        grant_type: 'password',
        username: 'fortumUsername',
        password: 'fortumPassword',
      });
      expect(axiosSpy).toHaveBeenCalledWith(
        'fortumSaleUrl',
        expect.objectContaining({
          ProductPackage: { ExternalPackageId: 'packageId' },
          Project: {
            ProjectId: 'projectId',
          },
        }),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        { headers: { Authorization: 'Bearer accessToken' }, validateStatus: expect.any(Function) },
      );
      expect(createLeadSpy).toHaveBeenCalledWith({
        email: 'email',
        name: `first last`,
        phone: 'phone',
        address: 'address',
        postalCode: '1111',
        brokerId: 'ANSO',
        departmentOfBroker: 'dep',
        externalLeadId: null,
        leadType: 'FORTUM',
      });
      expect(updateLeadSpy).toHaveBeenCalledWith('pgId', {
        isSuccessful: true,
        data: {
          request: {
            method: 'POST',
            url: 'fortumSaleUrl',
            headers: { Authorization: 'Bearer accessToken' },
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            body: expect.objectContaining({
              ProductPackage: { ExternalPackageId: 'packageId' },
              Project: {
                ProjectId: 'projectId',
              },
            }),
          },
          response: {
            statusCode: 201,
            body: { Id: 1234 },
          },
          notes: { participantID: 'mockContactId', otpId: 'forId' },
        },
        externalLeadId: '1234',
      });
      axiosSpy.mockClear();
    });

    it('should call Fortum api with correct street address', async () => {
      const externalLeadAuditRepository = externalLeadAuditRepositoryFixtureFactory();
      const leadService = LeadsServiceFixtureFactory();
      spyOn(externalLeadAuditRepository, 'create').and.returnValue(
        Promise.resolve({ id: 'pgId', createdAt: new Date() }),
      );
      spyOn(externalLeadAuditRepository, 'update').and.returnValue(Promise.resolve());
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      jest.spyOn(cache, 'fcache').mockImplementation((callback, _) => () => callback());
      const axiosSpy = jest.spyOn(axios, 'post');
      axiosSpy.mockImplementation(async (url: string) => {
        if (url === 'fortumSaleUrl') {
          return Promise.resolve({
            status: 201,
            config: { method: 'POST', url: 'fortumSaleUrl' },
            data: { Id: 1234 },
          });
        }
        // eslint-disable-next-line @typescript-eslint/naming-convention
        return Promise.resolve({ data: { access_token: 'accessToken' } });
      });
      const service = createElectricityLeadService({
        norgesConfig: createFortumConfig(),
        fortumConfig: createFortumConfig(),
        fjordkraftConfig: createFjordkraftConfig(),
        trondelagSpotConfig: createtrondelagSpotConfig(),
        trondelagTobbSpotConfig: createTondelTobbSpotConfig(),
        leadService,
        externalLeadAuditRepository,
      });

      await service.createSale(
        createSaleInput({
          provider: ElectricityProvider.FORTUM,
          otpId: 'forId',
          brokerName: 'brokerName',
          brokerEmployeeId: 'ANSO',
          estateOrBrokerDepartmentName: 'dep',
          address: { streetAdress: 'Some street 79b', apartmentNumber: '', city: '', zipCode: '' },
        }),
      );

      expect(axiosSpy).toHaveBeenCalledTimes(2);
      expect(axiosSpy).toHaveBeenCalledWith('fortumTokenUrl', {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        grant_type: 'password',
        username: 'fortumUsername',
        password: 'fortumPassword',
      });
      expect(axiosSpy).toHaveBeenCalledWith(
        'fortumSaleUrl',
        expect.objectContaining({
          DeliveryStreet: 'Some street',
          DeliveryStreetNumber: '79b',
        }),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        { headers: { Authorization: 'Bearer accessToken' }, validateStatus: expect.any(Function) },
      );
      axiosSpy.mockClear();
    });

    // it('should call Norges api with Norges ids in case of Norges person sale', async () => {
    //   const externalLeadAuditRepository = externalLeadAuditRepositoryFixtureFactory();
    //   const leadService = LeadsServiceFixtureFactory();
    //   const createLeadSpy = spyOn(externalLeadAuditRepository, 'create').and.returnValue(
    //     Promise.resolve({ id: 12345678, createdAt: new Date() }),
    //   );
    //   const updateLeadSpy = spyOn(externalLeadAuditRepository, 'update').and.returnValue(Promise.resolve());
    //   // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    //   jest.spyOn(cache, 'fcache').mockImplementation((callback, _) => () => callback());
    //   const axiosSpy = jest.spyOn(axios, 'post');
    //   axiosSpy.mockImplementation(async (url: string) => {
    //     if (url === 'norgesSaleUrl') {
    //       return Promise.resolve({
    //         status: 201,
    //         config: { method: 'POST', url: 'norgesSaleUrl' },
    //         data: { requestId: 'reqId' },
    //       });
    //     }
    //     // eslint-disable-next-line @typescript-eslint/naming-convention
    //     return Promise.resolve({ data: { access_token: 'accessToken' } });
    //   });
    //   const service = createElectricityLeadService({
    //     norgesConfig: createFortumConfig(),
    //     fortumConfig: createFortumConfig(),
    //     fjordkraftConfig: createFjordkraftConfig(),
    //     trondelagSpotConfig: createtrondelagSpotConfig(),
    //     trondelagTobbSpotConfig: createTondelTobbSpotConfig(),
    //     externalLeadAuditRepository,
    //     leadService,
    //   });

    //   await service.createSale(
    //     createSaleInput({
    //       provider: ElectricityProvider.NORGES_ENERGY,
    //       otpId: 'forId',
    //       firstName: 'first',
    //       lastName: 'last',
    //       brokerName: 'brokerName',
    //       brokerEmployeeId: 'ANSO',
    //       estateOrBrokerDepartmentName: 'dep',
    //     }),
    //   );

    //   expect(axiosSpy).toHaveBeenCalledTimes(2);
    //   expect(axiosSpy).toHaveBeenCalledWith('norgesTokenUrl', {
    //     grant_type: 'password',
    //     username: 'norgesUsername',
    //     password: 'norgesPassword',
    //   });
    //   expect(axiosSpy).toHaveBeenCalledWith(
    //     'norgesSaleUrl',
    //     expect.objectContaining({
    //       FirstName: 'first',
    //       LastName: 'last',
    //       Company: '',
    //       CustomerType: '1',
    //     }),
    //     // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    //     { headers: { Authorization: 'Bearer accessToken' }, validateStatus: expect.any(Function) },
    //   );

    //   expect(createLeadSpy).toHaveBeenCalledWith({
    //     email: 'email',
    //     name: `first last`,
    //     phone: 'phone',
    //     address: 'address',
    //     postalCode: '1111',
    //     brokerId: 'ANSO',
    //     departmentOfBroker: 'dep',
    //     externalLeadId: null,
    //     leadType: 'NORGES_ENERGY',
    //   });
    //   expect(updateLeadSpy).toHaveBeenCalledWith(12345678, {
    //     isSuccessful: true,
    //     data: {
    //       request: {
    //         method: 'POST',
    //         url: 'norgesSaleUrl',
    //         headers: { Authorization: 'Bearer accessToken' },
    //         // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    //         body: expect.objectContaining({
    //           FirstName: 'first',
    //           LastName: 'last',
    //           Company: '',
    //           CustomerType: '1',
    //           SalesId: 12345678,
    //         }),
    //       },
    //       response: {
    //         statusCode: 201,
    //         body: { requestId: 'reqId' },
    //       },
    //       notes: { participantID: 'mockContactId', otpId: 'forId' },
    //     },
    //     externalLeadId: 'reqId',
    //   });
    //   axiosSpy.mockClear();
    // });

    // it('should call Norges api with Norges ids in case of Norges company sale', async () => {
    //   const externalLeadAuditRepository = externalLeadAuditRepositoryFixtureFactory();
    //   const leadService = LeadsServiceFixtureFactory();
    //   const createLeadSpy = spyOn(externalLeadAuditRepository, 'create').and.returnValue(
    //     Promise.resolve({ id: 12345678, createdAt: new Date() }),
    //   );
    //   const updateLeadSpy = spyOn(externalLeadAuditRepository, 'update').and.returnValue(Promise.resolve());
    //   // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    //   jest.spyOn(cache, 'fcache').mockImplementation((callback, _) => () => callback());
    //   const axiosSpy = jest.spyOn(axios, 'post');
    //   axiosSpy.mockImplementation(async (url: string) => {
    //     if (url === 'norgesSaleUrl') {
    //       return Promise.resolve({
    //         status: 201,
    //         config: { method: 'POST', url: 'norgesSaleUrl' },
    //         data: { requestId: 'reqId' },
    //       });
    //     }
    //     // eslint-disable-next-line @typescript-eslint/naming-convention
    //     return Promise.resolve({ data: { access_token: 'accessToken' } });
    //   });
    //   const service = createElectricityLeadService({
    //     norgesConfig: createFortumConfig(),
    //     fortumConfig: createFortumConfig(),
    //     fjordkraftConfig: createFjordkraftConfig(),
    //     trondelagSpotConfig: createtrondelagSpotConfig(),
    //     trondelagTobbSpotConfig: createTondelTobbSpotConfig(),
    //     externalLeadAuditRepository,
    //     leadService,
    //   });

    //   await service.createSale(
    //     createSaleInput({
    //       provider: ElectricityProvider.NORGES_ENERGY,
    //       otpId: 'forId',
    //       firstName: '',
    //       lastName: '',
    //       companyName: 'company',
    //       contactType: ContactType.COMPANY,
    //       brokerName: 'brokerName',
    //       brokerEmployeeId: 'ANSO',
    //       estateOrBrokerDepartmentName: 'dep',
    //     }),
    //   );

    //   expect(axiosSpy).toHaveBeenCalledTimes(2);
    //   expect(axiosSpy).toHaveBeenCalledWith('norgesTokenUrl', {
    //     grant_type: 'password',
    //     username: 'norgesUsername',
    //     password: 'norgesPassword',
    //   });
    //   expect(axiosSpy).toHaveBeenCalledWith(
    //     'norgesSaleUrl',
    //     expect.objectContaining({
    //       FirstName: 'company',
    //       LastName: 'company',
    //       Company: 'company',
    //       CustomerType: '2',
    //     }),
    //     // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    //     { headers: { Authorization: 'Bearer accessToken' }, validateStatus: expect.any(Function) },
    //   );
    //   expect(createLeadSpy).toHaveBeenCalledWith({
    //     email: 'email',
    //     name: 'company',
    //     phone: 'phone',
    //     address: 'address',
    //     postalCode: '1111',
    //     brokerId: 'ANSO',
    //     departmentOfBroker: 'dep',
    //     externalLeadId: null,
    //     leadType: 'NORGES_ENERGY',
    //   });
    //   expect(updateLeadSpy).toHaveBeenCalledWith(12345678, {
    //     isSuccessful: true,
    //     data: {
    //       request: {
    //         method: 'POST',
    //         url: 'norgesSaleUrl',
    //         headers: { Authorization: 'Bearer accessToken' },
    //         // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    //         body: expect.objectContaining({
    //           FirstName: 'company',
    //           LastName: 'company',
    //           Company: 'company',
    //           CustomerType: '2',
    //           SalesId: 12345678,
    //         }),
    //       },
    //       response: {
    //         statusCode: 201,
    //         body: { requestId: 'reqId' },
    //       },
    //       notes: { participantID: 'mockContactId', otpId: 'forId' },
    //     },
    //     externalLeadId: 'reqId',
    //   });
    //   axiosSpy.mockClear();
    // });
  });
});
