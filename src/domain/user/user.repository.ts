import { ResourceNotFound } from '../../framework/errors/resource.errors';
import type { PasswordHelper } from '../password/password.helper';
import { UserAreaModel } from '../user-area/user-area.model';
import { userAreaTransformer } from '../user-area/user-area.repository';
import { UserOptionsModel } from '../user-options/user-options.model';
import { matchingUserQueryFactory, userOptionsTransformer } from '../user-options/user-options.repository';
import type { DeletedUser } from './deleted-user';
import { DeletedUserModel } from './deleteUser.model';
import type { RegisteredWith, User, UserUpdates } from './user';
import type { UserConsentGroups } from './user-consent-settings';
import { UserModel } from './user.model';

export type UserRepository = {
  createUser(user: {
    name: User['name'];
    phoneNumber: User['phoneNumber'];
    email: User['email'];
    password: User['password'];
    bankIdVerified: User['bankIdVerified'];
    birthday: User['birthday'];
    registeredWith: User['registeredWith'];
    consentSettings: User['consentSettings'];
  }): Promise<User>;
  createPasswordlessUser(user: {
    name: User['name'];
    phoneNumber: User['phoneNumber'];
    email: User['email'];
    bankIdVerified: User['bankIdVerified'];
    birthday: User['birthday'];
    registeredWith: User['registeredWith'];
  }): Promise<User>;
  deleteUserByID(id: User['id'], reason: string): Promise<DeletedUser | null>;
  getUserByID(id: User['id']): Promise<User | null>;
  getUserByEmail(email: User['email']): Promise<User | null>;
  getUserByPhone(phoneNumber: User['phoneNumber']): Promise<User | null>;
  updateUser(id: User['id'], updates: UserUpdates): Promise<User>;
  hasAppByEmails(emails: string[]): Promise<{ email: string; hasApp: boolean }[]>;
  getMatchingUsers({ area, price }: { area: number; price: number }): Promise<User[]>;
  getUserByReferralID(referralID: string): Promise<User | null>;
  searchUsers(query: string, limit: number): Promise<User[]>;
};

export const userTransformer = (userModel: UserModel): User => {
  const baseUser = {
    id: userModel.id,
    createdAt: userModel.createdAt,
    updatedAt: userModel.updatedAt,
    name: userModel.name,
    email: userModel.email.toLowerCase(),
    phoneNumber: userModel.phoneNumber,
    password: userModel.password,
    bankIdVerified: userModel.bankIdVerified,
    passwordCode: userModel.passwordCode,
    verifyPasswordCode: userModel.verifyPasswordCode,
    birthday: new Date(userModel.birthday),
    areas: userModel.userAreas?.map(userAreaTransformer).map((userArea) => userArea.areaID) || [],
    annualIncome: userModel.annualIncome,
    sumOfOtherLoans: userModel.sumOfOtherLoans,
    referralCode: userModel.referralCode,
    visitedChecklist: userModel.visitedChecklist,
    visitedSalesProcess: userModel.visitedSalesProcess,
    registeredWith: userModel.registeredWith,
    closedTutorialAt: userModel.closedTutorialAt,
    pushNotificationSettings: userModel.pushNotificationSettings,
    consentSettings: userModel.consentSettings,
  };
  if (!userModel.userOptions) {
    return baseUser;
  }
  const userOptionsModel = userModel.userOptions;
  return {
    ...baseUser,
    options: userOptionsTransformer(userOptionsModel),
  };
};

export const userRepositoryFactory = ({ passwordHelper }: { passwordHelper: PasswordHelper }): UserRepository => {
  const createUser = async (user: {
    name: string;
    phoneNumber: string;
    email: string;
    password: string;
    bankIdVerified: boolean;
    birthday: User['birthday'];
    registeredWith: RegisteredWith;
    consentSettings: UserConsentGroups;
  }): Promise<User> => {
    const hashedPassword = await passwordHelper.hash(user.password);
    const newUser = await UserModel.create({
      name: user.name,
      phoneNumber: user.phoneNumber,
      email: user.email.toLowerCase(),
      password: hashedPassword,
      bankIdVerified: user.bankIdVerified,
      birthday: user.birthday,
      registeredWith: user.registeredWith,
      consentSettings: user.consentSettings,
    });

    return userTransformer(newUser);
  };

  const createPasswordlessUser = async (user: {
    name: string;
    phoneNumber: string;
    email: string;
    bankIdVerified: boolean;
    birthday: User['birthday'];
    registeredWith: RegisteredWith;
  }): Promise<User> => {
    const newUser = await UserModel.create({
      name: user.name,
      phoneNumber: user.phoneNumber,
      email: user.email.toLowerCase(),
      bankIdVerified: user.bankIdVerified,
      birthday: user.birthday,
      registeredWith: user.registeredWith,
    });

    return userTransformer(newUser);
  };

  const getUserByID = async (id: User['id']): Promise<User | null> => {
    const selectedUser = await UserModel.findOne({
      where: {
        id,
      },
      include: [
        {
          model: UserOptionsModel,
          as: 'userOptions',
          where: {
            userID: id,
          },
          required: false,
        },
        {
          model: UserAreaModel,
          as: 'userAreas',
          where: {
            userID: id,
          },
          required: false,
        },
      ],
    });
    if (!selectedUser) {
      return null;
    }
    return userTransformer(selectedUser);
  };

  const getUserByEmail = async (email: User['email']): Promise<User | null> => {
    const selectedUser = await UserModel.findOne({
      where: {
        email: email.toLowerCase(),
      },
      include: [
        {
          model: UserOptionsModel,
          as: 'userOptions',
          required: false,
        },
      ],
    });

    if (!selectedUser) {
      return null;
    }

    return userTransformer(selectedUser);
  };

  const getUserByPhone = async (phoneNumber: User['phoneNumber']): Promise<User | null> => {
    const selectedUser = await UserModel.findOne({
      where: {
        phoneNumber,
      },
      include: [
        {
          model: UserOptionsModel,
          as: 'userOptions',
          required: false,
        },
      ],
    });

    if (!selectedUser) {
      return null;
    }

    return userTransformer(selectedUser);
  };
  const updateUser = async (id: User['id'], updates: UserUpdates): Promise<User> => {
    const updatableUser = await UserModel.findOne({
      where: {
        id,
      },
    });
    if (!updatableUser) {
      throw new ResourceNotFound('user not found');
    }
    const updatedUser = await updatableUser.update({
      ...updates,
      updatedAt: new Date(),
      email: updates.email?.toLowerCase(),
    });
    return userTransformer(updatedUser);
  };

  const deleteUserByID = async (id: User['id'], reason: string): Promise<DeletedUser | null> => {
    const userToBeDeleted = await UserModel.findOne({
      where: {
        id,
      },
    });

    if (!userToBeDeleted) {
      return null;
    }

    await userToBeDeleted.destroy();

    const deletedUser = await DeletedUserModel.create({ email: userToBeDeleted.email, reason: reason });

    return deletedUser.toJSON();
  };

  const hasAppByEmails = async (emails: string[]): Promise<{ email: string; hasApp: boolean }[]> => {
    const selectedUsers = await UserModel.findAll({
      where: {
        email: emails.map((e) => e.toLowerCase()),
      },
    });
    const selectedUsersMail = selectedUsers.map((u) => u.email);
    return emails.map((email) => {
      if (selectedUsersMail.includes(email)) {
        return { email, hasApp: true };
      }
      return { email, hasApp: false };
    });
  };

  const getMatchingUsers = async ({ area, price }: { area: number; price: number }): Promise<User[]> => {
    const selecedUsers = await UserModel.findAll({
      include: [
        {
          model: UserOptionsModel,
          as: 'userOptions',
          required: true,
          where: {
            ...matchingUserQueryFactory({ area, price }),
          },
        },
      ],
    });
    return selecedUsers.map((u) => userTransformer(u));
  };

  const getUserByReferralID = async (referralID: string): Promise<User | null> => {
    const selectedUser = await UserModel.findOne({
      where: { referralCode: referralID },
    });
    if (!selectedUser) {
      return null;
    }
    return userTransformer(selectedUser);
  };

  const searchUsers = async (query: string, limit: number): Promise<User[]> => {
    const { Op } = require('sequelize');
    const selectedUsers = await UserModel.findAll({
      where: {
        [Op.or]: [
          { email: { [Op.iLike]: `%${query}%` } },
          { name: { [Op.iLike]: `%${query}%` } },
          { phoneNumber: { [Op.iLike]: `%${query}%` } },
        ],
      },
      limit,
      order: [['createdAt', 'DESC']],
    });
    return selectedUsers.map(userTransformer);
  };

  return {
    getUserByID,
    getUserByEmail,
    getUserByPhone,
    createUser,
    createPasswordlessUser,
    updateUser,
    hasAppByEmails,
    getMatchingUsers,
    deleteUserByID,
    getUserByReferralID,
    searchUsers,
  };
};
