import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';

export const AVERAGE_SALE_PRICE_TABLE_NAME = 'AverageSalePrice';

export const averageSalePriceTableAttributes: ModelAttributes = {
  ...baseModelAttributes,
  averageTimeForPropertySale: {
    type: DataTypes.DECIMAL(32, 4),
    allowNull: true,
  },
  averageTimeForPropertySaleCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  averagePricePerSquareMeter: {
    type: DataTypes.DECIMAL(32, 4),
    allowNull: true,
  },
  averagePricePerSquareMeterCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  averagePriceAbsolute: {
    type: DataTypes.DECIMAL(32, 4),
    allowNull: true,
  },
  averagePriceAbsoluteCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  postalCode: {
    type: DataTypes.STRING,
    allowNull: true,
  },
};

export class AverageSalePriceModel extends ResourceModel {
  public averageTimeForPropertySale: number | null;
  public averageTimeForPropertySaleCount: number;
  public averagePricePerSquareMeter: number | null;
  public averagePricePerSquareMeterCount: number;
  public averagePriceAbsolute: number | null;
  public averagePriceAbsoluteCount: number;
  public postalCode: string | null;
}

export const averageSalePriceModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  AverageSalePriceModel.init(averageSalePriceTableAttributes, {
    sequelize,
    tableName: AVERAGE_SALE_PRICE_TABLE_NAME,
    timestamps: true,
  });
};
