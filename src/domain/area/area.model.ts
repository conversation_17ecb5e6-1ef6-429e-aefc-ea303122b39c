import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';

export const AREAS_TABLE_NAME = 'Areas';

export const areaAttributes: ModelAttributes = {
  ...baseModelAttributes,
  name: {
    type: DataTypes.TEXT,
  },
  parentId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'Areas',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  },
  weight: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  postalCodes: {
    type: DataTypes.ARRAY(DataTypes.TEXT),
    defaultValue: [],
  },
};

export class AreaModel extends ResourceModel {
  public name: string;
  public subAreas: AreaModel[];
  public weight: number;
  public postalCodes: string[];
}

export const areaModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  AreaModel.init(areaAttributes, {
    sequelize,
    tableName: AREAS_TABLE_NAME,
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['name', 'parentId'],
      },
    ],
  });
};

export const setAreaModelReferences = (): void => {
  AreaModel.hasMany(AreaModel, { foreignKey: 'parentId', as: 'subAreas' });
};
