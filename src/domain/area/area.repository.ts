import { flatten } from 'ramda';
import { Op } from 'sequelize';
import { norwegianComparator } from '../collat/collat';
import type { Area } from './area';
import { AreaModel } from './area.model';

export type AreaRepository = {
  getAreas(): Promise<Area[]>;
  getPostalCodesByIds(ids: Area['id'][]): Promise<string[]>;
  getAreaByPostalCode(postalCode: string): Promise<Area | null>;
};

export const areaTransformer = (areaModel: AreaModel): Area => {
  const area = {
    id: areaModel.id,
    createdAt: areaModel.createdAt,
    updatedAt: areaModel.updatedAt,
    name: areaModel.name,
    subAreas: areaModel.subAreas?.map(areaTransformer) || [],
    weight: areaModel.weight,
    postalCodes: areaModel.postalCodes,
  };

  return area;
};

export type AreaDto = {
  id: string;
  name: string;
  children: AreaDto[];
};

export type Output = {
  areas: AreaDto[];
};

export const areaRepositoryFactory = (): AreaRepository => {
  const areaComparator = (lhs: Area, rhs: Area): number => {
    if (rhs.weight > lhs.weight) {
      return 1;
    }

    return norwegianComparator(lhs.name, rhs.name);
  };

  const getAreas = async (): Promise<Area[]> => {
    const areaModels = await AreaModel.findAll({
      include: [
        {
          model: AreaModel,
          as: 'subAreas',
          required: true,
        },
      ],
    });

    return areaModels
      .map(areaTransformer)
      .sort(areaComparator)
      .map((areaModel) => {
        return {
          ...areaModel,
          subAreas: areaModel.subAreas.sort(areaComparator),
        };
      });
  };

  const getPostalCodesByIds = async (ids: Area['id'][]): Promise<string[]> => {
    const areaModels = await AreaModel.findAll({
      where: {
        id: ids,
      },
    });

    return flatten(areaModels.map((area) => area.postalCodes));
  };

  const getAreaByPostalCode = async (postalCode: string): Promise<Area | null> => {
    return AreaModel.findOne({
      where: {
        postalCodes: {
          [Op.contains]: [postalCode],
        },
      },
    });
  };

  return {
    getAreas,
    getPostalCodesByIds,
    getAreaByPostalCode,
  };
};
