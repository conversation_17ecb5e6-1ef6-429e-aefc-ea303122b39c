/* eslint-disable @typescript-eslint/naming-convention */
import type { VitecService } from '../../framework/vitec/vitec-service';
import { logger } from '../../logger';
import { LightAxiosError } from '../../utils/axios-logger.utils';
import type { EstateMongooseRepository } from '../estate/mongo/estate.mogoose-repository';
import type { EstateAddress } from '../estate/mongo/estate.mongoose-types';
import { generateExternalLeadHumanReadableId } from '../external-lead-audit/external-lead-audit';
import type { ExternalLeadAuditRepository } from '../external-lead-audit/external-lead-audit.repository';
import type { LeadsService } from '../leads/leads.service';
import { ServiceOfferViaApiType } from '../offers/service-offer';
import type { SettlementBuyerRepository } from '../settlement-buyer/settlement-buyer.repository';
import type { SettlementSellerRepository } from '../settlement-seller/settlement-seller.repository';
import { UserActivityType } from '../user-activity/user-activity';
import type { UserActivityRepository } from '../user-activity/user-activity.repository';
import { StorebrandAuditType } from './storebrand-audit';
import type { StorebrandAuditRepository } from './storebrand-audit.repository';
import type { StorebrandLead } from './storebrand.transformer';
import {
  getFormattedAddressString,
  storebrandAddressTransformer,
  storebrandNameTransformer,
} from './storebrand.transformer';

export enum OriginType {
  NextInternal = 1,
  External = 2,
  FromBank = 3,
  ToBank = 4,
}

export type StorebrandLeadPushRequest = StorebrandLead[];
export type StorebrandLeadPushResponse = string;

type SendLeadOptsBase = {
  name: string;
  phone: string;
  email: string;
  hasCoBuyer?: boolean;
  buyProcess?: string;
  address?: EstateAddress;
  postCode?: string;
  estateId?: string;
};

type SendLeadOpts = SendLeadOptsBase & {
  userOrParticipantID: string | null;
  settlementBuyerOrSellerId: string | null;
};
type SendLeadForParticipantOpts = SendLeadOptsBase & {
  participantId: string | null;
  settlementBuyerOrSellerId: string | null;
};
type SendLeadForUserOpts = SendLeadOptsBase & { userId: string | null };

export type StorebrandService = {
  sendLeadForUser: (opts: SendLeadForUserOpts) => Promise<void>;
  sendLeadForParticipant: (opts: SendLeadForParticipantOpts) => Promise<void>;
};

export const storebrandVitecServiceFactory = ({
  vitecService,
  storebrandAuditRepository,
  userActivityRepository,
  externalLeadAuditRepository,
  leadService,
  settlementBuyerRepository,
  settlementSellerRepository,
  estateRepository,
}: {
  vitecService: VitecService;
  storebrandAuditRepository: StorebrandAuditRepository;
  userActivityRepository: UserActivityRepository;
  externalLeadAuditRepository: ExternalLeadAuditRepository;
  leadService: LeadsService;
  settlementBuyerRepository: SettlementBuyerRepository;
  settlementSellerRepository: SettlementSellerRepository;
  estateRepository: EstateMongooseRepository;
}): StorebrandService => {
  const sendLead = async ({
    userOrParticipantID,
    name,
    phone,
    email,
    postCode,
    hasCoBuyer,
    buyProcess,
    estateId,
    address,
    settlementBuyerOrSellerId,
  }: SendLeadOpts): Promise<void> => {
    const {
      employeeId: brokerEmployeeId,
      departmentName: departmentOfEstate,
      departmentPostalCode,
    } = await leadService.getBrokerDetailsFromUserPhoneOrEmail({ phone, email });

    const { id: postgresId, createdAt } = await externalLeadAuditRepository.create({
      email,
      name,
      phone,
      address: getFormattedAddressString(address),
      postalCode: address?.zipCode || departmentPostalCode || null,
      brokerId: brokerEmployeeId,
      departmentOfBroker: departmentOfEstate,
      externalLeadId: null,
      leadType: ServiceOfferViaApiType.STOREBRAND_VITEC,
    });

    let vitecEstateId: string | undefined = estateId;

    try {
      // Fetch estateId if settlementBuyerOrSellerId is provided
      if (!vitecEstateId && settlementBuyerOrSellerId) {
        const [[seller], [buyer]] = await Promise.all([
          settlementSellerRepository
            .getWithCustomQueryWithoutConnections({
              where: {
                id: settlementBuyerOrSellerId,
              },
            })
            .catch(() => []), // Handle case where seller is not found
          settlementBuyerRepository
            .getWithCustomQueryWithoutConnections({
              where: {
                id: settlementBuyerOrSellerId,
              },
            })
            .catch(() => []), // Handle case where buyer is not found
        ]);

        if (seller?.estateVitecId) {
          vitecEstateId = seller.estateVitecId;
        } else if (buyer?.estateVitecId) {
          vitecEstateId = buyer.estateVitecId;
        } else {
          logger.warn(
            `No estate found for settlementBuyerOrSellerId: ${settlementBuyerOrSellerId}. Using null for estateId.`,
          );
        }
      }
    } catch (error) {
      logger.error(error as Error, 'Error while fetching estateId for settlementBuyerOrSellerId');
    }

    const estate = vitecEstateId
      ? await estateRepository.findEstateByVitecEstateId(vitecEstateId).catch(() => null)
      : null;

    address = address || estate?.address;

    if (!postCode && !estate) {
      try {
        const contact = await vitecService.get<{
          email?: string;
          mobilePhone?: string;
          firstName?: string;
          lastName?: string;
          postalCode?: string;
        }>({
          endpoint: 'Contact/find',
          tryCount: 2,
          config: {
            params: {
              email: email || undefined,
              mobilePhone: phone || undefined,
              firstName: name.split(' ')[0] || undefined,
              lastName: name.split(' ').slice(1).join(' ') || undefined,
            },
          },
        });

        if (contact?.postalCode) {
          postCode = contact.postalCode;
        } else {
          logger.warn(
            `No postal code found for contact with email: ${email}, phone: ${phone}, name: ${name}. Using empty string.`,
          );
          postCode = '';
        }
      } catch (error) {
        logger.error(error as Error, 'Error while fetching contact postal code from Vitec');
        postCode = '';
      }
    }

    if (!postCode) {
      logger.warn(
        `No postal code found for contact with email: ${email}, phone: ${phone}, name: ${name}. Using department postcode as fallback.`,
      );
      postCode = departmentPostalCode || '';
    }

    const externalLeadHumanReadableId = generateExternalLeadHumanReadableId({
      postgresId,
      createdAt,
      leadType: ServiceOfferViaApiType.STOREBRAND_VITEC,
    });

    const body = await getRequestBodyAndHeaders({
      storebrandAuditRepository,
      userActivityRepository,
      userOrParticipantID,
      name,
      phone,
      email,
      hasCoBuyer,
      buyProcess,
      address,
      postCode,
      externalLeadHumanReadableId,
      brokerEmployeeId: brokerEmployeeId || '',
    });

    let comment = '';

    if (buyProcess) {
      comment = `Kjøpsprosess: ${buyProcess}`;
    }

    if (hasCoBuyer) {
      comment += `\nHar medkjøper`;
    }

    const tipCreateRequest = {
      firstName: body.firstName,
      lastName: body.lastName,
      mobilePhone: body.mobilePhone,
      email: body.email || null,
      postalCode: body.postalCode || address?.zipCode || estate?.address.zipCode || '0000',
      streetAdress: body.address || address?.streetAdress || null,
      type: OriginType.ToBank,
      userId: brokerEmployeeId || null,
      comment,
      source: 'nordvik-app',
      estateId: estate ? vitecEstateId : undefined,
    };

    try {
      await vitecService.post({
        endpoint: 'Tips',
        body: tipCreateRequest,
        tryCount: 3,
      });

      await externalLeadAuditRepository.update(postgresId, {
        isSuccessful: true,
        data: {
          request: { method: 'POST', url: '/Tips', body: tipCreateRequest },
          response: { statusCode: 200, body: 'Success' },
          notes: { userOrParticipantID },
        },
      });
    } catch (error) {
      if (error instanceof LightAxiosError) {
        await externalLeadAuditRepository.update(postgresId, {
          isSuccessful: false,
          data: {
            request: { method: error.request.method, url: error.request.url, body },
            response: {
              statusCode: error.response?.status || 500,
              body: (error.response?.data as string) || '',
            },
            notes: { userOrParticipantID },
          },
        });
        logger.error(error, 'Error while calling Storebrand API');
      } else {
        logger.error(error as Error, 'Error while saving Storebrand Audit to External Lead Audit Repo');
      }

      throw error;
    }
  };

  const sendLeadForUser = async ({
    userId,
    name,
    phone,
    email,
    postCode,
    hasCoBuyer,
    buyProcess,
    estateId,
    address,
  }: SendLeadForUserOpts): Promise<void> => {
    try {
      await userActivityRepository.create(userId, UserActivityType.REQUEST_STOREBRAND_SERVICE, {});
    } catch (error) {
      logger.error(error as Error, 'Error while saving Storebrand User Activity');
    }

    await sendLead({
      userOrParticipantID: userId,
      name,
      phone,
      email,
      address,
      estateId,
      postCode,
      hasCoBuyer,
      buyProcess,
      settlementBuyerOrSellerId: null,
    });
  };

  const sendLeadForParticipant = async ({
    participantId,
    name,
    phone,
    email,
    hasCoBuyer,
    buyProcess,
    address,
    settlementBuyerOrSellerId,
  }: SendLeadForParticipantOpts): Promise<void> => {
    await sendLead({
      userOrParticipantID: participantId,
      name,
      phone,
      email,
      hasCoBuyer,
      buyProcess,
      address,
      settlementBuyerOrSellerId,
    });
  };

  return {
    sendLeadForUser,
    sendLeadForParticipant,
  };
};

const getRequestBodyAndHeaders = async ({
  userOrParticipantID,
  storebrandAuditRepository,
  name,
  email,
  phone,
  hasCoBuyer,
  buyProcess,
  address,
  postCode,
  externalLeadHumanReadableId,
  brokerEmployeeId,
}: SendLeadOptsBase & {
  userOrParticipantID: string | null;
  storebrandAuditRepository: StorebrandAuditRepository;
  userActivityRepository: UserActivityRepository;
  externalLeadHumanReadableId: string;
  brokerEmployeeId: string;
}): Promise<StorebrandLead> => {
  const lead: StorebrandLead = {
    ...storebrandAddressTransformer(address),
    ...storebrandNameTransformer(name),
    mobilePhone: phone,
    email: email,
    hasCoBuyer,
    postalCode: postCode || '', // @Tamo this has to be set.
    partnerLeadId: externalLeadHumanReadableId,
    buyProcess,
    brokerId: brokerEmployeeId,
  };

  await storebrandAuditRepository.create(userOrParticipantID, StorebrandAuditType.SERVER_LEAD_SEND, {
    lead,
  });

  return lead;
};
