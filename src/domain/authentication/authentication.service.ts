import { Unauthorized } from '../../framework/errors/authentication.errors';
import { APIError } from '../../framework/errors/errors';
import type { BrokerRepository } from '../broker/broker.repository';
import type { Identity } from '../identity/identity';
import { UserRole } from '../identity/identity';
import type { JWTService } from '../token/jwt-token.service';
import type { UserRepository } from '../user/user.repository';

export type AuthenticationService = {
  getIdentity(userID: string): Promise<Identity>;
  authenticate(token: string): Promise<Identity>;
};
export const authenticationServiceFactory = ({
  userRepository,
  tokenService,
  brokerRepository,
}: {
  userRepository: UserRepository;
  tokenService: JWTService;
  brokerRepository: BrokerRepository;
}): AuthenticationService => {
  const getIdentity = async (userID: string): Promise<Identity & { passwordCode: string }> => {
    const user = await userRepository.getUserByID(userID);
    if (user) {
      return { userID, role: UserRole.USER, passwordCode: user.passwordCode, phoneNumber: user.phoneNumber };
    }
    const broker = await brokerRepository.getBrokerByID(userID);
    if (broker) {
      return { userID, role: UserRole.BROKER, passwordCode: broker.passwordCode, phoneNumber: null };
    }
    throw new Unauthorized('user not found');
  };

  const authenticate = async (token: string): Promise<Identity> => {
    try {
      const decodedIdentity = tokenService.decode(token);
      if (!decodedIdentity) {
        throw new Unauthorized('invalid identity');
      }
      const identity = await getIdentity(decodedIdentity.userID);
      tokenService.verify(token, identity.passwordCode);
      return identity;
    } catch (error) {
      if (error instanceof APIError) {
        throw new Unauthorized('invalid identity');
      }
      throw error;
    }
  };

  return { getIdentity, authenticate };
};
