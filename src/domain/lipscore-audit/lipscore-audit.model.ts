import type { ModelAttributes, Sequelize } from 'sequelize';
import { ResourceModel } from '../../framework/sequelize/resource';
import {
  lipscoreAuditAttributes as baseModelAttributes,
  LIPSCORE_AUDIT_TABLE_NAME,
} from '../../migrations/202202071116/lipscore-audit.table';

export const lipscoreAuditModelAttributes: ModelAttributes = {
  ...baseModelAttributes,
};

export class LipscoreAuditModel extends ResourceModel {
  public targetEmail!: string;
  public brokerId!: string;
  public estateVitecId!: string;
  public data!: Record<string, unknown>;
  public response!: Record<string, unknown>;
  public isSuccessful!: boolean;
}
export const lipscoreAuditModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  LipscoreAuditModel.init(lipscoreAuditModelAttributes, {
    sequelize,
    tableName: LIPSCORE_AUDIT_TABLE_NAME,
    timestamps: true,
  });
};
