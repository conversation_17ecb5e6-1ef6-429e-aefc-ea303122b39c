import type { LipscoreAudit } from './lipscore-audit';
import { LipscoreAuditModel, lipscoreAuditModelAttributes } from './lipscore-audit.model';

export type LipscoreAuditRepository = {
  create(audit: Omit<LipscoreAudit, 'createdAt' | 'id' | 'updatedAt'>): Promise<LipscoreAudit>;
  get(id: string): Promise<LipscoreAudit | null>;
};
export const lipscoreAuditTransformer = (lipscoreAuditModel: LipscoreAuditModel): LipscoreAudit => ({
  id: lipscoreAuditModel.id,
  createdAt: lipscoreAuditModel.createdAt,
  updatedAt: lipscoreAuditModel.updatedAt,
  targetEmail: lipscoreAuditModel.targetEmail,
  brokerId: lipscoreAuditModel.brokerId,
  estateVitecId: lipscoreAuditModel.estateVitecId,
  data: lipscoreAuditModel.data,
  response: lipscoreAuditModel.response,
  isSuccessful: lipscoreAuditModel.isSuccessful,
});
export const lipscoreAuditRepositoryFactory = (): LipscoreAuditRepository => ({
  create: async (audit) => {
    const newAudit = await LipscoreAuditModel.create({ ...audit });
    return lipscoreAuditTransformer(newAudit);
  },
  get: async (id) => {
    const audit = await LipscoreAuditModel.findOne({
      attributes: Object.keys(lipscoreAuditModelAttributes),
      where: {
        id,
      },
    });
    return audit ? lipscoreAuditTransformer(audit) : null;
  },
});
