import type { LipscoreAudit } from './lipscore-audit';
import type { LipscoreAuditRepository } from './lipscore-audit.repository';

type LipscoreAuditService = {
  createLipscoreAudit(audit: Omit<LipscoreAudit, 'createdAt' | 'id' | 'updatedAt'>): Promise<LipscoreAudit>;
  getLipscoreAudit(id: string): Promise<LipscoreAudit | null>;
};
type LipscoreAuditServiceFactory = (params: {
  lipscoreAuditRepository: LipscoreAuditRepository;
}) => LipscoreAuditService;
export const lipscoreAuditServiceFactory: LipscoreAuditServiceFactory = ({
  lipscoreAuditRepository: LipscoreAuditRepository,
}) => ({
  createLipscoreAudit: async (audit) => {
    const newAudit = await LipscoreAuditRepository.create(audit);
    return newAudit;
  },
  getLipscoreAudit: async (id) => {
    const audit = await LipscoreAuditRepository.get(id);
    return audit;
  },
});
