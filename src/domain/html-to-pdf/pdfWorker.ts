import { parentPort } from 'worker_threads';
import puppeteer from 'puppeteer';

async function generatePDF(html: string): Promise<Buffer> {
  const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
  const page = await browser.newPage();
  await page.setContent(html);
  const pdfBuffer = await page.pdf({ format: 'A4' });
  await browser.close();
  return pdfBuffer;
}

// eslint-disable-next-line @typescript-eslint/no-misused-promises
parentPort?.on('message', async (html: string) => {
  try {
    const buffer = await generatePDF(html);
    parentPort?.postMessage({ buffer });
  } catch (error) {
    parentPort?.postMessage({ error: (error as Error)?.message });
  }
});
