import type { PDFOptions } from 'puppeteer';
import puppeteer from 'puppeteer';
import type { Unleash } from 'unleash-client';
import { config, FeatureFlag } from '../../config';

type HtmlToPdf = (htmlString: string, unleash: Unleash, lambda: AWS.Lambda, options?: PDFOptions) => Promise<Buffer>;
export const htmlToPdf: HtmlToPdf = async (htmlString, unleash, lambda, options?) => {
  if (unleash.isEnabled(FeatureFlag.LAMBDA_PDF_GENERATION, {})) {
    const params = {
      FunctionName: config.lambda.pdfGenFunctionName,
      InvocationType: 'RequestResponse',
      Payload: JSON.stringify({ html: htmlString, options }),
    };

    try {
      const { Payload } = await lambda.invoke(params).promise();
      const resp = JSON.parse((Payload as unknown) as string) as { body: string };
      const buffer = Buffer.from(resp.body, 'base64');

      if (buffer.length < 100) {
        throw new Error('Empty buffer returned from lambda');
      }

      return buffer;
    } catch (error) {
      console.error('Error invoking lambda:', error);
      return generatePDF(htmlString);
    }
  } else {
    return generatePDF(htmlString);
  }
};

async function generatePDF(html: string): Promise<Buffer> {
  const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
  const page = await browser.newPage();
  await page.setContent(html);
  const pdfBuffer = await page.pdf({ format: 'A4' });
  await browser.close();
  return pdfBuffer;
}

// async function pdfGenerationWorker(html: string): Promise<Buffer> {
//   return new Promise((resolve, reject) => {
//     const worker = new Worker(join(__dirname, './pdfWorker.js'));
//     worker.on('message', (data: { buffer?: Buffer; error?: string }) => {
//       if (data.error) {
//         reject(data.error);
//       } else if (data.buffer) {
//         resolve(data.buffer);
//       }
//     });
//     worker.on('error', reject);
//     worker.on('exit', (code) => {
//       if (code !== 0) {
//         reject(new Error(`Worker stopped with exit code ${code}`));
//       }
//     });
//     worker.postMessage(html);
//   });
// }
