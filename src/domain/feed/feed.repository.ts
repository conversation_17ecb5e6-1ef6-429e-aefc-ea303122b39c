import type { Feed } from './feed';
import { FeedModel } from './feed.model';

export type FeedsRepository = {
  listFeed: (i: { userID: string; limit: number; offset: number }) => Promise<Feed[]>;
  deleteFeed: (userID: string, id: string) => Promise<Feed | null>;
};

export const feedTransformer = (feedModel: FeedModel): Feed => ({
  id: feedModel.id,
  createdAt: feedModel.createdAt,
  updatedAt: feedModel.updatedAt,
  userID: feedModel.userID,
  text: feedModel.text,
  deletedAt: feedModel.deletedAt,
  iconName: feedModel.iconName,
  redirectUrl: feedModel.redirectUrl,
});

export const feedRepositoryFactory = (): FeedsRepository => {
  const listFeed = async (input: { userID: string; limit: number; offset: number }): Promise<Feed[]> => {
    const feedModels = await FeedModel.findAll({
      where: {
        userID: input.userID,
        deletedAt: null,
      },
      order: [['createdAt', 'DESC']],
      limit: input.limit || 3,
      offset: input.offset || 0,
    });
    return feedModels.map(feedTransformer);
  };

  const deleteFeed = async (userID: string, id: string): Promise<Feed | null> => {
    const feedModel = await FeedModel.findOne({ where: { userID, id } });
    if (!feedModel) {
      return null;
    }
    await feedModel.update({ deletedAt: new Date() });

    return feedTransformer(feedModel);
  };

  return { listFeed, deleteFeed };
};
