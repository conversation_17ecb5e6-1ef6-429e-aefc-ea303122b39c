import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import { UserModel } from '../user/user.model';

export const FEEDS_TABLE_NAME = 'Feeds';

const feedAttributes: ModelAttributes = {
  ...baseModelAttributes,
  userID: {
    type: DataTypes.UUID,
    references: {
      model: 'Users',
      key: 'id',
    },
    allowNull: false,
  },
  text: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  iconName: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  deletedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  redirectUrl: {
    type: DataTypes.STRING(1024),
    allowNull: true,
  },
};

export class FeedModel extends ResourceModel {
  public userID!: string;
  public text!: string;
  public iconName: string;
  public deletedAt: Date;
  public redirectUrl: string;
}

export const feedModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  FeedModel.init(feedAttributes, { sequelize, tableName: FEEDS_TABLE_NAME, timestamps: true });
};

export const setFeedModelReferences = (): void => {
  FeedModel.belongsTo(UserModel, { foreignKey: 'id', as: 'user', onUpdate: 'CASCADE', onDelete: 'CASCADE' });
};
