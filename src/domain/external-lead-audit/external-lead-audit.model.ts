import type { Sequelize } from 'sequelize';
import { ResourceModel } from '../../framework/sequelize/resource';
import {
  externalLeadAuditTableAttributes,
  EXTERNAL_LEAD_AUDIT_TABLE_NAME,
} from '../../migrations/20220920120000/external-lead-audit.table';
import type { ServiceOfferViaApiType, ServiceOfferViaEmailType } from '../offers/service-offer';

export type ExternalLeadAuditData = {
  request: Record<string, any>;
  response: Record<string, any> | null;
  notes: string | Record<string, any> | null;
};

export class ExternalLeadAuditModel extends ResourceModel {
  public leadType: ServiceOfferViaEmailType | ServiceOfferViaApiType | null;
  public isSuccessful: boolean | null;
  public externalLeadId: string | null;
  public name: string | null;
  public email: string | null;
  public phone: string | null;
  public address: string | null;
  public postalCode: string | null;
  public brokerId: string | null;
  public departmentOfBroker: string | null;
  public data: ExternalLeadAuditData;
}

export const externalLeadAuditModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  ExternalLeadAuditModel.init(externalLeadAuditTableAttributes, {
    sequelize,
    tableName: EXTERNAL_LEAD_AUDIT_TABLE_NAME,
    timestamps: true,
  });
};
