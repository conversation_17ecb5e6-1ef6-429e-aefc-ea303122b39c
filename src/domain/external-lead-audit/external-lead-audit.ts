import type {
  AllServiceOfferTypes,
  NotUnifiedServiceOfferViaApiType,
  NotUnifiedServiceOfferViaEmailType,
  ServiceOfferViaApiType,
  ServiceOfferViaEmailType,
} from '../offers/service-offer';
import type { ExternalLeadAuditData } from './external-lead-audit.model';

export type ExternalLeadAudit = {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  leadType:
    | ServiceOfferViaEmailType
    | NotUnifiedServiceOfferViaEmailType
    | ServiceOfferViaApiType
    | NotUnifiedServiceOfferViaApiType;
  isSuccessful: boolean | null;
  externalLeadId: string | null; // When we can't send our unique, human readable id to any of the lead providers, we store their id in their response to this field to make 1-1 connection
  name: string | null;
  email: string | null;
  phone: string | null;
  address: string | null;
  postalCode: string | null;
  brokerId: string | null;
  departmentOfBroker: string | null;
  data: ExternalLeadAuditData;
};

export function generateExternalLeadHumanReadableId({
  postgresId,
  leadType,
  createdAt,
}: {
  postgresId: string;
  leadType: AllServiceOfferTypes;
  createdAt: Date;
}): string {
  const year = createdAt.getFullYear();
  return `${year}-${leadType}-${postgresId}`;
}
