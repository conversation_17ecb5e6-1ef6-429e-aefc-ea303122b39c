import { endOfDay, startOfDay } from 'date-fns';
import { isNil } from 'lodash';
import type { WhereOptions } from 'sequelize';
import { Op } from 'sequelize';
import type { AllServiceOfferTypes } from '../offers/service-offer';
import type { ExternalLeadAudit } from './external-lead-audit';
import { ExternalLeadAuditModel } from './external-lead-audit.model';

type ExternalLeadAuditUpdate = Partial<Omit<ExternalLeadAudit, 'id' | 'createdAt' | 'updatedAt'>>;
type ExternalLeadAuditListQuery = { fromDate: Date; toDate: Date; leadType?: AllServiceOfferTypes };

export type ExternalLeadAuditRepository = {
  create(data: ExternalLeadAuditUpdate): Promise<{ id: string; createdAt: Date }>;
  update(id: string, data: ExternalLeadAuditUpdate): Promise<void>;
  getInTimeRange(opts: ExternalLeadAuditListQuery): Promise<ExternalLeadAudit[]>;
  getSuccessfulLeadBySettlementSellerOrBuyerId(opts: { id: string; createdAt: Date }): Promise<ExternalLeadAudit[]>;
};

export const ExternalLeadAuditRepositoryFactory = (): ExternalLeadAuditRepository => {
  const create = async (data: ExternalLeadAuditUpdate): Promise<{ id: string; createdAt: Date }> => {
    const newEntry = await ExternalLeadAuditModel.create(data);
    return { id: newEntry.id, createdAt: newEntry.createdAt };
  };
  const update = async (id: string, data: ExternalLeadAuditUpdate): Promise<void> => {
    await ExternalLeadAuditModel.update(data, { where: { id } });
  };
  const getInTimeRange = async ({
    fromDate,
    toDate,
    leadType,
  }: ExternalLeadAuditListQuery): Promise<ExternalLeadAudit[]> => {
    const fromDateStart = startOfDay(fromDate);
    const toDateEnd = endOfDay(toDate);
    let whereOptions: WhereOptions;
    const baseOptions: WhereOptions = {
      createdAt: {
        [Op.gte]: fromDateStart,
        [Op.lte]: toDateEnd,
      },
    };
    if (!isNil(leadType)) {
      whereOptions = { ...baseOptions, leadType };
    } else {
      whereOptions = { ...baseOptions };
    }
    const res: ExternalLeadAudit[] = (
      await ExternalLeadAuditModel.findAll({
        where: whereOptions,
      })
    ).map((r) => r.toJSON());
    return res;
  };

  const getSuccessfulLeadBySettlementSellerOrBuyerId: ExternalLeadAuditRepository['getSuccessfulLeadBySettlementSellerOrBuyerId'] = async ({
    id,
    createdAt,
  }) => {
    const res: ExternalLeadAudit[] = (
      await ExternalLeadAuditModel.findAll({
        where: {
          createdAt: {
            [Op.gte]: createdAt,
          },
          isSuccessful: true,
          data: {
            [Op.contains]: {
              notes: {
                settlementBuyerOrSellerId: id,
              },
            },
          },
        },
      })
    ).map((r) => r.toJSON());
    return res;
  };

  return {
    create,
    update,
    getInTimeRange,
    getSuccessfulLeadBySettlementSellerOrBuyerId,
  };
};
