import { SecretsManager } from 'aws-sdk';
import type { AwsConfig } from '../../config';

export type SecretsManagerService = {
  getSecretByKey: (key: string) => Promise<string | null>;
};

export const secretsManagerServiceFactory = ({ config }: { config: AwsConfig }): SecretsManagerService => {
  const secretsManager = new SecretsManager({
    region: config.region,
    accessKeyId: config.accessKeyId,
    secretAccessKey: config.secretAccessKey,
  });

  const getSecret = async (key: string): Promise<string | null> => {
    const response = await secretsManager
      .getSecretValue({
        SecretId: config.secretsManager.arn,
      })
      .promise();

    const secret = JSON.parse(response.SecretString || '{}') as Record<string, string>;

    return key in secret ? secret[key] : null;
  };

  return {
    getSecretByKey: getSecret,
  };
};
