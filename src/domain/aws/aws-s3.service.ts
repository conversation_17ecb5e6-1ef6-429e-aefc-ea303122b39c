import type { S3 } from 'aws-sdk';
import urljoin from 'url-join';
import { v4 } from 'uuid';
import { logger } from '../../logger';

export type S3UploadInput = {
  key: string;
  body: Buffer;
  bucket: string;
  acl: string;
};

export type S3GetAndDeleteInput = {
  key: string;
  bucket: string;
};

export enum S3ACLType {
  PUBLIC_READ = 'public-read',
}

export const s3ImageKeyFactory = (folder: string, ext: string, fileName?: string): string => {
  const randomFileName = `${v4()}.${ext}`;
  return urljoin(folder, fileName || randomFileName);
};

export type AWSS3Service = {
  getFile: (i: S3GetAndDeleteInput) => Promise<Buffer>;
  uploadFile: (i: S3UploadInput) => Promise<string>;
  deleteFile: (i: S3GetAndDeleteInput) => Promise<void>;
};

export const awsS3ServiceFactory = ({ s3 }: { s3: S3 }): AWSS3Service => {
  const getFile = async (i: S3GetAndDeleteInput): Promise<Buffer> => {
    const { Body } = await s3
      .getObject({
        Bucket: i.bucket,
        Key: i.key,
      })
      .promise();
    return Body as Buffer;
  };
  const uploadFile = async (i: S3UploadInput): Promise<string> => {
    const { Location } = await s3
      .upload({
        Key: i.key,
        Body: i.body,
        Bucket: i.bucket,
      })
      .promise();
    return Location;
  };
  const deleteFile = async (i: S3GetAndDeleteInput): Promise<void> => {
    try {
      await s3
        .deleteObject({
          Bucket: i.bucket,
          Key: i.key,
        })
        .promise();
    } catch (error) {
      logger.error(`Failed to delete file ${i.key} from ${i.bucket}.`);
    }
  };
  return { uploadFile, deleteFile, getFile };
};
