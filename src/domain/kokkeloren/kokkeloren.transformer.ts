import type { EstateAddress } from '../estate/mongo/estate.mongoose-types';
import type { User } from '../user/user';

export type KokkelorenName = {
  firstName: string;
  lastName: string;
};

export function kokkelorenNameTransformer(name: User['name']): KokkelorenName {
  const match = /^([^ ]+) (.+)$/.exec(name);

  if (match) {
    return {
      firstName: match[1],
      lastName: match[2],
    };
  } else {
    return {
      firstName: name || 'no name provided',
      lastName: 'no name provided',
    };
  }
}

export function getFormattedAddressString(address: EstateAddress | undefined): string {
  if (address) {
    const streetAddressWithApartmentNumber = `${address.streetAdress} ${address.apartmentNumber}`.trimEnd();
    return `${streetAddressWithApartmentNumber} ${address.zipCode} ${address.city}`;
  } else {
    return '';
  }
}
