/* eslint-disable @typescript-eslint/naming-convention */
import axios from 'axios';
import { format } from 'date-fns';
import type { KokkelorenConfig } from '../../config';
import { logger } from '../../logger';
import { LightAxiosError } from '../../utils/axios-logger.utils';
import type { EstateMongooseRepository, EstateMongooseResponse } from '../estate/mongo/estate.mogoose-repository';
import type { EstateAddress } from '../estate/mongo/estate.mongoose-types';
import { generateExternalLeadHumanReadableId } from '../external-lead-audit/external-lead-audit';
import type { ExternalLeadAuditRepository } from '../external-lead-audit/external-lead-audit.repository';
import type { LeadsService } from '../leads/leads.service';
import { ServiceOfferViaApiType } from '../offers/service-offer';
import type { SettlementBuyerRepository } from '../settlement-buyer/settlement-buyer.repository';
import { UserActivityType } from '../user-activity/user-activity';
import type { UserActivityRepository } from '../user-activity/user-activity.repository';
import { getFormattedAddressString, kokkelorenNameTransformer } from './kokkeloren.transformer';

export type KokkelorenLeadResponse = {
  id: string;
  properties: {
    address: string;
    createdate: string;
    firstname: string;
    hs_all_contact_vids: string;
    hs_calculated_mobile_number: string;
    hs_currently_enrolled_in_prospecting_agent: string;
    hs_is_contact: string;
    hs_is_unworked: string;
    hs_lifecyclestage_lead_date: string;
    hs_marketable_status: string;
    hs_marketable_until_renewal: string;
    hs_membership_has_accessed_private_content: string;
    hs_object_id: string;
    hs_object_source: string;
    hs_object_source_id: string;
    hs_object_source_label: string;
    hs_pipeline: string;
    hs_registered_member: string;
    hs_searchable_calculated_mobile_number: string;
    lastmodifieddate: string;
    lastname: string;
    lifecyclestage: string;
    mobilephone: string;
    nordvik_office: string;
    nordvik_test_or_prod: string;
    nordvik_wants_discount: string;
    handover_date: string;
    zip: string;
  };
  createdAt: string;
  updatedAt: string;
  archived: boolean;
};

type SendLeadOptsBase = {
  participant: { id: string; email: string | null; name: string | null; phoneNumber: string | null };
  address?: EstateAddress;
};

type SendLeadOpts = SendLeadOptsBase & {
  userOrParticipantID: string | null;
  settlementBuyerOrSellerId: string | null;
  pepFormId: string | null;
};
type SendLeadForParticipantOpts = SendLeadOptsBase & {
  participant: { id: string; email: string | null; name: string | null; phoneNumber: string | null };
  settlementBuyerOrSellerId: string | null;
  pepFormId: string | null;
};
type SendLeadForUserOpts = SendLeadOptsBase & { userId: string | null };

export type KokkelorenService = {
  sendLeadForUser: (opts: SendLeadForUserOpts) => Promise<void>;
  sendLeadForParticipant: (opts: SendLeadForParticipantOpts) => Promise<void>;
  isKokkelorenAvailable: (estateId: string) => Promise<boolean>;
};

export const kokkelorenServiceFactory = ({
  config,
  userActivityRepository,
  externalLeadAuditRepository,
  estateRepository,
  settlementBuyerRepository,
  leadService,
}: {
  config: KokkelorenConfig;
  estateRepository: EstateMongooseRepository;
  settlementBuyerRepository: SettlementBuyerRepository;
  userActivityRepository: UserActivityRepository;
  externalLeadAuditRepository: ExternalLeadAuditRepository;
  leadService: LeadsService;
}): KokkelorenService => {
  const isKokkelorenAvailable = async (estateId: string): Promise<boolean> => {
    const estate = await estateRepository.findEstateByVitecEstateId(estateId);
    // https://linear.app/nordvik/issue/NOR-1181/integrate-value-proposition-from-kokkeloren-into-the-settlement-buyer
    return estate?.departmentId !== undefined && [43, 13].includes(estate.departmentId);
  };

  const sendLead = async ({
    participant: { id: userOrParticipantID, email, name, phoneNumber: phone },
    address,
    settlementBuyerOrSellerId,
    pepFormId,
  }: SendLeadOpts): Promise<void> => {
    const {
      employeeId: brokerEmployeeId,
      departmentName: departmentOfEstate,
    } = await leadService.getBrokerDetailsFromUserPhoneOrEmail({ phone, email });
    const { id: postgresId, createdAt } = await externalLeadAuditRepository.create({
      email,
      name,
      phone,
      address: getFormattedAddressString(address),
      postalCode: address?.zipCode || null,
      brokerId: brokerEmployeeId,
      departmentOfBroker: departmentOfEstate,
      externalLeadId: null,
      leadType: ServiceOfferViaApiType.KOKKELOREN,
    });
    const externalLeadHumanReadableId = generateExternalLeadHumanReadableId({
      postgresId,
      createdAt,
      leadType: ServiceOfferViaApiType.KOKKELOREN,
    });

    const [form] = await settlementBuyerRepository.getWithCustomQueryWithoutConnections({
      where: { id: settlementBuyerOrSellerId },
    });

    if (!form) {
      throw new Error('Settlement form not found');
    }

    const estate = await estateRepository.findEstateByVitecEstateId(form.estateVitecId);

    if (!estate) {
      throw new Error('Estate not found');
    }

    // split name into first and last name
    const { firstName: firstname, lastName: lastname } = kokkelorenNameTransformer(name || '');

    const headers = {
      Authorization: `Bearer ${config.accessToken}`,
      'Content-Type': 'application/json',
    };

    const body = {
      properties: {
        nordvik_wants_discount: 'true',
        nordvik_office: 'Nordvik Frogner',
        nordvik_test_or_prod: config.isProduction ? 'prod' : 'test',
        firstname,
        lastname,
        mobilephone: phone,
        lifecyclestage: 'lead',
        address: estate.address.streetAdress,
        zip: estate.address.zipCode,
        handover_date: estate.takeOverDate ? format(estate.takeOverDate, 'yyyy-MM-dd') : null,
      },
    };

    try {
      const response = await axios.post<KokkelorenLeadResponse>(config.sendLeadURL, body, { headers });

      await externalLeadAuditRepository.update(postgresId, {
        externalLeadId: externalLeadHumanReadableId,
        isSuccessful: true,
        data: {
          request: { method: 'POST', url: config.sendLeadURL, headers, body },
          response: {
            statusCode: response.status,
            body: response.data,
          },
          notes: { userOrParticipantID, settlementBuyerOrSellerId, pepFormId },
        },
      });
    } catch (error) {
      if (error instanceof LightAxiosError) {
        await externalLeadAuditRepository.update(postgresId, {
          isSuccessful: false,
          externalLeadId: externalLeadHumanReadableId,
          data: {
            request: { method: error.request.method, url: error.request.url, headers, body },
            response: {
              statusCode: error.response?.status || 500,
              body: (error.response?.data as string) || '',
            },
            notes: { userOrParticipantID, settlementBuyerOrSellerId, pepFormId },
          },
        });
        logger.error(error, 'Error while calling Kokkeloren API');
      } else {
        logger.error(error as Error, 'Error while saving Kokkeloren Audit to External Lead Audit Repo');
      }

      throw error;
    }
  };

  const sendLeadForUser = async ({ participant, address }: SendLeadForUserOpts): Promise<void> => {
    try {
      await userActivityRepository.create(participant.id, UserActivityType.REQUEST_KOKKELOREN_SERVICE, {});
    } catch (error) {
      logger.error(error as Error, 'Error while saving Kokkeloren User Activity');
    }

    await sendLead({
      participant,
      userOrParticipantID: participant.id,
      address: address,
      settlementBuyerOrSellerId: null,
      pepFormId: null,
    });
  };

  const sendLeadForParticipant = async ({
    participant,
    address,
    settlementBuyerOrSellerId,
    pepFormId,
  }: SendLeadForParticipantOpts): Promise<void> => {
    await sendLead({
      participant,
      userOrParticipantID: participant.id,
      address,
      settlementBuyerOrSellerId,
      pepFormId,
    });
  };

  return {
    sendLeadForUser,
    sendLeadForParticipant,
    isKokkelorenAvailable,
  };
};

export const isKokkelorenteAvailableForEstate = (estate: EstateMongooseResponse): boolean => {
  return [13, 1].includes(estate.departmentId);
};
