import type { OpenAPIV3 } from 'openapi-types';

export const googleAnalyticsResponse: OpenAPIV3.SchemaObject = {
  type: 'object',
  nullable: true,
  required: [
    'totalNumberOfPageViews',
    'dailyPageViews',
    'numberOfSalesProspectDownloads',
    'numberOfDirectPageViews',
    'numberOfPaidFinnNoPageViews',
    'numberOfFinnNoPageViews',
    'numberOfFacebookPageViews',
    'numberOfGooglePageViews',
  ],
  properties: {
    totalNumberOfPageViews: { type: 'number' },
    dailyPageViews: {
      type: 'array',
      items: {
        type: 'number',
      },
    },
    numberOfSalesProspectDownloads: { type: 'number' },
    numberOfDirectPageViews: { type: 'number' },
    numberOfPaidFinnNoPageViews: { type: 'number' },
    numberOfFinnNoPageViews: { type: 'number' },
    numberOfFacebookPageViews: { type: 'number' },
    numberOfGooglePageViews: { type: 'number' },
  },
};
