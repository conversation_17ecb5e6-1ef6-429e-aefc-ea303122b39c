import type { OpenAPIV3 } from 'openapi-types';
import { ServiceOfferViaApiType } from '../../offers/service-offer';
import { SettlementLeadViaEmailProvider } from '../../settlement/settlement-leads';
import { enumToJsonDictionary } from '../enum-to-json-dictionary';

export const settlementLeadsSchema: OpenAPIV3.NonArraySchemaObject = {
  ...enumToJsonDictionary({
    enumObject: { ...ServiceOfferViaApiType, ...SettlementLeadViaEmailProvider },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: {
            type: 'string',
            format: 'uuid',
          },
        },
        additionalProperties: true,
      },
      nullable: true,
    },
  }),
  nullable: true,
};
