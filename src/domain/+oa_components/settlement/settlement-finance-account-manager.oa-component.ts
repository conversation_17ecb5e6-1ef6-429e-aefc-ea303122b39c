import type { OpenAPIV3 } from 'openapi-types';

export const settlementFinanceAccountManagerSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['name', 'ssn', 'address'],
  properties: {
    name: { type: 'string', nullable: false },
    ssn: { type: 'string', nullable: false },
    address: { type: 'string', nullable: false },
    relationToAccountManager: { type: 'string', nullable: false },
  },
};
