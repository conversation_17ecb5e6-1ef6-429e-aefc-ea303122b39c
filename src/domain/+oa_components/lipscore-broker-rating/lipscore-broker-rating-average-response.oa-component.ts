import type { OpenAPIV3 } from 'openapi-types';

export const lipscoreBrokerRatingReviewSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['createdAt', 'lang', 'rating', 'reviewerShortName', 'text', 'id'],
  properties: {
    id: { type: 'number' },
    createdAt: { type: 'string' },
    lang: { type: 'string' },
    rating: { type: 'number' },
    reviewerShortName: { type: 'string' },
    text: { type: 'string' },
  },
};

export const lipscoreBrokerRatingAverageResponseSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['averageRating', 'ratingCount'],
  properties: {
    averageRating: { type: 'number' },
    ratingCount: { type: 'number' },
    whitelistedReviews: { type: 'array', items: lipscoreBrokerRatingReviewSchema, nullable: true },
    whitelistedReviewCount: { type: 'number', nullable: true },
  },
};
