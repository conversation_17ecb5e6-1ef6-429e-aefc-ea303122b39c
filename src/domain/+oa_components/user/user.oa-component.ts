import type { OpenAPIV3 } from 'openapi-types';
import { userOptionsOAComponent } from './user-options.oa-component';

export const userOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['id', 'createdAt', 'updatedAt', 'email'],
  properties: {
    id: { type: 'string', format: 'uuid' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' },
    email: { type: 'string' },
    name: { type: 'string' },
    phoneNumber: { type: 'string' },
    areas: { type: 'array', items: { type: 'string' } },
    options: userOptionsOAComponent,
    referralCode: { type: 'string' },
    registeredWith: { type: 'string' },
    visitedChecklist: { type: 'boolean' },
    visitedSalesProcess: { type: 'boolean' },
    closedTutorialAt: { type: 'string' },
    pushNotificationSettings: {
      type: 'object',
      additionalProperties: true,
    },
    consentSettings: {
      type: 'object',
      additionalProperties: true,
    },
    isEmployee: { type: 'boolean' },
  },
};

export const userUpdatesOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    name: { type: 'string' },
    phoneNumber: { type: 'string' },
    areas: { type: 'array', items: { type: 'string' } },
    options: userOptionsOAComponent,
    visitedChecklist: { type: 'boolean' },
    visitedSalesProcess: { type: 'boolean' },
    closedTutorialAt: { type: 'string' },
    pushNotificationSettings: {
      type: 'object',
      additionalProperties: true,
    },
    consentSettings: {
      type: 'object',
      additionalProperties: true,
    },
  },
};
