import type { OpenAPIV3 } from 'openapi-types';
import {
  ContactHoursOption,
  ContactPreferences,
  DomesticType,
  HouseCondition,
  MortgageOption,
} from '../../user-options/user-options';
import { propertyTypeOAComponent } from '../estate/estate.oa-component';

export const rangeOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  required: ['from', 'to'],
  additionalProperties: false,
  nullable: true,
  properties: {
    from: { type: 'number', nullable: true },
    to: { type: 'number', nullable: true },
  },
};

export const profileOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  nullable: true,
  properties: {
    domesticType: {
      type: 'string',
      enum: [
        null,
        DomesticType.FAMILY,
        DomesticType.COUPLE,
        DomesticType.GROUP,
        DomesticType.NO_ANSWER,
        DomesticType.SINGLE,
      ],
      nullable: true,
    },
    houseCondition: {
      type: 'array',
      items: { type: 'string', enum: [null, HouseCondition.NEW, HouseCondition.USED], nullable: true },
    },
    numberOfPeople: { type: 'number', nullable: true },
    estateTypePreferences: { type: 'array', items: { type: 'string' } },
    type: propertyTypeOAComponent,
    rooms: rangeOAComponent,
  },
};

export const userOptionsOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  nullable: true,
  properties: {
    type: {
      type: 'object',
      nullable: true,
      additionalProperties: false,
      properties: {
        seller: { type: 'boolean' },
        buyer: { type: 'boolean' },
      },
    },
    livingArea: { type: 'number', nullable: true },
    area: rangeOAComponent,
    price: rangeOAComponent,
    profile: profileOAComponent,
    values: { type: 'array', items: { type: 'string' } },
    mortgage: { type: 'string', enum: [MortgageOption.YES, MortgageOption.NO, MortgageOption.PRE_APPROVED] },
    contactPreferences: {
      type: 'array',
      items: {
        type: 'string',
        enum: [
          ContactPreferences.CHAT,
          ContactPreferences.EMAIL,
          ContactPreferences.PHONE,
          ContactPreferences.TEXT_MESSAGES,
          ContactPreferences.DO_NOT_DISTURB,
        ],
      },
    },
    contactHours: {
      type: 'array',
      items: {
        type: 'string',
        enum: [
          null,
          ContactHoursOption.WEEKDAY_WORK_HOURS,
          ContactHoursOption.WEEKDAY_EVENING,
          ContactHoursOption.WEEKEND_DAYTIME,
          ContactHoursOption.WEEKEND_EVENINGS,
        ],
        nullable: true,
      },
    },
  },
};
