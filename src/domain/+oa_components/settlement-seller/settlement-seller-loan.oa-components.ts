import type { OpenAPIV3 } from 'openapi-types';

export const putSettlementSellerLoanSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    loanBank: { type: 'string', nullable: true },
    loanTaker: { type: 'string', nullable: true },
    residualDebt: { type: 'string', nullable: true },
    bankContactName: { type: 'string', nullable: true },
    bankContactPhone: { type: 'string', nullable: true },
    bankContactEmail: { type: 'string', nullable: true },
    shouldMortgageLoanBeRepaid: { type: 'boolean', nullable: true },
    loanIdNumber: { type: 'string', nullable: true },
  },
};

export const settlementSellerLoansSchema: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  items: {
    type: 'object',
    properties: {
      id: { type: 'string', nullable: false },
      ...putSettlementSellerLoanSchema.properties,
    },
  },
};

export const putSettlementSellerLoansSchema: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  items: putSettlementSellerLoanSchema,
};
