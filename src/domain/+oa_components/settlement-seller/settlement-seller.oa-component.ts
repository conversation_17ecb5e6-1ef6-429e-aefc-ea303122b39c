import type { OpenAPIV3 } from 'openapi-types';
import {
  getPEPFormParticipantsResponseSchema,
  patchPEPFormParticipantSchema,
} from '../pep/pep-participants.oa-component';
import { resourceSchema } from '../resource/resource.oa-component';
import { settlementLeadsSchema } from '../settlement/settlement-leads.oa-component';
import { settlementSellerAccountsSchema } from './settlement-seller-account.oa-components';
import { settlementSellerLoansSchema } from './settlement-seller-loan.oa-components';
import { settlementSellerParticipantSchema } from './settlement-seller-participant.oa-component';

export const patchSettlementSellerSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    isMortgaged: { type: 'boolean', nullable: true },
    accountsComment: { type: 'string', nullable: true },
    leads: settlementLeadsSchema,
    signingStarted: {
      type: 'string',
      nullable: true,
    },
    idfyDocumentId: {
      type: 'string',
      nullable: true,
    },
    accountPepParticipants: { type: 'array', items: patchPEPFormParticipantSchema, nullable: true },
  },
};

export const settlementSellerSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    ...resourceSchema.properties,
    ...patchSettlementSellerSchema.properties,
    estateVitecId: { type: 'string', nullable: false },
    id: { type: 'string', nullable: false },
    loans: settlementSellerLoansSchema,
    participants: { type: 'array', items: settlementSellerParticipantSchema },
    accounts: settlementSellerAccountsSchema,
    estateAssignmentNumber: { type: 'string' },
    estateAddress: { type: 'string' },
    isOsloTransportEnabledForEstate: { type: 'boolean' },
    isVerketInteriorEnabledForEstate: { type: 'boolean' },
    accountPepParticipants: getPEPFormParticipantsResponseSchema,
    sellers: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          contactId: { type: 'string' },
          firstName: { type: 'string' },
          lastName: { type: 'string' },
          email: { type: 'string' },
          mobilePhone: { type: 'string' },
          companyName: { type: 'string' },
        },
      },
    },
  },
};
