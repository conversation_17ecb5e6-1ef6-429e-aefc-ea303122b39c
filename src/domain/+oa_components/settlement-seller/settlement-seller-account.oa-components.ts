import type { OpenAPIV3 } from 'openapi-types';
import { settlementFinanceAccountManagerSchema } from '../settlement/settlement-finance-account-manager.oa-component';

export const putSettlementSellerAccountSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    hasFinanceAccountManagers: { type: 'boolean', nullable: true },
    accountNumber: { type: 'string', nullable: true },
    accountOwnerName: { type: 'string', nullable: true },
    distributionInPercentage: { type: 'number', nullable: true },
    isAccountShared: { type: 'string', nullable: true },
    financeAccountManagers: { type: 'array', items: settlementFinanceAccountManagerSchema, nullable: true },
  },
};

export const settlementSellerAccountsSchema: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  items: {
    type: 'object',
    properties: {
      id: { type: 'string', nullable: false },
      ...putSettlementSellerAccountSchema.properties,
    },
  },
};

export const putSettlementSellerAccountsSchema: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  items: putSettlementSellerAccountSchema,
};
