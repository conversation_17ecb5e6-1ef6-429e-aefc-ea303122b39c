import type { OpenAPIV3 } from 'openapi-types';

export const patchSettlementSellerParticipantSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    name: { type: 'string', nullable: true },
    email: { type: 'string', nullable: true },
    phoneNumber: { type: 'string', nullable: true },
    image: { type: 'string', nullable: true },
  },
};

export const settlementSellerParticipantSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    id: { type: 'string', nullable: false },
    ...patchSettlementSellerParticipantSchema.properties,
  },
};
