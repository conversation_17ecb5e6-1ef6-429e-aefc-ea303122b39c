import type { OpenAPIV3 } from 'openapi-types';
import {
  ServiceOfferViaEmailType,
  NotUnifiedServiceOfferViaEmailType,
  ServiceOfferViaApiType,
  NotUnifiedServiceOfferViaApiType,
} from '../../offers/service-offer';

export const externalLeadAuditSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    id: { type: 'number', nullable: false }, // auto increment number
    createdAt: { type: 'string', nullable: false },
    updatedAt: { type: 'string', nullable: false },
    leadType: {
      // as for now they are HMH_MOVING, HMH_CLEANING, IF_INSURANCE, TRYGG, SSG, BYGGSTART, EXPONOVA, STOREBRAND, NORGES_ENERGY, FORTUM, FJORDKRAFT, SECTOR_ALARM
      type: 'string',
      enum: [
        ...Object.values(ServiceOfferViaEmailType),
        ...Object.values(NotUnifiedServiceOfferViaEmailType),
        ...Object.values(ServiceOfferViaApiType),
        ...Object.values(NotUnifiedServiceOfferViaApiType),
      ],
      nullable: false,
    },
    isSuccessful: { type: 'boolean' },
    nordvikInternalLeadId: { type: 'string', nullable: false }, // computed from createdAt year, leadType and id 2022-FORTUM-239
    externalLeadId: { type: 'string' },
    name: { type: 'string' },
    email: { type: 'string' },
    phone: { type: 'string' },
    address: { type: 'string' },
    postalCode: { type: 'string' },
    brokerId: { type: 'string' },
    departmentOfBroker: { type: 'string' },
    data: {
      type: 'object',
      nullable: true,
      properties: {
        notes: { type: 'object', additionalProperties: true, nullable: true },
      },
    },
  },
};
