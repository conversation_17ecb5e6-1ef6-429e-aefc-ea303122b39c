import type { OpenAPIV3 } from 'openapi-types';
import { OtpMeterType } from '../../otp/otp-meter/otp-meter';
import { fileCreateSchema, fileDetailsSchema, filePatchSchema } from '../file/file.oa-component';
import { resourceSchema } from '../resource/resource.oa-component';

export const otpMeterCreateSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    meterNumber: { type: 'string' },
    meterReading: { type: 'string' },
    type: { type: 'string', enum: Object.values(OtpMeterType) },
    image: fileCreateSchema,
    meterName: { type: 'string' },
  },
};

export const otpMeterPatchSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    meterNumber: { type: 'string' },
    meterReading: { type: 'string' },
    type: { type: 'string', enum: Object.values(OtpMeterType) },
    image: filePatchSchema,
    meterName: { type: 'string' },
  },
};

export const otpMeterSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    ...resourceSchema.properties,
    ...otpMeterPatchSchema.properties,
    image: fileDetailsSchema,
  },
};
