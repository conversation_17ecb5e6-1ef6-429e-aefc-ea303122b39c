import type { OpenAPIV3 } from 'openapi-types';
import { ElectricityProvider } from '../../otp/otp';
import { otpMeterSchema } from './otp-meter.oa-component';
import { otpParticipantSchema } from './otp-participant.oa-component';

export const patchOtpSchemaProperties: OpenAPIV3.NonArraySchemaObject['properties'] = {
  idfyDocumentId: { type: 'string', nullable: true },
  signingStarted: { type: 'string', nullable: true },

  moneyTransferred: { type: 'boolean', nullable: true },
  sellerNewAddress: { type: 'string', nullable: true },
  sellerNewPostcode: { type: 'string', nullable: true },
  sellerNewCity: { type: 'string', nullable: true },
  propertyCleaned: { type: 'boolean', nullable: true },
  propertyCleanedComment: { type: 'string', nullable: true },
  sellerPaidCosts: { type: 'boolean', nullable: true },
  sellerPaidCostsComment: { type: 'string', nullable: true },
  handedOverAllKeys: { type: 'boolean', nullable: true },
  handedOverAllKeysComment: { type: 'string', nullable: true },
  numberOfKeys: { type: 'string', enum: [null, '1', '2', '3', '4', '5+'], nullable: true },
  smokeAlarmAvailable: { type: 'boolean', nullable: true },
  fireExtinguisherAvailable: { type: 'boolean', nullable: true },
  fireSafetyComment: { type: 'string', nullable: true },
  waterInfoProvided: { type: 'boolean', nullable: true },
  electricityInfoProvided: { type: 'boolean', nullable: true },
  finalSettlement: { type: 'boolean', nullable: true },
  finalSettlementComment: { type: 'string', nullable: true },
  electricityProviderSelected: { type: 'string', enum: [null, ...Object.values(ElectricityProvider)], nullable: true },
  handoverComment: { type: 'string', nullable: true },
  address: { type: 'string', nullable: true },
  postCode: { type: 'string', nullable: true },
  city: { type: 'string', nullable: true },
  billingBuyerContactId: { type: 'string', nullable: true },
  finalSettlementWithholding: { type: 'boolean', nullable: true },
  finalSettlementWithholdAmount: { type: 'string', nullable: true },
  finalSettlementWithholdReason: { type: 'string', nullable: true },
  finalSettlementWithholdingComment: { type: 'string', nullable: true },
};

export const otpOASchemaProperties: OpenAPIV3.NonArraySchemaObject['properties'] = {
  ...patchOtpSchemaProperties,
  id: { type: 'string', nullable: false },
  estateVitecId: { type: 'string', nullable: false },
  waterMeters: { type: 'array', items: otpMeterSchema },
  electricityMeters: { type: 'array', items: otpMeterSchema },
  participants: { type: 'array', items: otpParticipantSchema },
  estateBaseType: { type: 'number', nullable: false },
};
