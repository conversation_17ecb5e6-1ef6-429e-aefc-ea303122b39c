import type { OpenAPIV3 } from 'openapi-types';
import { OtpParticipantRole } from '../../otp/otp-participant/otp-participant';
import { fileCreateSchema, fileDetailsSchema, filePatchSchema } from '../file/file.oa-component';
import { resourceSchema } from '../resource/resource.oa-component';

export const otpParticipantCreateSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    name: { type: 'string', nullable: false },
    email: { type: 'string', nullable: false },
    phoneNumber: { type: 'string', nullable: false },
    isPowerOfAttorney: { type: 'boolean', nullable: false },
    belongsTo: { type: 'string', enum: Object.values(OtpParticipantRole), nullable: false },
    image: fileCreateSchema,
  },
};

export const otpParticipantPatchSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    name: { type: 'string' },
    email: { type: 'string' },
    phoneNumber: { type: 'string' },
    isPowerOfAttorney: { type: 'boolean' },
    belongsTo: { type: 'string', enum: Object.values(OtpParticipantRole) },
    image: filePatchSchema,
  },
};

export const otpParticipantSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    ...resourceSchema.properties,
    ...otpParticipantPatchSchema.properties,
    image: fileDetailsSchema,
  },
};
