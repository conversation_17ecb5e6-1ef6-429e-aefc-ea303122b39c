import type { OpenAPIV3 } from 'openapi-types';
import { EstateMongooseAssignmentTypeGroups } from '../../estate/mongo/estate.mongoose-types';
import { PepFormValuationPurpose } from '../../pep-form/pep-form';
import { PEPType } from '../../politically-exposed-person-form/politically-exposed-person-form';
import { resourceSchema } from '../resource/resource.oa-component';

export const patchPEPFormSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    // Sale Information
    saleInfoTransactionReason: { type: 'string', nullable: true },
    saleInfoInMyNameOrProxy: { type: 'string', nullable: true },
    saleInfoUseOrInvestment: { type: 'string', nullable: true },

    valuationPurpose: { type: 'string', enum: [null, ...Object.values(PepFormValuationPurpose)], nullable: true },
    valuationDescriptionOfOther: { type: 'string', nullable: true },
    // Property Information
    propertyInfoOwnedForTime: { type: 'string', nullable: true },
    propertyInfoIsRenovatedByOwner: { type: 'boolean', nullable: true },
    propertyInfoRenovator: { type: 'string', nullable: true },
    propertyInfoRenovationFinance: { type: 'string', nullable: true },
    propertyInfoRenovationDocumentation: { type: 'string', nullable: true },

    valuationSincePurchase: { type: 'boolean', nullable: true },
    valuationYear: { type: 'number', nullable: true },
    // Equity Information
    equityInfoPercent: { type: 'string', nullable: true },
    equityInfoSource: { type: 'string', nullable: true },
    // Financing Information
    financing: {
      type: 'object',
      properties: {
        newHome: { type: 'string', nullable: true },
        doYouWantFinancing: { type: 'boolean', nullable: true },
        participantsSelected: { type: 'array', items: { type: 'string' }, nullable: true },
      },
    },
    // Signing related
    idfyDocumentId: { type: 'string', nullable: true },
    signingStarted: { type: 'string', nullable: true },
  },
};

export const patchPEPFormResponseSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    formId: { type: 'string' },
  },
};

export const createPEPFormResponseSchema = patchPEPFormResponseSchema;

export const getPEPFormResponseSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    ...resourceSchema.properties,
    estateVitecId: { type: 'string', nullable: false },
    isNotificationSent: { type: 'boolean', nullable: true },
    type: { type: 'string', enum: [null, ...Object.values(PEPType)], nullable: true },
    // Signing related
    signingFinished: { type: 'string', nullable: true },
    estateAddress: { type: 'string', nullable: true },
    estateAssignmentNumber: { type: 'string', nullable: true },
    estateAssignmentTypeGroup: {
      type: 'number',
      enum: [null, ...Object.values(EstateMongooseAssignmentTypeGroups)],
      nullable: true,
    },
    ...patchPEPFormSchema.properties,
  },
};
