import type { OpenAPIV3 } from 'openapi-types';
import { descriptionOfSelectedSchema } from './description-of-selected.oa-component';

export const descriptionsOfSelectedConnectionSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: [...(descriptionOfSelectedSchema.required as string[])],
  properties: {
    ...descriptionOfSelectedSchema.properties,
    name: { type: 'string', nullable: false },
    relationship: { type: 'string', nullable: false },
  },
};
