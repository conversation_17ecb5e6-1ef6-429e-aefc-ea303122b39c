import type { OpenAPIV3 } from 'openapi-types';

export const descriptionOfSelectedSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['description', 'countries', 'isLessThanAYearAgo'],
  properties: {
    description: { type: 'string', nullable: false },
    countries: { type: 'string', nullable: false },
    isLessThanAYearAgo: { type: 'boolean', nullable: false },
    dateWhenPracticed: { type: 'string', nullable: true },
  },
  nullable: true,
};
