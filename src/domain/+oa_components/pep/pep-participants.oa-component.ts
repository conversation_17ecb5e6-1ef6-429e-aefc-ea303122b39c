import type { OpenAPIV3 } from 'openapi-types';
import { PEPType } from '../../pep-form-participant/pep-type';
import { resourceSchema } from '../resource/resource.oa-component';
import { descriptionsOfSelectedConnectionSchema } from './description-of-selected-connection.oa-component';
import { descriptionOfSelectedSchema } from './description-of-selected.oa-component';

const pepTypeSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'string',
  nullable: true,
  enum: [null, ...Object.values(PEPType)],
};

export const patchPEPFormParticipantSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    name: { type: 'string', nullable: true },
    email: { type: 'string', nullable: true },
    phoneNumber: { type: 'string', nullable: true },
    profession: { type: 'string', nullable: true },
    employer: { type: 'string', nullable: true },
    selectedCountry: { type: 'string', nullable: true },
    typedCountry: { type: 'string', nullable: true },
    estateCount: { type: 'string', nullable: true },
    citizenshipDescription: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          selectedCountry: { type: 'string' },
          typedCountry: { type: 'string' },
        },
      },
      nullable: true,
    },
    ownPepType: pepTypeSchema,
    ownDescriptionsOfSelected: {
      type: 'object',
      properties: {
        ...descriptionOfSelectedSchema.properties,
      },
      nullable: true,
    },
    ownComment: { type: 'string', nullable: true },
    employerPepType: pepTypeSchema,
    employerDescriptionsOfSelected: {
      type: 'object',
      properties: {
        ...descriptionsOfSelectedConnectionSchema.properties,
      },
      nullable: true,
    },
    employerComment: { type: 'string', nullable: true },
    familyPepType: pepTypeSchema,
    familyDescriptionsOfSelected: {
      type: 'object',
      properties: {
        ...descriptionsOfSelectedConnectionSchema.properties,
      },
      nullable: true,
    },
    familyComment: { type: 'string', nullable: true },
  },
};

export const patchPEPFormParticipantResponseSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    participantId: { type: 'string', nullable: false },
  },
};

export const getPEPFormParticipantsResponseSchema: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  additionalProperties: false,
  nullable: false,
  items: {
    type: 'object',
    properties: {
      ...resourceSchema.properties,
      contactPersonVitecId: { type: 'string', nullable: true },
      pepFormId: { type: 'string', nullable: true },
      settlementBuyerParticipantId: { type: 'string', nullable: true },
      ...patchPEPFormParticipantSchema.properties,
    },
  },
};
