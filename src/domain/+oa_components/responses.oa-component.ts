import type { OpenAPIV3 } from 'openapi-types';

export const detailedOAErrorResponse: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  description: 'handled error happened during the request',
  required: ['detail'],
  properties: {
    detail: { type: 'string' },
  },
};

export const userDocuments: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  items: {
    type: 'object',
    required: ['documentId', 'head', 'extension', 'docType', 'lastChanged'],
    additionalProperties: false,
    properties: {
      bucket: { type: 'string' },
      title: { type: 'string' },
      documentId: { type: 'string' },
      head: { type: 'string' },
      extension: { type: 'string' },
      docType: { type: 'string' },
      lastChanged: { type: 'string' },
      signature: { type: 'string' }, // only on get-estate-documents-endpoint
    },
  },
};

export const signableDocuments: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  items: {
    type: 'object',
    required: ['title', 'signUrl'],
    additionalProperties: false,
    properties: {
      title: { type: 'string' },
      signUrl: { type: 'string' },
    },
  },
};

export const userDocumentsCountGroupedByEstateResponse: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: {
    type: 'object',
    properties: {
      documentsToSignAmount: { type: 'number' },
    },
  },
};

export const baseOAErrorResponses: { [key: number]: OpenAPIV3.NonArraySchemaObject } = {
  [400]: { ...detailedOAErrorResponse, description: 'bad request' },
  [403]: { ...detailedOAErrorResponse, description: 'user is not authorized for the endpoint' },
  [404]: { ...detailedOAErrorResponse, description: 'not found' },
  [500]: { ...detailedOAErrorResponse, description: 'not handled error happened' },
};
