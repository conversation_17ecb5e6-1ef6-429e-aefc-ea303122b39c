import type { OpenAPIV3 } from 'openapi-types';
import { FileType } from '../../file/file';

export const fileCreateSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['content', 'fileName', 'mimeType'],
  properties: {
    content: { type: 'string' },
    fileName: { type: 'string' },
    mimeType: { type: 'string' },
  },
};

export const filePatchSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['fileName', 'mimeType'],
  properties: {
    content: { type: 'string', nullable: true },
    fileName: { type: 'string' },
    mimeType: { type: 'string' },
  },
};

export const fileDetailsSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    id: { type: 'string', format: 'uuid' },
    type: { type: 'string', enum: Object.values(FileType) },
    fileName: { type: 'string' },
    mimeType: { type: 'string' },
  },
};
