import type { OpenAPIV3 } from 'openapi-types';

export type EnumToJsonDictionary = <T extends Record<string, string>>(params: {
  enumObject: T;
  jsonSchema: OpenAPIV3.NonArraySchemaObject | OpenAPIV3.ArraySchemaObject;
}) => OpenAPIV3.NonArraySchemaObject;
export const enumToJsonDictionary: EnumToJsonDictionary = ({ enumObject, jsonSchema }) =>
  Object.values(enumObject).reduce(
    (acc, enumValue) => ({
      ...acc,
      properties: { ...acc.properties, [enumValue]: jsonSchema },
    }),
    {
      type: 'object',
      additionalProperties: false, // DO NOT ADD REQUIRED PROPS AS IT WILL NOT BE BACKWARD COMPATIBLE WHEN ADDING NEW SETTLEMENT LEAD PROVIDER
      properties: {},
    },
  );
