import type { EnumToJsonDictionary } from './enum-to-json-dictionary';
import { enumToJsonDictionary } from './enum-to-json-dictionary';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type TestCase<TFunctionType extends (...args: any[]) => any> = {
  it: string;
  input: Parameters<TFunctionType>;
  expected: ReturnType<TFunctionType>;
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type RunTestCases = <TFunctionType extends (...args: any[]) => any>(params: {
  functionToTest: TFunctionType;
  testCases: TestCase<TFunctionType>[];
}) => void;
const runTestCases: RunTestCases = ({ functionToTest, testCases }) =>
  testCases.forEach((testCase) =>
    it(testCase.it, () => expect(functionToTest(...testCase.input)).toEqual(testCase.expected)),
  );

enum TestEnum {
  TEST1 = 'TEST1',
  TEST2 = 'TEST2',
}
describe('enumToJsonDictionary', () => {
  const testCases: TestCase<EnumToJsonDictionary>[] = [
    {
      it: 'should return the correct schema with string type json',
      input: [{ enumObject: TestEnum, jsonSchema: { type: 'string' } }],
      expected: {
        type: 'object',
        additionalProperties: false,
        properties: {
          ['TEST1']: { type: 'string' },
          ['TEST2']: { type: 'string' },
        },
      },
    },
    {
      it: 'should return the correct schema with object type json',
      input: [{ enumObject: TestEnum, jsonSchema: { type: 'object', properties: { key: { type: 'string' } } } }],
      expected: {
        type: 'object',
        additionalProperties: false,
        properties: {
          ['TEST1']: { type: 'object', properties: { key: { type: 'string' } } },
          ['TEST2']: { type: 'object', properties: { key: { type: 'string' } } },
        },
      },
    },
    {
      it: 'should return the correct schema with array type json',
      input: [
        {
          enumObject: TestEnum,
          jsonSchema: { type: 'array', items: { type: 'object', properties: { key: { type: 'string' } } } },
        },
      ],
      expected: {
        type: 'object',
        additionalProperties: false,
        properties: {
          ['TEST1']: { type: 'array', items: { type: 'object', properties: { key: { type: 'string' } } } },
          ['TEST2']: { type: 'array', items: { type: 'object', properties: { key: { type: 'string' } } } },
        },
      },
    },
  ];

  runTestCases({ functionToTest: enumToJsonDictionary, testCases });
});
