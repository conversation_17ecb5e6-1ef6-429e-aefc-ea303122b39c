import type { OpenAPIV3 } from 'openapi-types';
import {
  TimelineBucketItemType,
  TimelineBucketStatus,
  TimelineJourney,
  TimelineTodoType,
} from '../../estate/estate-timeline/estate-timeline';
import { EstateMongooseAssignmentTypeGroups } from '../../estate/mongo/estate.mongoose-types';
import { imageOAComponent } from '../image/image.oa-component';

export const timelineOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['estateID', 'journeyType', 'digitalInspectionUrl', 'buckets'],
  properties: {
    estateID: { type: 'string', nullable: true },
    journeyType: { type: 'string', enum: Object.values(TimelineJourney) },
    digitalInspectionUrl: { type: 'string', nullable: true },
    buckets: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'title', 'step', 'logName', 'status'],
        properties: {
          id: { type: 'string' },
          title: { type: 'string' },
          step: { type: 'number' },
          logName: { type: 'string' },
          status: { type: 'string', enum: Object.values(TimelineBucketStatus) },
        },
      },
    },
    broker: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        image: imageOAComponent,
        email: { type: 'string' },
        mobilePhone: { type: 'string' },
        workPhone: { type: 'string' },
      },
      nullable: true,
    },
  },
};

export const timelineBucketItemOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  nullable: true,
  required: ['id', 'type', 'title', 'logName', 'isComplete'],
  properties: {
    id: { type: 'string' },
    type: { type: 'string', enum: Object.values(TimelineBucketItemType) },
    title: { type: 'string' },
    logName: { type: 'string' },
    isComplete: { type: 'boolean' },
    date: { type: 'string', nullable: true },
    dateFrom: { type: 'string', nullable: true },
    dateUntil: { type: 'string', nullable: true },
    url: { type: 'string' },
    items: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number' },
          title: { type: 'string' },
          description: { type: 'string' },
          logName: { type: 'string' },
          isComplete: { type: 'boolean' },
        },
      },
    },
  },
};

export const timelineBucketItemsOAComponent: OpenAPIV3.ArraySchemaObject = {
  type: 'array',
  items: timelineBucketItemOAComponent,
};

export const timelineBucketOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: [
    'estateID',
    'journeyType',
    'digitalInspectionUrl',
    'id',
    'title',
    'step',
    'logName',
    'description',
    'status',
    'items',
  ],
  properties: {
    estateID: { type: 'string', nullable: true },
    journeyType: { type: 'string', enum: Object.values(TimelineJourney) },
    digitalInspectionUrl: { type: 'string', nullable: true },
    id: { type: 'string' },
    title: { type: 'string' },
    step: { type: 'number' },
    logName: { type: 'string' },
    description: { type: 'string' },
    status: { type: 'string', enum: [null, ...Object.values(TimelineBucketStatus)] },
    items: timelineBucketItemsOAComponent,
    assignmentTypeGroup: {
      type: 'number',
      enum: [null, ...Object.values(EstateMongooseAssignmentTypeGroups)],
      nullable: true,
    },
  },
};

export const timelineTodoOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['todos'],
  properties: {
    todos: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          title: { type: 'string' },
          type: { type: 'string', enum: Object.values(TimelineTodoType) },
          item: timelineBucketItemOAComponent,
        },
      },
    },
  },
};
