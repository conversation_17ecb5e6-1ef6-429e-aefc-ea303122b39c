import type { OpenAPIV3 } from 'openapi-types';
import { PropertyType, UserEstateRelation, UserPreference } from '../../estate/estate';
import { imageOAComponent } from '../image/image.oa-component';
import { googleAnalyticsResponse } from '../statistics/google.oa-component';

export const propertyTypeOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'string',
  enum: [
    null,
    PropertyType.HOUSE,
    PropertyType.APARTMENT,
    PropertyType.DETACHED_HOUSE,
    PropertyType.TOWNHOUSE,
    PropertyType.SEMI_DETACHED_HOUSE,
    PropertyType.PLOT,
    PropertyType.HOLIDAY_HOME,
  ],
  nullable: true,
};

export const preferenceOAComponent = {
  type: 'string',
  enum: [null, UserPreference.NOW, UserPreference.RIGHT_PRICE, UserPreference.WITHIN_SIX_MONTHS],
  nullable: true,
};

export const estateHeaderResponseOACOmponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  required: [
    'id',
    'vitecID',
    'pgEstateId',
    'type',
    'image',
    'address',
    'streetAddress',
    'status',
    'broker',
    'soldDate',
    'soldPrice',
    'takeOverDate',
    'propertyType',
    'vitecStatus',
    'areEstateImagesShowableInPictureGallery',
    'inOsloArea',
    'zipCode',
    'isSteddyAvailable',
  ],
  additionalProperties: false,
  properties: {
    id: { type: 'string' },
    vitecID: { type: 'string' },
    pgEstateId: { type: 'string' },
    type: { type: 'string', enum: ['forSale', 'owned'] },
    image: {
      nullable: true,
      type: 'object',
      required: ['large', 'medium', 'small'],
      additionalProperties: false,
      properties: {
        large: { type: 'string' },
        medium: { type: 'string' },
        small: { type: 'string' },
      },
    },
    address: { type: 'string' },
    streetAddress: { type: 'string' },
    status: {
      type: 'object',
      required: ['remainingSteps', 'stepName'],
      additionalProperties: false,
      properties: {
        remainingSteps: { type: 'number' },
        stepName: { type: 'string' },
      },
    },
    broker: {
      nullable: true,
      type: 'object',
      required: ['name'],
      additionalProperties: false,
      properties: {
        name: { type: 'string' },
        email: { type: 'string' },
        mobilePhone: { type: 'string' },
        workPhone: { type: 'string' },
        image: {
          nullable: true,
          type: 'object',
          properties: {
            large: { type: 'string' },
            medium: { type: 'string' },
            small: { type: 'string' },
          },
        },
      },
    },
    statusText: {
      type: 'string',
    },
    bidValue: {
      nullable: true,
      type: 'number',
    },
    userEstateRelation: {
      nullable: true,
      type: 'string',
      enum: [null, ...Object.values(UserEstateRelation)],
    },
    vitecStatus: {
      type: 'number',
      nullable: true,
    },
    soldDate: { type: 'string', nullable: true },
    soldPrice: { type: 'number', nullable: true },
    takeOverDate: { type: 'string', nullable: true },
    createdDate: { type: 'string', nullable: true },
    propertyType: { type: 'string' },
    floor: { type: 'number' },
    area: { type: 'number' },
    buildYear: { type: 'number' },
    numberOfBedrooms: { type: 'number' },
    sellPreference: { type: 'string' },
    connectToBroker: { type: 'boolean' },
    ownerSince: { type: 'string' },
    estimatedValue: { type: 'string' },
    salesProcess: {
      type: 'object',
      properties: {
        stepName: { type: 'string', nullable: true },
        nextEvent: { type: 'string', nullable: true },
      },
    },
    valuationChange: {
      type: 'object',
      properties: {
        increasePercentage: { type: 'number', nullable: true },
        increaseValue: { type: 'number', nullable: true },
      },
    },
    areEstateImagesShowableInPictureGallery: {
      type: 'boolean',
      nullable: true,
    },
    inOsloArea: { type: 'boolean' },
    zipCode: { type: 'string' },
    isSteddyAvailable: { type: 'boolean' },
  },
};

export const createEstateOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  required: ['address', 'propertyType', 'numberOfBedrooms', 'sellPreference'],
  additionalProperties: false,
  properties: {
    address: { type: 'string' },
    propertyType: { type: 'string' },
    numberOfBedrooms: { type: 'number' },
    sellPreference: {
      type: 'string',
      enum: [UserPreference.NOW, UserPreference.RIGHT_PRICE, UserPreference.WITHIN_SIX_MONTHS],
    },
  },
};

export const estateBrokerOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  properties: {
    employeeId: { type: 'string' },
    brokerRole: { type: 'number' },
    image: imageOAComponent,
    slug: { type: 'string' },
    name: { type: 'string' },
    email: { type: 'string' },
    title: { type: 'string' },
    mobilePhone: { type: 'string' },
    workPhone: { type: 'string' },
  },
};

export const estateBidOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    bidId: { type: 'number' },
    type: { type: 'number' },
    time: { type: 'string' },
    amount: { type: 'number' },
    partyid: { type: 'string' },
    expires: { type: 'string' },
    reservations: { type: 'boolean' },
    accepted: { type: 'boolean' },
    changedDate: { type: 'string' },
    rejectedDate: { type: 'string', nullable: true },
  },
};

export const estateResponseOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: [
    'id',
    'vitecID',
    'address',
    'image',
    'matches',
    'primaryRoomArea',
    'numberOfBedrooms',
    'propertyType',
    'valuation',
    'purchasePrice',
    'statusText',
    'brokers',
    'bids',
    'price',
    'statistics',
    'ownership',
    'constructionYear',
    'status',
  ],
  properties: {
    id: { type: 'string' },
    floor: { type: 'number' },
    vitecID: { type: 'string' },
    hasValuation: { type: 'boolean' },
    address: { type: 'string' },
    image: imageOAComponent,
    matches: { type: 'number' },
    primaryRoomArea: { type: 'number' },
    numberOfBedrooms: { type: 'number' },
    propertyType: { type: 'string' },
    valuation: { type: 'number', nullable: true },
    purchasePrice: { type: 'number' },
    statusText: {
      type: 'string',
    },
    brokers: {
      type: 'array',
      items: estateBrokerOAComponent,
    },
    bids: {
      type: 'array',
      items: estateBidOAComponent,
    },
    price: {
      nullable: true,
      type: 'object',
      additionalProperties: false,
      properties: {
        value: { type: 'number', nullable: true },
        label: { type: 'string', nullable: true },
        lastBidValue: { type: 'number', nullable: true },
      },
    },
    statistics: {
      nullable: true,
      type: 'object',
      additionalProperties: false,
      properties: {
        finnNo: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              key: { type: 'string' },
              value: { type: 'number' },
            },
          },
        },
        nordvikboligNo: googleAnalyticsResponse,
      },
    },
    ownership: { type: 'string' },
    constructionYear: { type: 'number' },
    status: { type: 'number' },
    userEstateRelation: {
      nullable: true,
      type: 'string',
      enum: [null, ...Object.values(UserEstateRelation)],
    },
    wentToViewingNo: { type: 'number', nullable: true },
    interestedNo: { type: 'number', nullable: true },
  },
};

export const estateValuationOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  additionalProperties: false,
  nullable: true,
  required: [
    'totalEstimatedValue',
    'mortgage',
    'equity',
    'value',
    'storebrandSaving',
    'extendedMortgage',
    'moreExpensiveProperty',
    'affordMoreSpace',
  ],
  properties: {
    estateId: { type: 'string', nullable: true },
    streetAddress: { type: 'string', nullable: true },
    totalEstimatedValue: { type: 'number', nullable: true },
    areFinancialDetailsFilled: { type: 'boolean' },
    hasRecentBrokerValuation: { type: 'boolean' },
    mortgage: {
      type: 'object',
      properties: {
        percentage: { type: 'number', nullable: true },
        value: { type: 'number', nullable: true },
      },
    },
    equity: {
      type: 'object',
      properties: {
        percentage: { type: 'number', nullable: true },
        value: { type: 'number', nullable: true },
      },
    },
    value: {
      type: 'object',
      properties: {
        initial: { type: 'number', nullable: true },
        current: { type: 'number', nullable: true },
        increasePercentage: { type: 'number', nullable: true },
        increaseValue: { type: 'number', nullable: true },
      },
    },
    storebrandSaving: { type: 'number', nullable: true },
    extendedMortgage: { type: 'number', nullable: true },
    moreExpensiveProperty: { type: 'number', nullable: true },
    affordMoreSpace: { type: 'number', nullable: true },
  },
};

export const estateValuationsOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: [],
  properties: {
    hasSentStorebrandLead: {
      type: 'boolean',
    },
    estates: {
      type: 'array',
      items: estateValuationOAComponent,
    },
    aggregation: estateValuationOAComponent,
  },
};
