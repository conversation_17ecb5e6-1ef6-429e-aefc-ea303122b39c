import type { OpenAPIV3 } from 'openapi-types';
import { EstateTimelineItemType } from '../../estate/estate-timeline/estate-timeline-item';
import { EstateTimelineBuyerBucketName } from '../../estate/estate-timeline/buyer/estate-timeline-buyer';

export const estateTimelineBuyerItemOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  required: ['type', 'name', 'id', 'isComplete'],
  additionalProperties: false,
  properties: {
    type: { type: 'string', enum: [EstateTimelineItemType.CHECKLIST, EstateTimelineItemType.ITEM] },
    name: { type: 'string' },
    id: { type: 'string' },
    isComplete: { type: 'boolean' },
    value: { type: 'string', nullable: true },
    logName: { type: 'string' },
  },
};

export const estateTimelineBuyerSectionOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  required: ['name', 'items', 'isActive'],
  additionalProperties: false,
  properties: {
    name: {
      type: 'string',
      enum: [EstateTimelineBuyerBucketName.TAKEOVER, EstateTimelineBuyerBucketName.MOVE_IN],
    },
    items: { type: 'array', items: estateTimelineBuyerItemOAComponent },
    isActive: { type: 'boolean' },
    logName: { type: 'string' },
  },
};
