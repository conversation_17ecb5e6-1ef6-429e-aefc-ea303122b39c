import type { OpenAPIV3 } from 'openapi-types';
import { EstateTimelineItemType } from '../../estate/estate-timeline/estate-timeline-item';
import { EstateTimelineSellerBucketName } from '../../estate/estate-timeline/seller/estate-timeline-seller';

export const estateTimelineSellerItemOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  required: ['type', 'name', 'id', 'isComplete'],
  additionalProperties: false,
  properties: {
    type: { type: 'string', enum: [EstateTimelineItemType.CHECKLIST, EstateTimelineItemType.ITEM] },
    name: { type: 'string' },
    id: { type: 'string' },
    isComplete: { type: 'boolean' },
    value: { type: 'string', nullable: true },
    logName: { type: 'string' },
  },
};

export const estateTimelineSellerSectionOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  required: ['name', 'items', 'isActive'],
  additionalProperties: false,
  properties: {
    name: {
      type: 'string',
      enum: [
        EstateTimelineSellerBucketName.BIDDING,
        EstateTimelineSellerBucketName.FINISHING,
        EstateTimelineSellerBucketName.MARKETING,
        EstateTimelineSellerBucketName.PREPARATION,
        EstateTimelineSellerBucketName.VIEWING,
      ],
    },
    items: { type: 'array', items: estateTimelineSellerItemOAComponent },
    isActive: { type: 'boolean' },
    logName: { type: 'string' },
  },
};
