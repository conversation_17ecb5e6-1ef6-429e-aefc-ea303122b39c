import type { OpenAPIV3 } from 'openapi-types';

export const estateBuyerOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  properties: {
    contactId: { type: 'string' },
    contactType: { type: 'number' },
    companyName: { type: 'string' },
    firstName: { type: 'string' },
    lastName: { type: 'string' },
    mobilePhone: { type: 'string' },
  },
};

export const estateSellerOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  properties: estateBuyerOAComponent.properties,
};
