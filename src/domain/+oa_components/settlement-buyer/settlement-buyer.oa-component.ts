import type { OpenAPIV3 } from 'openapi-types';
import { FinancedWithLoan } from '../../settlement-buyer/settlement-buyer';
import {
  getPEPFormParticipantsResponseSchema,
  patchPEPFormParticipantSchema,
} from '../pep/pep-participants.oa-component';
import { resourceSchema } from '../resource/resource.oa-component';
import { settlementFinanceAccountManagerSchema } from '../settlement/settlement-finance-account-manager.oa-component';
import { settlementLeadsSchema } from '../settlement/settlement-leads.oa-component';
import { patchSettlementBuyerLoanSchema, settlementBuyerLoanSchema } from './settlement-buyer-loan.oa-component';
import {
  patchSettlementBuyerParticipantSchema,
  settlementBuyerParticipantSchema,
} from './settlement-buyer-participant.oa-component';

export const patchSettlementBuyerSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    hasEquity: { type: 'boolean', nullable: true },
    financeAccountNumber: { type: 'string', nullable: true },
    financeAccountOwnerName: { type: 'string', nullable: true },
    hasFinanceAccountManagers: { type: 'boolean', nullable: true },
    financeAccountManagers: {
      type: 'array',
      items: settlementFinanceAccountManagerSchema,
      nullable: true,
    },
    equityAccountNumber: { type: 'string', nullable: true },
    equityAccountHolderName: { type: 'string', nullable: true },
    hasEquityAccountManagers: { type: 'boolean', nullable: true },
    equityAccountManagers: {
      type: 'array',
      items: settlementFinanceAccountManagerSchema,
      nullable: true,
    },
    isFinanceAndEquityAccountsTheSame: { type: 'boolean', nullable: true },
    financedWithLoan: { type: 'string', nullable: true, enum: [...Object.values(FinancedWithLoan), null] },
    loans: {
      type: 'array',
      items: patchSettlementBuyerLoanSchema,
      nullable: false,
    },
    participants: {
      type: 'array',
      items: patchSettlementBuyerParticipantSchema,
      nullable: false,
    },
    leads: settlementLeadsSchema,
    saleInfoTransactionReason: { type: 'string', nullable: true },
    saleInfoInMyNameOrProxy: { type: 'string', nullable: true },
    saleInfoUseOrInvestment: { type: 'string', nullable: true },
    signingStarted: {
      type: 'string',
      nullable: true,
    },
    idfyDocumentId: {
      type: 'string',
      nullable: true,
    },
    accountPepParticipants: { type: 'array', items: patchPEPFormParticipantSchema, nullable: true },
  },
};

export const settlementBuyerSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    ...resourceSchema.properties,
    ...patchSettlementBuyerSchema.properties,
    estateVitecId: { type: 'string', nullable: false },
    estateAssignmentNumber: { type: 'string', nullable: true },
    estateAddress: { type: 'string', nullable: true },
    idfyDocumentId: { type: 'string', nullable: true },
    signingStarted: { type: 'string', nullable: true },
    signingFinished: { type: 'string', nullable: true },
    isNotificationSent: { type: 'boolean', nullable: true },
    fileId: { type: 'string', format: 'uuid', nullable: true },
    loans: {
      type: 'array',
      items: settlementBuyerLoanSchema,
      nullable: false,
    },
    participants: {
      type: 'array',
      items: settlementBuyerParticipantSchema,
      nullable: false,
    },
    isOsloTransportEnabledForEstate: { type: 'boolean' },
    hasAccessToExpoNova: { type: 'boolean' },
    accountPepParticipants: getPEPFormParticipantsResponseSchema,
    isKokkelorenEnabledForEstate: { type: 'boolean' },
    isSteddyEnabledForEstate: { type: 'boolean' },
    isVerketInteriorEnabledForEstate: { type: 'boolean' },
    telenor: {
      type: 'object',
      nullable: true,
      properties: {
        offers: {
          type: 'array',
          items: {
            type: 'object',
            nullable: true,
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              serviceTechnology: { type: 'string' },
              establishmentFee: {
                type: 'object',
                nullable: true,
                properties: {
                  amount: { type: 'number' },
                  currency: { type: 'string' },
                },
              },
              pricePerMonth: {
                type: 'object',
                properties: {
                  amount: { type: 'number' },
                  currency: { type: 'string' },
                },
              },
              campaignPricePerMonth: {
                type: 'object',
                properties: {
                  amount: { type: 'number' },
                  currency: { type: 'string' },
                },
              },
              campaignDurationInMonths: { type: 'number' },
              bandwidth: {
                type: 'object',
                properties: {
                  download: {
                    type: 'object',
                    properties: {
                      value: { type: 'number' },
                      unit: { type: 'string' },
                    },
                  },
                  upload: {
                    type: 'object',
                    properties: {
                      value: { type: 'number' },
                      unit: { type: 'string' },
                    },
                  },
                },
              },
              hasTv: { type: 'boolean' },
            },
          },
        },
        addressId: { type: 'string' },
      },
    },
  },
};
