import type { OpenAPIV3 } from 'openapi-types';
import { OriginOfEquity } from '../../settlement-buyer/settlement-buyer-equity';
import { resourceSchema } from '../resource/resource.oa-component';

export const patchSettlementBuyerEquitySchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    amountOfEquity: { type: 'string', nullable: true },
    bankOfEquity: { type: 'string', nullable: true },
    originOfEquity: { type: 'string', nullable: true, enum: [...Object.values(OriginOfEquity)] },
    originOfEquityComment: { type: 'string', nullable: true },
  },
};

export const settlementBuyerEquitySchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: [
    'id',
    'createdAt',
    'updatedAt',
    'amountOfEquity',
    'bankOfEquity',
    'originOfEquity',
    'originOfEquityComment',
  ],
  properties: {
    ...resourceSchema.properties,
    ...patchSettlementBuyerEquitySchema.properties,
  },
};
