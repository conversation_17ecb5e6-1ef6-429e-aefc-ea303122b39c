import type { OpenAPIV3 } from 'openapi-types';
import { resourceSchema } from '../resource/resource.oa-component';

export const patchSettlementBuyerLoanSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    loanBank: { type: 'string', nullable: true },
    bankContactName: { type: 'string', nullable: true },
    bankContactPhone: { type: 'string', nullable: true },
    bankContactEmail: { type: 'string', nullable: true },
    loanTaker: { type: 'string', nullable: true },
  },
};

export const settlementBuyerLoanSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: [
    'id',
    'createdAt',
    'updatedAt',
    'loanBank',
    'bankContactName',
    'bankContactPhone',
    'bankContactEmail',
    'loanTaker',
  ],
  properties: {
    ...resourceSchema.properties,
    ...patchSettlementBuyerLoanSchema.properties,
  },
};
