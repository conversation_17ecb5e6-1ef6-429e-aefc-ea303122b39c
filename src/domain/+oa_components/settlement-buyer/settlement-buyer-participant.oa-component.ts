import type { OpenAPIV3 } from 'openapi-types';
import { patchPEPFormParticipantSchema } from '../pep/pep-participants.oa-component';
import { resourceSchema } from '../resource/resource.oa-component';
import { patchSettlementBuyerEquitySchema, settlementBuyerEquitySchema } from './settlement-buyer-equity.oa-component';

export const patchSettlementBuyerParticipantSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  properties: {
    id: { type: 'string', nullable: false },
    name: { type: 'string', nullable: true },
    email: { type: 'string', nullable: true },
    phoneNumber: { type: 'string', nullable: true },
    hasEquity: { type: 'boolean', nullable: true },
    equities: {
      type: 'array',
      items: patchSettlementBuyerEquitySchema,
      nullable: false,
    },
  },
};

export const settlementBuyerParticipantSchema: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['id', 'createdAt', 'updatedAt', 'name', 'email', 'phoneNumber', 'hasEquity', 'equities'],
  properties: {
    ...resourceSchema.properties,
    ...patchSettlementBuyerParticipantSchema.properties,
    equities: {
      type: 'array',
      items: settlementBuyerEquitySchema,
      nullable: false,
    },
    pepParticipant: {
      type: 'object',
      properties: { ...patchPEPFormParticipantSchema.properties, ...resourceSchema.properties },
      nullable: true,
    },
  },
};
