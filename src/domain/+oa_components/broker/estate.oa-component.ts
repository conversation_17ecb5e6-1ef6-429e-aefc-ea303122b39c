import type { OpenAPIV3 } from 'openapi-types';
import { reminderOAComponent } from '../reminder/reminder.oa-component';

export const brokerEstateOAComponent: OpenAPIV3.SchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['id', 'estateID', 'title', 'address', 'reminders', 'relatedBrokerRoles'],
  properties: {
    id: { type: 'string' },
    estateID: { type: 'string' },
    title: { type: 'string' },
    address: { type: 'string' },
    reminders: { type: 'array', items: reminderOAComponent },
    relatedBrokerRoles: { type: 'array', items: { type: 'number' } },
    estateStatus: { type: 'number' },
  },
};
