import type { OpenAPIV3 } from 'openapi-types';

export const brokerOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['id', 'createdAt', 'updatedAt', 'email'],
  properties: {
    id: { type: 'string', format: 'uuid' },
    createdAt: { type: 'string' },
    updatedAt: { type: 'string' },
    email: { type: 'string' },
    name: { type: 'string' },
    phoneNumber: { type: 'string' },
  },
};
