import type { OpenAPIV3 } from 'openapi-types';
import { PopupType, ReferralPopupState } from '../../popup/popup';

export const popupOAComponent: OpenAPIV3.NonArraySchemaObject = {
  type: 'object',
  additionalProperties: false,
  required: ['id', 'createdAt', 'updatedAt', 'userID', 'type', 'state'],
  properties: {
    id: {
      type: 'string',
      format: 'uuid',
    },
    createdAt: {
      type: 'string',
    },
    updatedAt: {
      type: 'string',
    },
    userID: {
      type: 'string',
      format: 'uuid',
    },
    type: {
      type: 'string',
      enum: Object.values(PopupType),
    },
    state: {
      type: 'string',
      enum: Object.values(ReferralPopupState),
    },
  },
};
