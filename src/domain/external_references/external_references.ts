export type ExternalReferenceType = {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  IDENTIFIER: 'identifier';
};
export type FacebookIdentifierOptions = {
  facebook: {
    userID: string;
  };
};
export type ExternalReferenceOptions = FacebookIdentifierOptions | Record<string, unknown>;
export type ExternalReference = {
  type: ExternalReferenceType;
  options: ExternalReferenceOptions;
};
export const isFacebookIdentifierExternalReference = (
  options: ExternalReferenceOptions,
): options is FacebookIdentifierOptions => !!(options as FacebookIdentifierOptions).facebook;
