import type { User } from '../user/user';
import type { <PERSON>roker } from '../broker/broker';

export type AnalyticsService = {
  identifyAnonymousVisitor(anonymousID: string): void;
  identifyCustomer(customer: User): void;
  identifyBroker(broker: Broker): void;
  trackAnonymousVisitor(anonymousID: string, event: string, properties: Record<string, unknown>): void;
  trackCustomer(customer: User, event: string, properties: Record<string, unknown>): void;
  trackBroker(broker: Broker, event: string, properties: Record<string, unknown>): void;
};
