import { add, addYears, endOfDay, isAfter, isWithinInterval, startOfDay } from 'date-fns';
import i18n from '../../i18n.config';

export type BucketScheme<T> = {
  name: string;
  predicate: (item: T) => boolean;
  items: T[];
  logName?: string;
};

export type Bucket<TO> = {
  name: string;
  items: TO[];
  logName?: string;
};

export type BucketGroup<TI> = {
  insert(item: TI): void;
  getBuckets<TO>(mapOperator: (item: TI) => TO): Bucket<TO>[];
};

export const createBucketGroup = <T>(
  bucketSchemes: BucketScheme<T>[],
  equalityOperator: (lhs: T, rhs: T) => boolean,
): BucketGroup<T> => {
  const insert = (item: T): void => {
    const bucketScheme = bucketSchemes.find((bucketScheme) => bucketScheme.predicate(item));

    if (bucketScheme) {
      const alreadyExists =
        bucketScheme.items.find((existingItem) => equalityOperator(existingItem, item)) !== undefined;

      if (!alreadyExists) {
        bucketScheme.items = [...bucketScheme.items, item];
      }
    }
  };

  const getBuckets = <TO>(mapOperator: (item: T) => TO): Bucket<TO>[] => {
    return bucketSchemes.map((bucketScheme) => {
      return {
        name: bucketScheme.name,
        items: bucketScheme.items.map(mapOperator),
        logName: bucketScheme.logName,
      };
    });
  };

  return { getBuckets, insert };
};

const makeInterval = (startOffset: Duration, endOffset: Duration): Interval => {
  const now = new Date();
  const start = startOfDay(add(now, startOffset));
  const end = endOfDay(add(now, endOffset));
  return {
    start,
    end,
  };
};

export const createDefaultTemporalBucketGroup = <T>(
  name: string,
  dateOperator: (item: T) => Date,
  equalityOperator: (lhs: T, rhs: T) => boolean,
  lognames?: {
    recently: string;
    monthsAgo: string;
    yearsAgo: string;
  },
): BucketGroup<T> => {
  return createBucketGroup<T>(
    [
      {
        name: `${name} ${i18n.__('recently')}`,
        predicate: (item) => isWithinInterval(dateOperator(item), makeInterval({ months: -6 }, {})),
        items: [],
        logName: lognames?.recently,
      },
      {
        name: `${name} > 6 ${i18n.__('months ago')}`,
        predicate: (item) => isWithinInterval(dateOperator(item), makeInterval({ years: -3 }, { months: -6 })),
        items: [],
        logName: lognames?.monthsAgo,
      },
      {
        name: `${name} > 3 ${i18n.__('years ago')}`,
        predicate: (item) => isAfter(addYears(new Date(), -3), dateOperator(item)),
        items: [],
        logName: lognames?.yearsAgo,
      },
    ],
    equalityOperator,
  );
};
