import { addDays, addMonths, addYears } from 'date-fns';
import type { BucketGroup } from './Bucket';
import { createDefaultTemporalBucketGroup, createBucketGroup } from './Bucket';

describe('BucketGroup', () => {
  let testBucketGroup: BucketGroup<number>;

  beforeEach(() => {
    testBucketGroup = createBucketGroup<number>(
      [
        {
          name: `Odd numbers`,
          predicate: (item) => item % 2 !== 0,
          items: [],
        },
        {
          name: `Positive numbers`,
          predicate: (item) => item > 0,
          items: [],
        },
        {
          name: `Negative numbers`,
          predicate: (item) => item < 0,
          items: [],
        },
      ],
      (lhs, rhs) => lhs === rhs,
    );
  });

  describe('#insert', () => {
    test('it should not assign the item to any buckets if there are no matches', () => {
      testBucketGroup.insert(0);

      const buckets = testBucketGroup.getBuckets((item) => item);
      expect(buckets[0].items.length).toEqual(0);
      expect(buckets[1].items.length).toEqual(0);
      expect(buckets[2].items.length).toEqual(0);
    });

    test.each([
      [1, 1, 0, 0],
      [2, 0, 1, 0],
      [-2, 0, 0, 1],
    ])(
      'it should assign the item to the correct bucket (item="%s")',
      (item, firstBucketLength, secondBucketLength, thirdBucketLength) => {
        testBucketGroup.insert(item);

        const buckets = testBucketGroup.getBuckets((item) => item);
        expect(buckets[0].items.length).toEqual(firstBucketLength);
        expect(buckets[1].items.length).toEqual(secondBucketLength);
        expect(buckets[2].items.length).toEqual(thirdBucketLength);
      },
    );

    test('it should assign the item to the first bucket if it belongs to multiple buckets', () => {
      testBucketGroup.insert(101);

      const buckets = testBucketGroup.getBuckets((item) => item);
      expect(buckets[0].items.length).toEqual(1);
      expect(buckets[1].items.length).toEqual(0);
      expect(buckets[2].items.length).toEqual(0);
    });

    test('it should return the mapped input instead of the actual input', () => {
      testBucketGroup.insert(101);

      const buckets = testBucketGroup.getBuckets((item) => item.toString());
      expect(typeof buckets[0].items[0]).toEqual('string');
    });

    test('it should not add duplicated items more than once', () => {
      testBucketGroup.insert(1);
      testBucketGroup.insert(1);

      const buckets = testBucketGroup.getBuckets((item) => item);
      expect(buckets[0].items.length).toEqual(1);
      expect(buckets[1].items.length).toEqual(0);
      expect(buckets[2].items.length).toEqual(0);
    });
  });
});

describe('#createDefaultTemporalBucketGroup', () => {
  let temporalBucketGroup: BucketGroup<string>;

  beforeEach(() => {
    temporalBucketGroup = createDefaultTemporalBucketGroup<string>(
      'Tested',
      (item) => new Date(item),
      (lhs, rhs) => lhs === rhs,
    );
  });

  test('it should create three buckets', () => {
    const buckets = temporalBucketGroup.getBuckets((item) => item);
    expect(buckets.length).toEqual(3);
  });

  test('it should properly name the buckets', () => {
    const buckets = temporalBucketGroup.getBuckets((item) => item);
    expect(buckets[0].name).toEqual('Tested nylig');
    expect(buckets[1].name).toEqual('Tested > 6 måneder siden');
    expect(buckets[2].name).toEqual('Tested > 3 år siden');
  });

  test.each([
    [addDays(new Date(), 1), 0, 0, 0],
    [addDays(new Date(), -1), 1, 0, 0],
    [addMonths(new Date(), -6), 1, 0, 0],
    [addDays(addMonths(new Date(), -6), -1), 0, 1, 0],
    [addYears(new Date(), -3), 0, 1, 0],
    [addDays(addYears(new Date(), -3), -1), 0, 0, 1],
  ])(
    'it should assign the item to the correct bucket (item="%s")',
    (item, firstBucketLength, secondBucketLength, thirdBucketLength) => {
      temporalBucketGroup.insert(item.toISOString());

      const buckets = temporalBucketGroup.getBuckets((item) => item);
      expect(buckets[0].items.length).toEqual(firstBucketLength);
      expect(buckets[1].items.length).toEqual(secondBucketLength);
      expect(buckets[2].items.length).toEqual(thirdBucketLength);
    },
  );
});
