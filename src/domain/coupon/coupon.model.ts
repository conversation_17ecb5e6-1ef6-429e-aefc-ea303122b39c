import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import { REWARDS_TABLE_NAME } from '../../migrations/************/rewards.table';
import { ReferralRewardsModel } from '../referral/referral-rewards/referral-reward.model';
import type { RewardType } from '../referral/reward-types/reward-types';
import type { CouponType } from './coupon';

export const COUPONS_TABLE_NAME = 'Coupons';
export const COUPON_TYPE_ENUM = 'coupon_type';

export const couponAttributes: ModelAttributes = {
  ...baseModelAttributes,
  code: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  rewardID: {
    type: DataTypes.UUID,
    references: {
      model: REWARDS_TABLE_NAME,
      key: 'id',
    },
  },
  type: {
    type: COUPON_TYPE_ENUM,
    allowNull: false,
  },
};

export class CouponModel extends ResourceModel {
  public code!: string;
  public rewardID: RewardType['id'];
  public type!: CouponType;
}

export const couponModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  CouponModel.init(couponAttributes, {
    sequelize,
    tableName: COUPONS_TABLE_NAME,
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['type', 'code'],
      },
    ],
  });
};

export const setCouponModelReferences = (): void => {
  CouponModel.belongsTo(ReferralRewardsModel, { foreignKey: 'rewardID', as: 'coupons' });
};
