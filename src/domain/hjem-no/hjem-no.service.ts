import axios from 'axios';
import type { HjemNoConfig } from '../../config';
import type { Logger } from '../../logger';

export type HjemNoToken = {
  access_token: string;
  token_type: string;
  expires_in: number;
  expires_at: Date;
};

export type HjemNoStatisticsDataPoint = {
  clicks: string;
  views: string;
  emails: string;
  pushes: string;
  group_formatted: string;
};

export type HjemNoStatistics = {
  totalClicks: number;
  totalViews: number;
  totalEmails: number;
  totalPushes: number;
  dailyData: HjemNoStatisticsDataPoint[];
  averageClicksPerDay: number;
  averageViewsPerDay: number;
  clickThroughRate: number; // clicks / views * 100
  emailEngagementRate: number; // emails / views * 100
};

export type HjemNoApiResponse = {
  success: boolean;
  data: HjemNoStatisticsDataPoint[];
};

export type HjemNoService = {
  getStatistics(systemName: string, installationId: string, estateId: string): Promise<HjemNoStatistics>;
  getToken(): Promise<string>;
};

let cachedToken: HjemNoToken | null = null;

export const hjemNoServiceFactory = ({ config, logger }: { config: HjemNoConfig; logger: Logger }): HjemNoService => {
  const getToken = async (): Promise<string> => {
    // Check if cached token is still valid
    if (cachedToken && cachedToken.expires_at > new Date()) {
      return cachedToken.access_token;
    }

    try {
      const response = await axios.post<{
        access_token: string;
        token_type: string;
        expires_in: number;
      }>(
        config.tokenUrl,
        {
          grant_type: 'client_credentials',
          client_id: config.clientId,
          client_secret: config.clientSecret,
        },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );

      const tokenData = response.data;

      // Cache token with expiration (1 minute buffer)
      cachedToken = {
        ...tokenData,
        expires_at: new Date(Date.now() + tokenData.expires_in * 1000 - 60000),
      };

      logger.info('Successfully obtained Hjem.no access token');
      return tokenData.access_token;
    } catch (error) {
      logger.error('Failed to obtain Hjem.no access token', { error });
      throw new Error('Failed to authenticate with Hjem.no API');
    }
  };

  const getStatistics = async (
    systemName: string,
    installationId: string,
    estateId: string,
  ): Promise<HjemNoStatistics> => {
    try {
      const token = await getToken();

      const response = await axios.get<HjemNoApiResponse>(
        `${config.apiBaseUrl}/properties/${systemName}/${installationId}/${estateId}/statistics`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          params: {
            period: 'M',
            grouping: 'day',
          },
        },
      );

      if (!response.data.success) {
        throw new Error('Hjem.no API returned unsuccessful response');
      }

      const data = response.data.data;

      // Transform data to match frontend expectations
      const totalClicks = data.reduce((sum, item) => sum + parseInt(item.clicks, 10), 0);
      const totalViews = data.reduce((sum, item) => sum + parseInt(item.views, 10), 0);
      const totalEmails = data.reduce((sum, item) => sum + parseInt(item.emails, 10), 0);
      const totalPushes = data.reduce((sum, item) => sum + parseInt(item.pushes, 10), 0);

      const daysWithData = data.length;
      const averageClicksPerDay = daysWithData > 0 ? totalClicks / daysWithData : 0;
      const averageViewsPerDay = daysWithData > 0 ? totalViews / daysWithData : 0;

      const clickThroughRate = totalViews > 0 ? (totalClicks / totalViews) * 100 : 0;
      const emailEngagementRate = totalViews > 0 ? (totalEmails / totalViews) * 100 : 0;

      logger.info('Successfully retrieved Hjem.no statistics', {
        systemName,
        installationId,
        estateId,
        totalClicks,
        totalViews,
        daysWithData,
      });

      return {
        totalClicks,
        totalViews,
        totalEmails,
        totalPushes,
        dailyData: data,
        averageClicksPerDay,
        averageViewsPerDay,
        clickThroughRate,
        emailEngagementRate,
      };
    } catch (error) {
      logger.error('Failed to retrieve Hjem.no statistics', { systemName, installationId, estateId, error });
      throw new Error('Failed to fetch statistics from Hjem.no API');
    }
  };

  return {
    getToken,
    getStatistics,
  };
};
