/**
 * Integration test for Hjem.no API
 *
 * This test verifies that the OAuth token can be obtained successfully
 * with the provided credentials. It's disabled by default to avoid
 * making actual API calls during regular test runs.
 *
 * To run this test:
 * 1. Set the environment variables HJEM_NO_CLIENT_ID and HJEM_NO_CLIENT_SECRET
 * 2. Change 'describe.skip' to 'describe' below
 * 3. Run: npm test -- --testPathPattern="hjem-no.integration"
 */

import { createLogger } from '../../logger';
import { hjemNoServiceFactory } from './hjem-no.service';

describe.skip('Hjem.no API Integration', () => {
  const logger = createLogger({ level: 'info', name: 'test', version: '1.0.0' });

  const config = {
    clientId: process.env.HJEM_NO_CLIENT_ID || '9c34d788-4e63-4700-8df2-b750a741f8df',
    clientSecret: process.env.HJEM_NO_CLIENT_SECRET || 'nFcSVCr5iXHtun7iF8XItQKVYkWM0fTbZE8Dk8LX',
    tokenUrl: 'https://apigw.hjem.no/admin-backend/api/v1/oauth/token',
    apiBaseUrl: 'https://apigw.hjem.no/admin-backend/api/v1',
    systemName: 'vitec_next',
    installationId: 'MSNOP',
  };

  const hjemNoService = hjemNoServiceFactory({ config, logger });

  it('should successfully obtain an OAuth token', async () => {
    const token = await hjemNoService.getToken();

    expect(token).toBeDefined();
    expect(typeof token).toBe('string');
    expect(token.length).toBeGreaterThan(0);

    console.log('✅ Successfully obtained Hjem.no OAuth token');
    console.log('Token length:', token.length);
    console.log('Token starts with:', `${token.substring(0, 20)}...`);
  }, 30000); // 30 second timeout for API call

  it('should cache the token and reuse it', async () => {
    const token1 = await hjemNoService.getToken();
    const token2 = await hjemNoService.getToken();

    expect(token1).toBe(token2);
    console.log('✅ Token caching is working correctly');
  }, 30000);

  it('should fail gracefully with invalid credentials', async () => {
    const invalidConfig = {
      ...config,
      clientSecret: 'invalid-secret',
    };

    const invalidService = hjemNoServiceFactory({
      config: invalidConfig,
      logger,
    });

    await expect(invalidService.getToken()).rejects.toThrow('Failed to authenticate with Hjem.no API');
    console.log('✅ Error handling works correctly for invalid credentials');
  }, 30000);
});

/**
 * Manual test for statistics endpoint
 *
 * This test can be used to manually verify that the statistics endpoint works
 * with a real estate ID. You need to:
 * 1. Replace 'REPLACE_WITH_REAL_ESTATE_ID' with an actual estate ID
 * 2. Verify the systemName and installationId are correct
 * 3. Enable the test by changing 'describe.skip' to 'describe'
 */
describe.skip('Hjem.no Statistics API Integration', () => {
  const logger = createLogger({ level: 'info', name: 'test', version: '1.0.0' });

  const config = {
    clientId: process.env.HJEM_NO_CLIENT_ID || '9c34d788-4e63-4700-8df2-b750a741f8df',
    clientSecret: process.env.HJEM_NO_CLIENT_SECRET || 'nFcSVCr5iXHtun7iF8XItQKVYkWM0fTbZE8Dk8LX',
    tokenUrl: 'https://apigw.hjem.no/admin-backend/api/v1/oauth/token',
    apiBaseUrl: 'https://apigw.hjem.no/admin-backend/api/v1',
    systemName: 'vitec_next',
    installationId: 'MSNOP',
  };

  const hjemNoService = hjemNoServiceFactory({ config, logger });

  it('should successfully fetch statistics for an estate', async () => {
    // Replace this with a real estate ID (e.g., CEB82E13-2A43-48A2-A8DD-61B73E7EC729)
    const estateId = 'REPLACE_WITH_REAL_ESTATE_ID';

    const statistics = await hjemNoService.getStatistics(config.systemName, config.installationId, estateId);

    expect(statistics).toBeDefined();
    expect(typeof statistics.totalClicks).toBe('number');
    expect(typeof statistics.totalViews).toBe('number');
    expect(typeof statistics.totalEmails).toBe('number');
    expect(typeof statistics.totalPushes).toBe('number');
    expect(Array.isArray(statistics.dailyData)).toBe(true);

    console.log('✅ Successfully fetched statistics from Hjem.no');
    console.log('Estate ID:', estateId);
    console.log('Statistics:', {
      totalClicks: statistics.totalClicks,
      totalViews: statistics.totalViews,
      totalEmails: statistics.totalEmails,
      totalPushes: statistics.totalPushes,
      dailyDataPoints: statistics.dailyData.length,
      clickThroughRate: `${statistics.clickThroughRate.toFixed(2)}%`,
      emailEngagementRate: `${statistics.emailEngagementRate.toFixed(2)}%`,
    });
  }, 30000);
});
