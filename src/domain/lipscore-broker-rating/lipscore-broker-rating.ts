import type { Resource } from '../../framework/sequelize/resource';

export type LipscoreBrokerRatingReview = {
  id: number;
  createdAt: Date;
  lang: string;
  reviewerShortName: string;
  text: string;
  rating: string;
  recommendation: number;
  agentSatisfaction: number;
};

export type LipscoreBrokerRatingType = {
  id: number;
  createdAt: Date;
  lang: string;
  rating: number;
  reviewerShortName: string;
};

export type LipscoreBrokerRating = Resource & {
  averageRating: number;
  lipscoreId: number;
  ratingCount: string;
  reviewCount: number;
  reviews: LipscoreBrokerRatingReview[];
  vitecBrokerEmployeeId: string;
  ratings: LipscoreBrokerRatingType[];
};

export type LipscoreBrokerRatingAverage = {
  averageRating: string;
  ratingCount: number;
};

export type LipscoreBrokerRatingAverageResponse = LipscoreBrokerRatingAverage & {
  whitelistedReviews?: LipscoreBrokerRatingReview[];
  whitelistedReviewCount?: number;
};
