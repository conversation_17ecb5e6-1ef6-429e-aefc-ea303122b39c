import { ResourceNotFound } from '../../framework/errors/resource.errors';
import type { LipscoreBrokerRatingAverageResponse } from './lipscore-broker-rating';
import type { LipscoreBrokerRatingRepository } from './lipscore-broker-rating.repo';

export type LipscoreBrokerRatingService = {
  getAverage(withReviews: boolean): Promise<LipscoreBrokerRatingAverageResponse>;
};

type LipscoreBrokerRatingServiceFactory = (params: {
  lipscoreBrokerRatingRepository: LipscoreBrokerRatingRepository;
}) => LipscoreBrokerRatingService;
export const lipscoreBrokerRatingServiceFactory: LipscoreBrokerRatingServiceFactory = ({
  lipscoreBrokerRatingRepository,
}) => ({
  getAverage: async (withReviews) => {
    const average = await lipscoreBrokerRatingRepository.getAverage();

    if (!average) {
      throw new ResourceNotFound('Lipscore broker rating average not found');
    }

    if (withReviews) {
      const whitelistedReviews = await lipscoreBrokerRatingRepository.getWhitelistedReviews();
      if (!whitelistedReviews || !whitelistedReviews.length) {
        return average;
      }
      return { ...average, whitelistedReviews, whitelistedReviewCount: whitelistedReviews.length };
    }

    return average;
  },
});
