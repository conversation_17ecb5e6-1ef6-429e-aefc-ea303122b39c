/* eslint-disable @typescript-eslint/naming-convention */
import type { ModelAttributes, Sequelize } from 'sequelize';
import { DataTypes } from 'sequelize';
import { baseModelAttributes, ResourceModel } from '../../framework/sequelize/resource';
import {
  lipscoreBrokerRatingTableAttributes as originalTableAttributes,
  LIPSCORE_BROKER_RATING_TABLE_NAME,
} from '../../migrations/202204191123/lipscore-broker-rating.table';

export const lipscoreBrokerRatingModelAttributes: ModelAttributes = {
  ...baseModelAttributes,
  ...originalTableAttributes,
  ratings: {
    type: DataTypes.JSONB,
    allowNull: true,
  },
};

export class LipscoreBrokerRatingModel extends ResourceModel {
  public averageRating: number;
  public lipscoreId: number;
  public ratingCount: number;
  public reviewCount: number;
  public reviews: Record<string, unknown>;
  public vitecBrokerEmployeeId: string;
  public ratings: Record<string, unknown>;
}

export const lipscoreBrokerRatingModelInit = ({ sequelize }: { sequelize: Sequelize }): void => {
  LipscoreBrokerRatingModel.init(lipscoreBrokerRatingModelAttributes, {
    sequelize,
    tableName: LIPSCORE_BROKER_RATING_TABLE_NAME,
    timestamps: true,
  });
};
