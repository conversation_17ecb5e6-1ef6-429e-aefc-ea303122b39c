import { isEmpty } from 'lodash';
import type { Sequelize } from 'sequelize';
import { QueryTypes } from 'sequelize';
import type { ConfigObject } from '../../config';
import type { LipscoreBrokerRatingAverage, LipscoreBrokerRatingReview } from './lipscore-broker-rating';

export type LipscoreBrokerRatingRepository = {
  getAverage(): Promise<LipscoreBrokerRatingAverage | null>;
  getWhitelistedReviews(): Promise<LipscoreBrokerRatingReview[]>;
  getAllReviews(): Promise<LipscoreBrokerRatingReview[]>;
};

type LipscoreBrokerRatingRepositoryFactory = (params: {
  config: ConfigObject;
  sequelize: Sequelize;
}) => LipscoreBrokerRatingRepository;
export const lipscoreBrokerRatingRepositoryFactory: LipscoreBrokerRatingRepositoryFactory = ({
  config,
  sequelize,
}) => ({
  getAverage: async () => {
    const query = `
      SELECT
          SUM("averageRating" * "ratingCount")/SUM("ratingCount") AS "averageRating",
          SUM("ratingCount") AS "ratingCount"
        FROM "LipscoreBrokerRating";
      `;
    const res = await sequelize.query(query, { type: QueryTypes.SELECT });

    if (!res || isEmpty(res)) {
      return null;
    }

    return res[0] as LipscoreBrokerRatingAverage;
  },

  getAllReviews: async () => {
    const query = `
      WITH "reviews" AS (
        SELECT
            jsonb_array_elements("reviews") AS "review"
          FROM "LipscoreBrokerRating"
      )
      SELECT
          review->'id' as "id",
          review->'createdAt' as "createdAt",
          review->'lang' as "lang",
          review->'rating' as "rating",
          review->'reviewerShortName' as "reviewerShortName",
          review->'text' as "text",
          review->'recommendation' as "recommendation",
          review->'agentSatisfaction' as "agentSatisfaction"
        FROM "reviews"
      ;
    `;

    const reviews = await sequelize.query(query, { type: QueryTypes.SELECT });

    if (!reviews || isEmpty(reviews)) {
      return [];
    }

    return reviews as LipscoreBrokerRatingReview[];
  },

  getWhitelistedReviews: async () => {
    if (isEmpty(config.lipscore.brokerRating.whitelistedIds)) {
      return [];
    }

    const whitelistedIds = config.lipscore.brokerRating.whitelistedIds
      .split(',')
      .map((id) => `'${id}'`)
      .join(',');

    const query = `
      WITH "reviews" AS (
        SELECT
            jsonb_array_elements("reviews") AS "review"
          FROM "LipscoreBrokerRating"
      )
      SELECT
          review->'id' as "id",
          review->'createdAt' as "createdAt",
          review->'lang' as "lang",
          review->'rating' as "rating",
          review->'reviewerShortName' as "reviewerShortName",
          review->'text' as "text",
          review->'recommendation' as "recommendation",
          review->'agentSatisfaction' as "agentSatisfaction"
        FROM "reviews"
        WHERE review->'rating' = '5'
        AND review->'id' IN (${whitelistedIds})
        ;
    `;

    const reviews = await sequelize.query(query, { type: QueryTypes.SELECT });

    if (!reviews || isEmpty(reviews)) {
      return [];
    }

    return reviews as LipscoreBrokerRatingReview[];
  },
});
