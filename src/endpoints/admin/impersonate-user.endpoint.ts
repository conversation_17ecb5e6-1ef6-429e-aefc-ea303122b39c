import type { Endpoint } from '../../framework/endpoint/endpoint';
import { EndpointMethod } from '../../framework/endpoint/endpoint';
import type { Request, Response } from '../../framework/endpoint/request-response';
import type { ImpersonateUserUseCase } from '../../use-cases/admin/impersonate-user.use-case';
import type { ApikeyAuthenticatedUseCase } from '../../use-cases/apikey-authenticated.use-case';

type ImpersonateUserRequest = Request<
  { userId: string },
  Record<string, never>,
  Record<string, never>,
  Record<string, string>
>;

type ImpersonateUserResponse = Response<{ token: string }, Record<string, never>>;
type ImpersonateUserEndpoint = Endpoint<ImpersonateUserRequest, ImpersonateUserResponse>;

type ImpersonateUserEndpointFactory = (params: {
  useCase: ApikeyAuthenticatedUseCase<ImpersonateUserUseCase>;
  rateLimit: { max: number; timeWindow: number };
}) => ImpersonateUserEndpoint;

export const impersonateUserEndpointFactory: ImpersonateUserEndpointFactory = ({ useCase, rateLimit }) => ({
  method: EndpointMethod.POST,
  route: '/admin/users/impersonate',
  schema: {
    summary: 'Generate impersonation token for user',
    description: 'Generate a JWT token to impersonate a specific user',
    tags: ['Admin'],
    headers: {
      type: 'object',
      properties: {
        'x-api-key': { type: 'string' },
      },
      required: ['x-api-key'],
    },
    body: {
      type: 'object',
      properties: {
        userId: { type: 'string', format: 'uuid' },
      },
      required: ['userId'],
    },
    response: {
      200: {
        type: 'object',
        properties: {
          token: { type: 'string' },
        },
      },
      401: {
        type: 'object',
        properties: {
          error: { type: 'string' },
        },
      },
      404: {
        type: 'object',
        properties: {
          error: { type: 'string' },
        },
      },
    },
  },
  handler: async (request) => {
    const { userId } = request.body;
    const clientIp = request.headers['cf-connecting-ip'] || request.headers['x-forwarded-for'] || 'unknown';

    console.log(`[ADMIN IMPERSONATE] Admin impersonating user ${userId} from IP: ${clientIp}`);

    const result = await useCase({
      apiKey: request.headers['x-api-key'],
      userId,
    });

    return {
      status: 200,
      response: { token: result },
      headers: {},
    };
  },
  rateLimit,
});
