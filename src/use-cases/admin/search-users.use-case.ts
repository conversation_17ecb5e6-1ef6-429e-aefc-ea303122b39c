import type { UserRepository } from '../../domain/user/user.repository';
import type { AsyncUseCase } from '../../framework/use-case/async.use-case';
import type { WithApiKey } from '../apikey-authenticated.use-case';

export type Input = WithApiKey<{
  query: string;
  limit: number;
}>;

type UserSearchResult = {
  id: string;
  email: string;
  name: string;
  phoneNumber: string;
  passwordCode: string;
};

type Output = UserSearchResult[];

export type SearchUsersUseCase = AsyncUseCase<Input, Output>;

export const searchUsersUseCaseFactory = (userRepository: UserRepository): SearchUsersUseCase => async (
  input: Input,
): Promise<Output> => {
  // Note: apiKey validation is handled by the apikeyAuth wrapper
  const { query, limit } = input;
  const users = await userRepository.searchUsers(query, limit);

  return users.map(user => ({
    id: user.id,
    email: user.email,
    name: user.name,
    phoneNumber: user.phoneNumber,
    passwordCode: user.passwordCode,
  }));
};
