import { User<PERSON><PERSON> } from '../../domain/identity/identity';
import type { JWTService } from '../../domain/token/jwt-token.service';
import type { UserRepository } from '../../domain/user/user.repository';
import { ResourceNotFound } from '../../framework/errors/resource.errors';
import type { AsyncUseCase } from '../../framework/use-case/async.use-case';
import type { WithApiKey } from '../apikey-authenticated.use-case';

export type Input = WithApiKey<{
  userId: string;
}>;

type Output = string;

export type ImpersonateUserUseCase = AsyncUseCase<Input, Output>;

export const impersonateUserUseCaseFactory = (
  userRepository: UserRepository,
  tokenService: JWTService,
): ImpersonateUserUseCase => async (input: Input): Promise<Output> => {
  // Note: apiKey validation is handled by the apikeyAuth wrapper
  const { userId } = input;
  const user = await userRepository.getUserByID(userId);

  if (!user) {
    throw new ResourceNotFound('User not found');
  }

  const token = tokenService.sign(
    {
      userID: user.id,
      role: UserRole.USER,
      phoneNumber: user.phoneNumber,
    },
    user.passwordCode,
  );

  return token;
};
